import {defineConfig, loadEnv} from 'vite'
import path from "path"
import vue from '@vitejs/plugin-vue'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import {ElementPlusResolver} from 'unplugin-vue-components/resolvers'
import Inspect from 'vite-plugin-inspect'

const Timestamp = new Date().getTime();
const getViteEnv = (mode, target) => {
    return loadEnv(mode, process.cwd(), "")[target]; // eslint-disable-line
};
const getRewriteReg = (baseApi) => new RegExp('^' + baseApi);

export default defineConfig(({mode}) => {
    const env = loadEnv(mode, process.cwd(), '')
    const baseSecMenu = env.VITE_PROXY_SECMENU ? `/${env.VITE_PROXY_SECMENU}/` : ''
    return {
        base: '',
        publicDir: "src/assets",
        plugins: [
            vue(
                // 配置微应用标签
                {
                    template: {
                        compilerOptions: {
                            isCustomElement: tag => /^micro-app/.test(tag)
                        }
                    }
                }
            ),
            AutoImport({
                imports: ['vue', 'vue-router'],
                // eslintrc: {
                //   // 已存在文件设置默认 false，需要更新时再打开，防止每次更新都重新生成
                //   enabled: false,
                //   // 生成文件地址和名称
                //   filepath: './.eslintrc-auto-import.mjs',
                // },
                resolvers: [ElementPlusResolver()]
            }),
            Components({
                resolvers: [ElementPlusResolver()]
            }),
            // Inspect()
        ],
        resolve: {
            alias: {
                '@': path.resolve(__dirname, 'src')
            },
            // 支持不带后缀名引用.vue文件，虽然支持但还是建议引用的时候带上.vue
            extensions: [".mjs", ".js", ".ts", ".jsx", ".tsx", ".json", ".vue"],
        },
        build: {
            /* 其他build生产打包配置省略 */
            //...
            target: 'es2022',
            commonjsOptions: {
                include: /node_modules|lib/, //这里记得把lib目录加进来，否则生产打包会报错！！
            },
            // rollup 配置
            rollupOptions: {
                output: {
                    chunkFileNames: `js/[name]-${Timestamp}.js`, // 打包后的文件名
                    entryFileNames: `js/[name]-${Timestamp}.js`, // 打包后的入口文件
                    assetFileNames: `[ext]/[name]-${Timestamp}.[ext]`, // 打包后静态资源文件名
                    manualChunks(id) {
                        if (id.includes('node_modules')) {
                            return id
                                .toString()
                                .split('node_modules/')[1]
                                .split('/')[0]
                                .toString();
                        }
                    },
                },
            },
            sourcemap: false, //生产时关闭sourcemap，减少打包体积，不影响调试
            reportCompressedSize: false, // 生产环境不生成压缩大小报告
        },
        optimizeDeps: {
            include: [
                'sm-crypto',
                'jsrsasign',
                'qrcodejs2-fix',
                'file-saver'
            ],
            esbuildOptions: {
                target: 'es2022'
            }
        },
        server: {
            host: true,
            open: true,
            proxy: {
                [`${getViteEnv(mode, 'VITE_APP_BASE_API')}/careader/token`]: {
                    target: `${env.VITE_APP_RQUEST_API}`,
                    changeOrigin: true,
                    rewrite: path => path.replace(getRewriteReg(getViteEnv(mode, 'VITE_APP_BASE_API')), '')
                },
                [`${getViteEnv(mode, 'VITE_APP_BASE_API')}/careader/zz`]: {
                    target: `${env.VITE_APP_RQUEST_API}`,
                    changeOrigin: true,
                    rewrite: path => path.replace(getRewriteReg(getViteEnv(mode, 'VITE_APP_BASE_API')), '')
                },
                [`${getViteEnv(mode, 'VITE_APP_BASE_API')}/careader`]: {
                    target: 'http://***********:8394/api',
                    changeOrigin: true,
                    rewrite: path => path.replace(getRewriteReg(`${getViteEnv(mode, 'VITE_APP_BASE_API')}/careader`), '')
                },
                [`${getViteEnv(mode, 'VITE_APP_BASE_API')}/liaoyitong`]: {
                    target: 'http://lyt.lnwlzb.com/EpMCertService',
                    changeOrigin: true,
                    rewrite: path => path.replace(getRewriteReg(`${getViteEnv(mode, 'VITE_APP_BASE_API')}/liaoyitong`), '')
                },
                [`${getViteEnv(mode, 'VITE_APP_BASE_API')}/face/smrz`]: {
                    target: 'http://smrz.lnwlzb.com/api/smrz',
                    changeOrigin: true,
                    rewrite: path => path.replace(getRewriteReg(`${getViteEnv(mode, 'VITE_APP_BASE_API')}/face`), '')
                },
                [`${getViteEnv(mode, 'VITE_APP_BASE_API')}/face/expertids`]: {
                    target: `${env.VITE_APP_RQUEST_API}`,
                    changeOrigin: true,
                    rewrite: path => path.replace(getRewriteReg(getViteEnv(mode, 'VITE_APP_BASE_API')), '')
                },
                [`${getViteEnv(mode, 'VITE_APP_BASE_API')}/face`]: {
                    target: 'https://lnwlzj.capass.cn/ca/cloudSignatureExpireStatus',
                    changeOrigin: true,
                    rewrite: path => path.replace(getRewriteReg(`${getViteEnv(mode, 'VITE_APP_BASE_API')}/face`), '')
                },
                [`${getViteEnv(mode, 'VITE_APP_BASE_API')}pdf/server`]: {
                    target: `${env.VITE_APP_RQUEST_API}`,
                    changeOrigin: true,
                    rewrite: path => path.replace(getRewriteReg(getViteEnv(mode, 'VITE_APP_BASE_API')), '')
                },
                '/file/pdf': {
                    target: 'http://127.0.0.1:8888/files',
                    changeOrigin: true,
                    rewrite: path => path.replace(/^\/file\/pdf/, '')
                },
                [`${env.VITE_APP_BASE_API}`]: {
                    target: `${env.VITE_APP_RQUEST_API}`,
                    changeOrigin: true,
                    rewrite: path => path.replace(getRewriteReg(getViteEnv(mode, 'VITE_APP_BASE_API')), '')
                }
            }
        }
    }
})
