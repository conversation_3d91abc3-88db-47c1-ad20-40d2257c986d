{"name": "jsgc-fe", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --base=/mg-admin/", "instal": "npm install --legacy-peer-deps", "tsc": "vue-tsc -b", "lint:fix": "eslint src --fix", "lint": "eslint src --ignore-pattern .gitignore --fix", "build": "vite build", "build:stage": "vite build --mode staging", "build:normal": "vite build", "preview": "vite preview", "lint-staged": "lint-staged", "lint:staged": "eslint", "prepare": "husky"}, "lint-staged": {"**/*.{js,mjs,cjs,jsx,ts,mts,cts,tsx,vue,astro,svelte}": "npm run lint:staged"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@element-plus/icons-vue": "^2.3.1", "@jy/ca-reader": "^2.0.9", "@jy/pdf-viewer": "^2.1.0", "@micro-zoe/micro-app": "^1.0.0-rc.13", "@wl-fe/http": "^0.0.5", "ant-design-vue": "^4.2.3", "dayjs": "^1.11.11", "element-plus": "^2.8.7", "js-cookie": "^3.0.5", "pinia": "^2.1.7", "sass": "^1.79.3", "vue": "^3.4.29", "vue-router": "^4.4.0", "zustand-vue": "1.0.0-beta.21"}, "devDependencies": {"@eslint/js": "^9.5.0", "@types/node": "^20.14.9", "@vitejs/plugin-vue": "^5.0.5", "eslint": "9.x", "eslint-plugin-vue": "^9.26.0", "globals": "^15.6.0", "html2canvas": "^1.4.1", "husky": "^9.0.11", "less": "^4.2.0", "lint-staged": "^15.2.7", "typescript": "^5.2.2", "typescript-eslint": "^7.14.1", "unplugin-auto-import": "^0.17.6", "unplugin-vue-components": "^0.27.2", "vite": "^5.3.1", "vite-plugin-inspect": "^0.8.4", "vue-tsc": "^2.0.21"}}