import { create } from 'zustand-vue'
interface globalState {
    token: string
    permissions: string[],
    roles: string[],
    sysUserModules: string[],
    sysUserModulesCopy: string[],
    user: Partial<{
        nickName: string
        userName: string
    }>,
    setToken: (token: globalState['token']) => void,
    setPermissions: (permissions: globalState['permissions']) => void,
    setRoles: (roles: globalState['roles']) => void,
    setUser: (user: globalState['user']) => void,
    clear:()=>void
}

const useGloBalStore = create<globalState>((set) => ({
    token: '',
    permissions: [],
    roles: [],
    sysUserModules:[],
    sysUserModulesCopy:[],
    user: {},
    setToken: (value) => {
        set(() => ({
            token: value
        }))
    },
    setUser: (value) => {
        set(() => ({
            user: value
        }))
    },
    setPermissions: (value) => {
        set(() => ({
            permissions: value
        }))
    },
    setRoles: (value) => {
        set(() => ({
            roles: value
        }))
    },
    setSysUserModules: (value) => {
        set(() => ({
            sysUserModules: value,
            sysUserModulesCopy:JSON.parse(JSON.stringify(value) ) 
        }))
    },
    clear:()=>{
        set(()=>({
            token:'',
            permissions:[],
            roles:[],
            sysUserModules:[],
            user:{}
        }))
    }
}))

export default useGloBalStore