


const useCacheList = (defaultList: any, cacheKey: string) => {
    const list = ref(defaultList)
    const changeList = (newList: []) => {

        list.value = newList
        localStorage.setItem(cacheKey, JSON.stringify(newList))
    }
    onMounted(() => {
        const cacheList = localStorage.getItem(cacheKey)
        console.log(cacheList)
        // if (cacheList) {
        //     const cache = JSON.parse(cacheList)
        //     list.value = (cache.map((item: any) => {
        //         return typeof item !== 'object' ? item : {
        //             ...item,
        //             isCache: true
        //         }
        //     }))
        // } else {
            localStorage.setItem(cacheKey, JSON.stringify(list.value))
        // }

    })
    onUnmounted(() => {
        if (list.value.length > 0) {
            localStorage.setItem(cacheKey, JSON.stringify(list.value))
        }
    })
    return [list, changeList]
}

export default useCacheList
