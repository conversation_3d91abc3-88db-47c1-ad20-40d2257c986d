
function useIsInViewport(ref: any) {
    const isIntersecting = ref(false)
    const observer = new IntersectionObserver(([entry]) =>
        isIntersecting.value = (entry.isIntersecting),
    )

    onBeforeMount(() => {
        if (ref.value) {
            observer.observe(ref.value)

        }
    })
    onBeforeUnmount(()=>{
        observer.disconnect();
    })

    return isIntersecting;
}
export default useIsInViewport