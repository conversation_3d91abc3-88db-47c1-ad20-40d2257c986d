<template>
  <div class="header-card">
    <div
      class="card-type"
      v-for="item in cardList"
      :key="item.text"
      :class="item.bgClass"
      @click="() => emit('changeType', item.type)"
    >
      <div class="card-text">{{ item.text }}</div>
      <div class="card-value">{{ statsData[item.value] }}</div>
    </div>
  </div>
</template>

<script setup>
import { reactive, defineProps } from "vue";
const emit =  defineEmits(["changeType"]);
let props = defineProps({
  statsData: {
    type: Object,
    default: {},
  },
});
let cardList = reactive([
  {
    text: "全部",
    value: "all",
    bgClass: "bg-blue",
    type: "",
  },
  {
    text: "待核验",
    value: "wait",
    bgClass: "bg-purple",
    type: "2",
  },
  {
    text: "锁定中",
    value: "pass",
    bgClass: "bg-green",
    type: "1",
  },
  {
    text: "已解锁",
    value: "noPass",
    bgClass: "bg-origan",
    type: "0",
  },
]);
</script>

<style lang="less" scoped>
.header-card {
  width: 100%;
  display: flex;
}
.card-type {
  width: 206px;
  height: 65px;
  margin-right: 30px;
  color: #fff;
  font-size: 20px;
  font-weight: bold;
  position: relative;
  cursor: pointer;
  .card-text {
    position: absolute;
    top: 8px;
    left: 47px;
  }
  .card-value {
    position: absolute;
    top: 38px;
    left: 47px;
  }
}
.bg-blue {
  background: url("/projectManagement/status1.png") no-repeat;
  background-size: 100% 100%;
}
.bg-purple {
  background: url("/projectManagement/status2.png") no-repeat;
  background-size: 100% 100%;
}
.bg-green {
  background: url("/projectManagement/status3.png") no-repeat;
  background-size: 100% 100%;
}
.bg-origan {
  background: url("/projectManagement/status4.png") no-repeat;
  background-size: 100% 100%;
}
</style>
