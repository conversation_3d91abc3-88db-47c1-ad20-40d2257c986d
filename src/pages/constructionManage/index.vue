<template>
    <PageWrapper>
        <div class="project-list" v-loading="isLoading">
            <div class="project-search-box">
                <searchButton
                        style="width: auto;margin-right: 10px"
                        @search="handleSearch"
                        :placeholder="'请输入项目名称或项目识别码'"
                >
                </searchButton>
                <HeaderCard :statsData="statsData" @changeType="changeType"></HeaderCard>
            </div>
            <div class="project-content">
                <div class="project-city" v-if="isShowList">
                    <City
                            :selectCityId="searchInfo.regionCode"
                            @selectCity="handleSelectCity"
                            :city-list="cityLists"
                    ></City>
                </div>
                <div class="project-list-box">
                    <div class="project-list-conotent conotentTopNone">
                        <el-table
                                :data="tableData"
                                style="width: 100%"
                                :height="650"
                                :row-class-name="tableRowClassName"
                                class="my-table"
                        >
                            <el-table-column
                                    label="序号"
                                    type="index"
                                    :index="indexMethod"
                                    align="center"
                                    width="80px"
                            />
                            <el-table-column
                                    v-for="item in tableColData"
                                    :key="item.prop"
                                    align="center"
                                    :prop="item.prop"
                                    :label="item.label"
                                    :min-width="item.width"
                                    :show-overflow-tooltip="
                  ['tenderProjectName'].includes(item.prop)
                    ? false
                    : tooltipOptions
                "
                            >
                                <template #default="{ row }">
                                    <div v-if="['approvalStatus'].includes(item.prop)">

                                    </div>
                                    <div v-if="['lockStatusStr'].includes(item.prop)">
                    <span
                            :style="{ color: getAuditStatusColorByLock(row[item.prop]) }"
                    >{{ row[item.prop] ? row[item.prop] : "--" }}</span
                    >
                                    </div>
                                    <div v-if="['hasSupervision'].includes(item.prop)">
                                        <img
                                                v-if="row[item.prop] !== 0"
                                                class="my-img my-img-yj"
                                                :src="item.redImg"
                                                alt=""
                                        />
                                        <span v-else>--</span>
                                    </div>
                                    <span
                                            v-if="
                      ![
                        'approvalStatus',
                        'lockStatusStr',
                        'hasSupervision',
                        'tenderProjectName',
                      ].includes(item.prop)
                    "
                                    >{{ row[item.prop] ? row[item.prop] : "--" }}</span
                                    >
                                </template>
                            </el-table-column>
                            <el-table-column align="center" label="操作" width="220px">
                                <template #default="{ row }">
                                    <el-button
                                            type="primary"
                                            class="my-detail-btn"
                                            plain
                                            size="small"
                                            round
                                            @click="handleViewDetail(row)"
                                    >查看详情
                                    </el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                        <div class="pagination-box">
                            <el-pagination
                                    class="pagination"
                                    background
                                    v-model:current-page="searchInfo.pageNum"
                                    v-model:page-size="searchInfo.pageSize"
                                    :total="total"
                                    :size="searchInfo.pageSize"
                                    :page-sizes="[100, 200, 300, 400]"
                                    layout="total, sizes, prev, pager, next, jumper"
                                    @size-change="handleSizeChange"
                                    @current-change="handleChangePage"
                            />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </PageWrapper>
</template>

<script setup>
import City from "@/pages/project/projectList/cityList/index.vue";
import {httpGet, httpPost} from "@wl-fe/http/dist";
import {constructionCols} from "./content.js";
import AccountManagementFilter from "@/filters/index.js";
import {getOptionList} from "@/pages/project/projectBadBehavior/formOptionList";
import useGloBalStore from "@/store/useGlobalStore"
import HeaderCard from "./headerCard/index.vue";
import {ref} from "vue";

const {user} = useGloBalStore();
// 定义 tooltipOptions 对象
const tooltipOptions = ref({
    effect: "light", // 提示框主题为亮色
    placement: "top", // 提示框显示在单元格上方
    showArrow: true, // 显示提示框的箭头
    hideAfter: 200, // 提示框隐藏的延迟时间为 200 毫秒
    popperOptions: {
        strategy: "fixed", // 使用 fixed 定位策略
    },
});
const {getAuditStatusColorByLock} = AccountManagementFilter();
//console.log("getInsStatusColor", getInsStatusColor, AccountManagementFilter);

const router = useRouter();
const indexMethod = (index) => {
    return index + 1;
};
const handleSearch = (val) => {
    searchInfo.keyword = val;
    handleGetList();
};
const isLoading = ref(false);
let total = ref(1);
const parentNodeCode = 'inProcessSupervisionData'
const nodeCode = 'WINING_ADVICE'
const tableColData = ref(constructionCols);
let searchInfo = reactive({
    keyword: "",
    regionCode: "",
    pageNum: 1,
    pageSize: 10,
    nodeCode,
    approvalStatus: "",
    platformCode: undefined,
    currentNodeCode: undefined,
});
const handleSelectCity = (item) => {
    searchInfo.regionCode = item.code;
    handleGetList();
};
const tableRowClassName = ({rowIndex}) => {
    if (rowIndex % 2 !== 0) {
        return "warning-row";
    }
    return "";
};

const handleViewDetail = async (row) => {
    router.push({
        path: "/constructionManage/constructionDetail",
        query: {
            nodeCode: row.nodeCode,
            bidSectionGuid: row.bidSectionGuid,
            rowGuid: row.rowGuid,
            projectGuid: row.projectGuid,
            parentNodeCode: parentNodeCode,
            tolerance: row.tolerance,
            isShowLeft: nodeCode == "AS_AGENCY_LOCKOUT" ? false : true,
        },
    });
};
const changeType = (val) => {
    searchInfo.approvalStatus = val;
    handleGetList();
};
let statsData = ref([]);
let cityLists = ref([]);
const isShowList = computed(() => {
    return cityLists.value.length > 0;
});
const handleChangePage = (val) => {
    console.log(val);
    searchInfo.pageNum = val;
    handleGetList();
};
const handleSizeChange = (val) => {
    console.log(val);
    searchInfo.pageNum = val;
    handleGetList();
};
const platformOption = ref([]);
const pressOption = ref([]);
onMounted(async () => {
    if (nodeCode != "BAD_BEHAVIOR") {
        const list = await httpGet("/api/sysRegion/list", {});
        cityLists.value = list;
        searchInfo.regionCode = user.regionCode ? user.regionCode : "";
    }
    handleGetList();
    platformOption.value = await getOptionList("jyptbm");
    pressOption.value = await getOptionList("tenderProcessNode");
});
let tableData = reactive([]);
const handleGetList = async () => {
    isLoading.value = true;
    let data = await httpPost(`/cWinBidderLock/list?pageNum=${searchInfo.pageNum}&pageSize=${searchInfo.pageSize}`,
        searchInfo
    );
    statsData.value = await httpPost("/cWinBidderLock/getStatsData", searchInfo);
    isLoading.value = false;
    total.value = data.total;
    tableData = data.rows;
};
</script>
<style scoped lang="less">
.project-list {
  width: 100%;
  height: 100%;

  .project-search-box {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;

    .select {
      width: 200px;
      margin-right: 16px;
      border-radius: 50px;

      :deep(.el-select__wrapper) {
        border-radius: 50px;
      }
    }

    .back-btn {
      width: 90px;
    }

    .success-btn {
      width: 150px;
    }
  }

  .project-content {
    width: 100%;
    height: 93%;
    margin-top: 1%;
    display: flex;
    justify-content: space-between;

    .project-city {
      width: 8%;
      height: 100%;
      margin-right: 10px;
      overflow: auto;
      flex-shrink: 0;
    }

    .project-list-box {
      height: 100%;
      flex: 1;
      width: 0;
      display: flex;
      flex-direction: column;

      .radio-box {
        text-align: right;
      }

      .project-list-conotent {
        width: 100%;
        // height: 82%;
        margin-top: 1%;
        flex: 1;
        height: 0;

        .pagination-box {
          margin-top: 20px;
          display: flex;
          flex-direction: row;
          justify-content: flex-end;
        }
      }

      .conotentTopNone {
        margin-top: 0;
      }
    }
  }
}

.table-view {
  width: 20px;
  height: 20px;
  border-radius: 20px;
  border: 1px solid #3366ff;
  text-align: center;
  line-height: 22px;
  cursor: pointer;
  margin: 0 auto;
  color: #3366ff;
}

.my-img {
  width: 20px;
  margin-right: 5px;
}

.my-img-yj {
  width: 30px;
}

.status-red {
  color: red;
}

.status-green {
  color: green;
}

.status-orign {
  color: #f4871c;
}

/* 设置滚动条的样式 */
.project-city-box::-webkit-scrollbar {
  width: 6px;
  box-sizing: border-box;
}

/* 滚动条滑块 */
.project-city-box::-webkit-scrollbar-thumb {
  height: 5px;
  border-radius: 5px;
  background-color: #c2cede;
}

/*滚动槽*/
.project-city-box::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0);
  border-radius: 10px;
  background: #ffffff;
}

/* 滚动条滑块 鼠标悬浮 */
.project-city-box::-webkit-scrollbar-thumb:hover {
  background-color: #c2cede;
  // border: 1px solid rgba(0, 0, 0, 0.21);
  cursor: pointer;
}

.my-table {
  :deep(.el-table__header-wrapper) {
    .el-table__cell {
      background: #f0f6ff;
      height: 54px;
      color: rgba(0, 0, 0, 0.88);
    }
  }

  :deep(.el-table__row) {
    height: 54px;
  }

  :deep(.warning-row) {
    background: #f9fbff;
  }
}
</style>
