<!-- eslint-disable no-undef -->
<template>
  <div id="perject-detail" v-loading="isLoading">
    <div class="project-header">
      <div class="header-box">
        <div class="header-text-s unify-title5" v-if="dataInfo">
          <span class="text-t">{{
            dataInfo.tenderProjectName ? "招标项目名称" : "标段名称"
          }}</span>
          <el-popover placement="bottom" trigger="hover">
            <template #reference>
              <span class="text-content">{{
                dataInfo.tenderProjectName
                  ? dataInfo.tenderProjectName
                  : dataInfo.bidSectionName
              }}</span>
            </template>
            <div style="text-align: center">
              {{
                dataInfo.tenderProjectName
                  ? dataInfo.tenderProjectName
                  : dataInfo.bidSectionName
              }}
            </div>
          </el-popover>
        </div>
        <div
          class="header-text-s unify-title5"
          style="margin-left: 10%"
          v-if="dataInfo"
        >
          <span class="text-t">{{
            dataInfo.tenderProjectCode ? "招标项目编号" : "标段编号"
          }}</span>
          <span class="text-content" style="width: 316px">{{
            dataInfo.tenderProjectCode
              ? dataInfo.tenderProjectCode
              : dataInfo.bidSectionCode
          }}</span>
        </div>
      </div>
      <el-button @click="handleBack" class="back-btn" type="primary" plain round
        >返回
      </el-button>
    </div>
    <div class="project-content">
      <div class="project-left">
        <div class="left-top">
          <div class="left-header header-title unify-title7">流 程</div>
          <div class="project-stop">
            <div
              class="stop-item"
              v-for="(item, index) in processList"
              :key="index"
            >
              <div
                class="stop-text"
                @click="handleSelectModule(item.key)"
                :class="{ 'stop-text-avtive': item.key == selectKey }"
              >
                <span class="h-top-bg"> {{ index + 1 }} </span>
                {{ item.name }}
              </div>
              <div
                class="step-img"
                v-if="processList.length - 1 != index"
              ></div>
            </div>
          </div>
        </div>
        <div class="left-bottom">
          <div class="left-bottom-header header-title unify-title7">
            核验记录
          </div>
          <div class="bottom-list-audit">
            <el-timeline>
              <el-timeline-item
                v-for="(activity, index) in activities"
                :key="index"
                :color="activity.color"
                :size="activity.size"
              >
                <div class="no-status">
                  <div
                    class="audit-status"
                    :style="{ color: getAuditStatusColorDetail(activity.content) }"
                  >
                    {{ activity.content }}
                  </div>
                  <div class="audit-companyName" style="margin-left: 7px">
                    {{ activity.companyName }}
                  </div>
                  <div class="audit-time" style="margin-left: 7px">
                    {{ activity.timestamp }}
                  </div>
                  <div
                    class="audit-time"
                    style="margin-left: 7px; color: red"
                    v-if="activity.content == '【 审核不通过 】'"
                  >
                    原因：{{ activity.handingSuggestion }}
                  </div>
                </div>
              </el-timeline-item>
            </el-timeline>
          </div>
        </div>
      </div>
      <div class="project-center">
        <div class="project-audit" ref="mianscroll">
          <div class="center-text" v-if="isShowPdf">
            <div class="iframe-box">
              <div
                class="company-list"
                v-if="isShowBtn"
                :class="!isShowList ? 'company-list-sh' : ''"
              >
                <div class="com-btn" @click="isShowList = !isShowList">
                  {{ isShowList ? "收起" : "展开" }}
                </div>
                <div
                  class="company-name"
                  :class="{ companyNameS: item.rowGuid == fileRowGuid }"
                  @click="hansleModelSign(item)"
                  v-for="(item, index) in moduleList"
                  :key="index"
                >
                  <div class="qz-sign" v-if="item.signatureStatus === 1"></div>
                  {{ item.attachmentFileName }}
                </div>
              </div>
              <iframe
                id="iframe"
                ref="iframe"
                width="100%"
                height="100%"
                frameborder="0"
                :src="iframeSrc"
              ></iframe>
            </div>
          </div>
          <div class="project-detail-info" v-else>
            <detailModule
              v-for="(val, key, index) in selectModule"
              :key="index"
              :headerTitle="key"
              :moduleInfo="val"
            ></detailModule>
          </div>
        </div>
      </div>
      <div class="project-right" v-if="fileDateList.length != 0">
        <div class="right-header header-title unify-title7">附 件</div>
        <div class="file-content-box">
          <div
            class="file-list"
            v-for="(item, key, index) in fileDateList"
            :key="index"
          >
            <div class="file-header">{{ key }}</div>
            <div
              class="file-content"
              v-for="(val, index1) in fileDateList[key]"
              :key="index1"
            >
              <div class="content-header" :title="val.attachmentName">
                {{ val.attachmentName }}
              </div>
              <div class="file-src" @click="handleOpenPdf(val)">
                <span class="pdf-img"></span> {{ val.attachmentFileName }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="project-footer">
      <el-button
        v-if="isShowSign"
        :disabled="pdfQzUrl"
        :type="pdfQzUrl ? 'info' : 'primary'"
        class="btn-footer"
        round
        @click="handleSign()"
        >{{ pdfQzUrl ? "已签章" : "签 章" }}
      </el-button>
      <el-button
        v-if="isShowAudit"
        type="primary"
        class="btn-footer"
        color="#8bc34a"
        style="color: #fff"
        round
        @click="handlePass"
        >核验通过
      </el-button>
      <el-button
        v-if="isShowAudit"
        type="primary"
        class="btn-footer"
        color="red"
        style="color: #fff"
        round
        @click="handleNoPass"
        >核验不通过
      </el-button>
      <el-button
        @click="handleBack"
        class="btn-footer"
        type="primary"
        plain
        round
        >关 闭
      </el-button>
    </div>
  </div>
  <el-dialog v-model="dialogFormVisible" width="700px" title="核验意见">
    <el-form ref="ruleFormRef" :model="formData" :rules="rules">
      <el-form-item label="意见" prop="reason">
        <el-input
          v-model="formData.reason"
          :rows="6"
          placeholder="请输入意见！"
          type="textarea"
          autocomplete="off"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button round @click="dialogFormVisible = false">取消</el-button>
        <el-button round type="primary" @click="handleConNoPass(ruleFormRef)"
          >确 认</el-button
        >
      </span>
    </template>
  </el-dialog>
  <newQz @signedSuccess="signedSuccess" ref="newQzEl"></newQz>
</template>
<script setup>
import AccountManagementFilter from "@/filters/index.js";
import { useRouter, useRoute } from "vue-router";
import { httpPost } from "@wl-fe/http/dist";

const { getAuditStatusColorDetail } = AccountManagementFilter();
const router = useRouter();
const route = useRoute();
let isLoading = ref(false);
const handleBack = () => {
  router.push({
    path: "/constructionManage",
    query: {
      nodeCode: detailInfo.nodeCode,
      parentNodeCode: parentNodeCode,
    },
  });
};
const isShowList = ref(false);
const isShowBtn = ref(false);
const dialogFormVisible = ref(false);
const ruleFormRef = ref();
const formData = reactive({
  reason: "",
});
const rules = reactive({
  reason: [{ required: true, message: "请先输入意见！", trigger: "blur" }],
});
const handlePass = () => {
  ElMessageBox.alert("确认核验通过？", "", {
    confirmButtonText: "确认",
    callback: (action) => {
      if (action == "confirm") {
        let info = {
          status: 2,
          cruxGuid: detailInfo.rowGuid,
          handingSuggestion: "核验通过",
          node: detailInfo.nodeCode,
          bidSectionGuid: detailInfo.bidSectionGuid,
        }
        handleVerification(info);
      }
    },
  });
};
const handleNoPass = () => {
  dialogFormVisible.value = true;
};
const handleConNoPass = async (formEL) => {
  if (!formEL) return;
  await formEL.validate((valid, fields) => {
    if (valid) {
      dialogFormVisible.value = false;
      let info = {
        status: 1,
        cruxGuid: detailInfo.rowGuid,
        handingSuggestion: formData.reason,
        node: detailInfo.nodeCode,
        bidSectionGuid: detailInfo.bidSectionGuid,
      };
      handleVerification(info);
    }
  });
};
const handleVerification = async (info) => {
  await httpPost("/processNodeApproval/saveProcessNodeApprovalByLock", info);
  ElMessage({
    message: "核验完成！",
    type: "success",
  });
  //  handleBack()
  handleGetDetail();
  handleGetAuditInfo();
};

let { query } = toRefs(route);
const {
  nodeCode,
  bidSectionGuid,
  rowGuid,
  parentNodeCode,
  projectGuid
} = query.value;

const detailInfo = reactive({
  // 统一详情用的
  nodeCode,
  rowGuid,
  projectGuid,
  bidSectionGuid
});
let dataInfo = ref(null);
let fileDateList = ref([]);
let processObj = {
  WINING_ADVICE: [
    {
      name: "申请人员变更",
      key: "bidWinningNoticeInfo",
    },
    {
      name: "申请人员变更（附件）",
      key: "bidWinningNoticeFileInfo",
    },
  ],
};
let processList = ref(processObj["WINING_ADVICE"]);
const fileUrl = ref("");
let selectKey = ref("");
let selectModule = ref({});
const mianscroll = ref(null);
onMounted(async () => {
  handleGetDetail();
  handleGetAuditInfo();
});
const handleGetDetail = async () => {
  isShowPdf.value = false;
  isLoading.value = true;
  let data = await httpPost(
    "/inviteTender/bidWinningNotice/constructionDetails",
    detailInfo
  );
  isLoading.value = false;
  fileDateList.value = data.fileData;
  dataInfo.value = data;
  console.log('111111111');
  console.log(data.bidWinningNoticeFileInfo['核验附件'],Array.isArray(data.bidWinningNoticeFileInfo['核验附件']));
  if(!Array.isArray(data.bidWinningNoticeFileInfo['核验附件'])){
      processObj = {
          WINING_ADVICE: [
              {
                  name: "锁定人员信息",
                  key: "bidWinningNoticeInfo",
              },
              {
                  name: "申请人员变更（附件）",
                  key: "bidWinningNoticeFileInfo",
              },
          ],
      };
      processList.value = processObj["WINING_ADVICE"];
  }else{
      processObj = {
          WINING_ADVICE: [
              {
                  name: "锁定人员信息",
                  key: "bidWinningNoticeInfo",
              },
          ],
      };
      processList.value = processObj["WINING_ADVICE"];
  }

  handleSelectModule(processList.value[processList.value.length - 1].key);
};
const isShowSign = computed(() => {
  return (
    dataInfo.value?.buttonDataInfo?.signButton == 1 &&
    dataInfo.value?.buttonDataInfo?.approvalButton == 1
  );
});
const isShowAudit = computed(() => {
  return dataInfo.value?.buttonDataInfo?.approvalButton == 1;
});
const activities = ref([]);
const handleGetAuditInfo = async () => {
  let info = {
    cruxGuid: detailInfo.rowGuid,
    bidSectionGuid: detailInfo.bidSectionGuid,
    node: detailInfo.nodeCode,
  };
  let data = await httpPost(
    "/processNodeApproval/getProcessNodeApprovalByGuidByLock",
    info
  );
  let list = data.map((item) => {
    return {
      content: `【 ${item.approvalName} 】`,
      timestamp: item.operateTime,
      size: "large",
      color:
        item.approvalName == "审核通过"
          ? "#0bbd87"
          :item.approvalName == "待审核"
          ? "#FF9933"
          : item.approvalName == "审核不通过"
          ? "#e40d0d"
          : item.approvalName == "流程退回"
          ? "#e40d0d"
          : "#0bbd87",
      companyName: item.operateUser,
      handingSuggestion: item.handingSuggestion,
    };
  });
  activities.value = list;
};
const isShowPdf = ref(false);
const moduleList = ref([]);
const handleSelectModule = (key) => {
  selectKey.value = key;
  selectModule.value = dataInfo.value[key];
  isShowBtn.value = false;
  if (  key == "bidWinningNoticeFileInfo" ) {
    isShowPdf.value = true;
    fileUrl.value = selectModule.value["核验附件"].url;
  } else {
    isShowPdf.value = false;
  }
  bottomScrollClick();
};
const fileRowGuid = ref("");
const hansleModelSign = (item) => {
  fileUrl.value = item.url;
  fileRowGuid.value = item.rowGuid;
};
const bottomScrollClick = async () => {
  await nextTick();
  mianscroll.value.scrollTop = 0;
};
const handleOpenPdf = (val) => {
  window.open(val.url);
};
const newQzEl = ref(null);
const handleSign = () => {
  handleSelectModule(processList.value[processList.value.length - 1].key);
  let info = {
    id: new Date().getTime(), //fileRowGuid.value,
    fileUrl: fileUrl.value,
  };
  newQzEl.value.open(info);
};
const signedSuccess = async (signInfo) => {
  console.log("signInfo", signInfo, JSON.parse(signInfo.outResp));
  newQzEl.value.handleClose();
  try {
    let res = JSON.parse(signInfo.outResp);
    fileUrl.value = res.msg;
    pdfQzUrl.value = res.msg;
    let info = {
      rowGuid: "", // selectModule.value["核验附件"].rowGuid,
      signatureUrl: pdfQzUrl.value,
    };
    if (selectKey.value == "bidWinningNoticeFileInfo") {
      info.rowGuid = selectModule.value["核验附件"].rowGuid;
    }
    await httpPost("api/sysAttach/updateSignatureFile", info);
    ElMessage({
      message: "签章成功！",
      type: "success",
    });
  } catch (error) {
    console.log("errorerror", error);
    ElMessage({
      message: "签章失败！",
      type: "error",
    });
  }
};
const pdfQzUrl = ref(false);

// 添加计算属性来处理 iframe src
const iframeSrc = computed(() => {
    return `${import.meta.env.VITE_PDF_WEB_VIEW}${fileUrl.value}`;
});
</script>
<style scoped lang="less">
#perject-detail {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  font-size: 16px;
  .project-header {
    width: 100%;
    height: 6%;
    background-color: #fff;
    border-radius: 5px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    font-weight: bold;
    font-size: 16px;

    .header-box {
      display: flex;
      flex-direction: row;
      align-items: center;
      width: 80%;

      .header-lan {
        height: 30px;
        width: 8px;
        background: #438bfa;
        border-radius: 5px;
        margin-left: 21px;
      }

      .header-text-s {
        margin-left: 12px;
        font-size: 20px;
        display: flex;

        .text-t {
          margin-right: 10px;
          display: inline-block;
          width: 128px;
        }

        .text-content {
          display: inline-block;
          width: 584px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }

    .back-btn {
      width: 90px;
      margin-right: 20px;
    }
  }

  .project-press {
    width: 100%;
    height: 65px;
    background: #fff;
    border-radius: 5px;
    margin: 5px 0px;

    .simple {
      display: flex;
      height: 45px;
      align-items: center;
      width: 100%;

      .step {
        display: inline-block;
        background: red;
        margin: 0 5px;
        height: 35px;
        line-height: 35px;
        text-align: center;
        width: 100%;
        cursor: pointer;

        .step-title {
          // cursor: pointer;
          font-size: 18px;

          display: block;
        }
      }

      .success {
        background: url("/projectManagement/step2.png") no-repeat center;
        background-size: 100% 100%;
        color: #fff;
        font-weight: bold;
      }

      .success-first {
        background: url("/projectManagement/step1.png") no-repeat center;
        background-size: 100% 100%;
        color: #fff;
        font-weight: bold;
      }

      .success-last {
        background: url("/projectManagement/step3.png") no-repeat center;
        background-size: 100% 100%;
        color: #fff;
        font-weight: bold;
      }

      .finish {
        background: url("/projectManagement/step8.png") no-repeat center;
        background-size: 100% 100%;
      }

      .finish-first {
        background: url("/projectManagement/step7.png") no-repeat center;
        background-size: 100% 100%;
      }

      .finish-last {
        background: url("/projectManagement/step9.png") no-repeat center;
        background-size: 100% 100%;
      }
    }

    .step-span {
      cursor: pointer;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      width: 100%;
      display: inline-block;
      vertical-align: bottom;
    }

    :deep(.el-steps--simple) {
      background: var(--el-color-primary-light-9);
      border-radius: 4px;
      padding: 13px 2%;
    }

    :deep(.el-step__title) {
      line-height: 20px;
    }

    :deep(.el-steps) {
      padding-top: 14px;
    }

    :deep(
        .el-step.is-simple .el-step__arrow:after,
        .el-step.is-simple .el-step__arrow:before
      ) {
      background: var(--el-color-primary-light-3);
      content: "";
      display: inline-block;
      height: 15px;
      position: absolute;
      width: 3px;
    }

    :deep(.el-step.is-simple .el-step__arrow:before) {
      background: var(--el-color-primary-light-3);
      content: "";
      display: inline-block;
      height: 15px;
      position: absolute;
      width: 3px;
    }

    :deep(.is-success .el-icon:before) {
      // background: var(--el-color-primary-light-3);
      content: "";
      // display: inline-block;
      // height: 15px;
      // position: absolute;
      // width: 3px;
      position: absolute;
      top: -11px;
      left: 7px;
      border-left: 5px solid transparent;
      border-right: 5px solid transparent;
      border-top: 10px solid #67c23a;
    }
  }

  .project-content {
    width: 100%;
    height: 79%;
    border-radius: 5px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;

    .project-left {
      width: 15%;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      margin-right: 12px;

      .left-top,
      .left-bottom {
        width: 100%;
        height: 49%;
        background: #fff;
        border-radius: 5px;
        box-sizing: border-box;
        padding: 13px 0px;

        .left-bottom-header {
          font-size: 16px;
          font-weight: bold;
          margin-bottom: 10px;
        }

        .bottom-list-audit {
          width: 100%;
          height: 90%;
          padding-top: 5px;
          overflow: auto;

          :deep(.el-timeline-item__tail) {
            border-left: 2px solid #ccc;
          }

          .no-status {
            cursor: pointer;
          }

          .audit-status {
            color: #3981f8;
          }

          .audit-companyName {
          }

          .audit-time {
          }

          :deep(.el-timeline) {
            margin-left: -19px;
          }
        }

        .left-header {
          font-size: 16px;
          font-weight: bold;
        }

        .project-stop {
          width: 100%;
          margin-top: 15px;

          .stop-item {
            display: flex;
            flex-direction: column;
            align-items: center;

            .stop-text {
              width: 200px;
              height: 30px;
              text-align: center;
              line-height: 28px;
              border: 1px solid #3981f8;
              color: #3981f8;
              border-radius: 5px;
              cursor: pointer;
              background: #ebf2fe;
              border-radius: 20px;
              position: relative;

              .h-top-bg {
                width: 30px;
                height: 20px;
                position: absolute;
                top: -9px;
                left: 20px;
                background: url("/projectManagement/h-tip.png") no-repeat;
                background-size: 100% 100%;
                line-height: 20px;
                text-align: left;
                padding-left: 6px;
                color: #fff;
                font-weight: bold;
              }
            }

            .stop-text-avtive {
              background: #3981f8;
              color: #fff;
            }

            .step-img {
              width: 30px;
              height: 30px;
              background: url("/projectManagement/step-down.png") no-repeat;
              background-size: 100% 100%;
            }
          }
        }
      }

      .left-bottom-height {
        height: 100%;
      }
    }

    .project-center {
      // width: 61%;
      width: 0;
      height: 100%;
      background: #fff;
      border-radius: 5px;
      overflow: hidden;
      flex: 1;

      .project-audit {
        width: 100%;
        height: 99%;
        overflow: auto;
        // .center-header {
        //   width: 100%;
        //   height: 6%;
        //   display: flex;
        //   flex-direction: row;
        //   justify-content: flex-end;
        //   align-items: center;
        // }
        .center-text {
          width: auto;
          height: 100%;
          // border: 1px solid #ccc;
          .iframe-box {
            width: 100%;
            height: 99%;
            position: relative;

            .company-list {
              position: absolute;
              top: 39px;
              left: 0;
              width: 300px;
              height: 95%;
              background: #00000038;
              box-sizing: border-box;
              padding: 10px;
              border-radius: 0px 5px 5px 0px;
              display: flex;
              flex-direction: column;
              justify-content: center;
              transition: all 0.5s;

              .company-name {
                width: 100%;
                height: 30px;
                line-height: 30px;
                text-align: center;
                color: #fff;
                border: 1px solid #fff;
                border-radius: 5px;
                padding: 0 25px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                margin: 5px;
                cursor: pointer;
                position: relative;

                .qz-sign {
                  position: absolute;
                  top: 4px;
                  left: 4px;
                  width: 20px;
                  height: 20px;
                  background: url("/projectManagement/yqz.png") no-repeat;
                  background-size: 100% 100%;
                }
              }

              .companyNameS {
                color: #3366ff;
                border: 1px solid #3366ff;
              }

              .com-btn {
                position: absolute;
                right: -24px;
                top: 43%;
                height: 84px;
                width: 22px;
                background: #00000038;
                color: #fff;
                text-align: center;
                line-height: 41px;
                border-radius: 0 5px 5px 0px;
                cursor: pointer;
              }
            }

            .company-list-sh {
              left: -300px;
            }
          }
        }

        .project-detail-info {
          width: 100%;
          height: 100%;
          box-sizing: border-box;
          padding: 10px 5px;
          position: relative;

          :deep(.my-descriptions) {
            // background: #dceaff;
            font-size: 16px;
            width: 22%;
            color: #000;
            text-align: center;
            background: #f6faff;
          }

          .monitor {
            position: absolute;
            right: 20px;
            top: 21px;
            font-size: 18px;
            cursor: pointer;
            z-index: 10;

            .ionCountImg {
              width: 30px;
              margin-right: 5px;
              vertical-align: bottom;
            }

            .text {
              color: red;
              cursor: pointer;

              span {
                font-size: 25px;
                font-weight: bold;
              }
            }
          }
        }
      }
    }

    .project-right {
      width: 15%;
      height: 100%;
      background: #fff;
      border-radius: 5px;
      box-sizing: border-box;
      padding: 13px 0px;

      margin-left: 12px;

      .right-header {
        font-size: 16px;
        font-weight: bold;
        height: 5%;
      }

      .file-content-box {
        height: 95%;
        overflow: auto;
        padding-bottom: 5px;
        box-sizing: border-box;

        .file-list {
          width: 90%;
          margin: 0 auto;
          overflow: hidden;
          margin-top: 22px;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);

          .file-header {
            width: 100%;
            height: 38%;
            line-height: 38px;
            text-align: center;
            background: #dceaff;
          }

          .file-content {
            width: 100%;
            box-sizing: border-box;
            margin-bottom: 20px;

            .content-header {
              white-space: nowrap;
              text-overflow: ellipsis;
              overflow: hidden;
              height: 28px;
              line-height: 28px;
              padding: 0 10px;
              cursor: pointer;
              background: #f6faff;
              color: #3981f8;

              &:hover {
                .file-src {
                  color: #438bfa;
                }
              }
            }

            .file-src {
              // height: 28px;
              line-height: 36px;
              // background: #f6faff;
              padding: 0 10px;
              cursor: pointer;
              font-size: 14px;
              width: 100%;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;

              &:hover {
                color: #438bfa;
              }

              .pdf-img {
                width: 28px;
                height: 32px;
                display: inline-block;
                vertical-align: sub;
                background: url("/projectManagement/PDF.png") no-repeat;
                background-size: 100% 100%;
                margin-right: 4px;
                vertical-align: middle;
              }
            }

            .file-time {
              text-align: center;
              margin-top: 10px;
            }
          }
        }
      }
    }
  }

  .project-footer {
    width: 100%;
    height: 6%;
    background: #fff;
    border-radius: 5px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    margin: 5px 0px;

    .btn-footer {
      width: 120px;
    }

    .btn-tolerance {
      width: 150px;
    }
  }

  .header-title-bg {
    display: inline-block;
    height: 30px;
    width: 8px;
    background: #2196f3;
    border-radius: 5px;
    vertical-align: middle;
    margin: 0 5px 0 3px;
  }
}

/* 设置滚动条的样式 */
::-webkit-scrollbar {
  width: 6px;
  box-sizing: border-box;
}

/* 滚动条滑块 */
::-webkit-scrollbar-thumb {
  height: 5px;
  border-radius: 5px;
  background-color: #c2cede;
}

/*滚动槽*/
::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0);
  border-radius: 10px;
  background: #ffffff;
}

/* 滚动条滑块 鼠标悬浮 */
::-webkit-scrollbar-thumb:hover {
  background-color: #c2cede;
  // border: 1px solid rgba(0, 0, 0, 0.21);
  cursor: pointer;
}

.header-title {
  height: 32px;
  font-weight: 600;
  line-height: 32px;
  margin-left: 5px;
  padding-left: 5px;
  position: relative;
  color: #000;
  z-index: 0;
}

.unify-title7 {
  &:after {
    content: "";
    position: absolute;
    bottom: 0;
    top: 0;
    left: 0;
    width: 180px;
    height: 100%;
    opacity: 0.3;
    // background: linear-gradient(to right, #2d83fa, #4bf15900);
  }

  &:before {
    content: " ";
    position: absolute;
    bottom: 0;
    left: 0;
    position: absolute;
    height: 2px;
    width: 100%;
    background: linear-gradient(to right, #2d83fa, rgba(255, 255, 255, 0));
  }
}

:deep(.el-drawer__header) {
  display: none;
}

.dialog-footer {
  display: flex;
  justify-content: center;
  margin-top: 13px;
}

.viewPdfbox {
  width: 100%;
  height: 700px;
  display: flex;

  .navBox {
    width: 15%;
    height: 100%;
    border-right: 1px solid #d1eedb;
    margin-right: 5px;
  }

  .pdfBox {
    width: 85%;
    height: 100%;
  }
}
</style>
<style scoped>
#toolbarViewer {
  background: red !important;
}
</style>
