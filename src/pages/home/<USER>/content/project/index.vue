<script setup lang="ts">
import { projectDetailColumns } from "../../constant.ts"
import { isEven } from "@/utils/number"
import LabelTitle from "@/components/labelTitle.vue"
const getItemValue = (item: any, businessJson: any) => {

  if(item && businessJson) {
    return businessJson[item?.dataIndex as string] || ''; // 假设dataIndex是一个存在的键
  }
};
const splitTitle = (title: string) => {
  return title.split('').map((char: string) => `<span>${char}</span>`).join('');
  // 注意：在 Vue 模板中直接渲染 HTML 是不安全的，你可能需要使用 v-html 指令，但请确保内容是安全的
};
const props = defineProps({
  data: {
      type: Object as () => any,
      required: true,
    },
})
</script>
<template>
  <div class="project">
    <LabelTitle title="项目详细信息" />
    <div class="list">
      <div v-for="(item, index) in projectDetailColumns" :key="index" :class="['item', isEven(index) ? 'even' : '']">
        <div class="label">
          {{ (item.title) }}
        </div>
        <div class="value">
          {{ getItemValue(item, data.businessJson) }}
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.project {
  .list {
    margin-top: 24px;
    margin-left: 24px;

    .item {
      height: 40px;
      display: flex;
      padding: 0px 12px;
      align-items: center;

      &.even {
        background-color: #f0f0f0;
      }

      .label {
        width: 80px;
        display: flex;
        justify-content: space-between;
        margin-right: 24px;
      }

      .value {
        flex: 1;
        font-weight: bold;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
}
</style>
