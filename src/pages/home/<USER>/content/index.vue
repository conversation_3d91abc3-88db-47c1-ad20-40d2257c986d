<script setup>
import { ElMessage } from "element-plus";
import { httpPost } from "@wl-fe/http/dist";
import Project from "./project/index.vue"
import SearchButton from "@/components/searchButton.vue"
import WlTable from "@/components/wlTable.vue"
import { calendarFormat, getColumns } from "../constant"
import dayjs from 'dayjs';
const props = defineProps({
    currentDay: {
        type: String,
        default: ''
    },
    records: {
        type: Array,
        default: []
    }
})
const spinning = ref(false)
const list = ref([]) 
const data = ref() 
const searchParams  = ref({})
const emit = defineEmits(["onOk"])
onMounted(() => {
    data.value = props.records.find(item => item.rivetingTime === props.currentDay)
})


const onSearch = async (keyword) => {
        const newParams = { 
            ...searchParams,
            keyword
        }
        searchParams.value = newParams
        spinning.value = true
        try {
            const result = await httpPost("/subsyStemCallController/getBlindboxList", searchParams.value)
            list.value = result.rows
            spinning.value = false
        } catch (error) {
            list.value = []
            spinning.value = false
        }
    }
    const onLink = async (data, row, index) => {
        await httpPost("/sysMemorandumController/saveMemorandumData", {
            rivetingTime: dayjs(props.currentDay).format(calendarFormat),
            businessJson: row
        })
        ElMessage.success("关联成功")
        emit('onOk')
    }
</script>
<template>
          <div class="content">
            <Project v-if="data" :data="data"></Project>
            <div v-else class="result">
                <SearchButton placeholder="请输入项目名称" @search="onSearch" ></SearchButton>
                <div class="list">
                    <a-spin :spinning="spinning">
                        <WlTable style="width: 600px;" v-if="list.length > 0" :tableColumns="getColumns(onLink)" :tableData="list"></WlTable>
                        <el-empty v-else description="暂无数据" />
                    </a-spin>
                </div>
            </div>
            <!-- {
                data ? <Project data={data} /> : <div className={styles.result}>
                    <SearchButton placeholder="请输入项目名称" onSearch={(keyword) => {
                        onSearch(keyword)
                    }} />
                    <div className={styles.list}>
                        <Spin spinning={loading}>
                            {
                                list.length > 0 ?
                                    <WlTable columns={getColumns(onLink)} dataSource={list} />
                                    : <Empty />
                            }
                        </Spin>
                    </div>
                </div>
            } -->
        </div>
  </template>
  
  <style scoped lang="less">
.content {
    height: 100%;
    .result {
        display: flex;
    height: 100%;
        flex-direction: column;
        align-items: center;
        .list {
            margin-top: 32px;
            flex: 1;
            overflow-y: auto;
            overflow-x: hidden;
        }
    }
}
</style>
  