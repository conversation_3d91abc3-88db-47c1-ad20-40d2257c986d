<template>
    <PageWrapper>
        <div class="project-list" v-loading="isLoading">
            <div class="project-search-box">
                <el-select
                        class="select"
                        clearable
                        v-model="searchInfo.isRead"
                        @change="handleGetList"
                        placeholder="状态筛选"
                        size="large"
                >
                    <el-option
                            v-for="item in readOption"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                    />
                </el-select>
                <!-- 风险等级筛选 -->
                <el-select
                        class="select"
                        clearable
                        style="width: 250px"
                        v-model="searchInfo.riskLevel"
                        @change="handleGetList"
                        placeholder="风险等级筛选"
                        size="large"
                >
                    <el-option label="低风险" :value="1" />
                    <el-option label="中风险" :value="2" />
                    <el-option label="高风险" :value="3" />
                </el-select>
              <!-- 电子监察点筛选 -->
              <el-select
                class="select"
                clearable
                style="width: 250px"
                v-model="searchInfo.supervisionPoint"
                @change="handleGetList"
                placeholder="电子监察点筛选"
                size="large"
              >
                <el-option
                  v-for="item in supervisionPointOptions"
                  :key="item.dictValue"
                  :label="item.dictLabel"
                  :value="item.dictValue"
                />
              </el-select>

                <el-input v-model="searchInfo.keyword" placeholder="请输入标段名称或标段编号"
                          clearable
                          class="custom-input"
                />
                <el-button round type="primary" @click="handleSearch" class="custom-button">搜索</el-button>
                <el-button round type="primary" @click="handleAddRisk" class="custom-button">新增</el-button>
                <HeaderCard
                        v-if="isShowAuditHeader"
                        :statsData="statsData"
                        @changeType="changeType"
                ></HeaderCard>
            </div>
            <div class="project-content">
                <div class="project-city" v-if="isShowList">
                    <City
                            :selectCityId="searchInfo.regionCode"
                            @selectCity="handleSelectCity"
                            :city-list="cityLists"
                    ></City>
                </div>
                <div class="project-list-box">
                    <div class="project-list-conotent">
                        <el-table
                                :data="tableData"
                                style="width: 100%"
                                :height="isShowAuditHeader ? 592 : 620"
                                :row-class-name="tableRowClassName"
                                class="my-table"
                        >
                            <el-table-column
                                    label="序号"
                                    type="index"
                                    :index="indexMethod"
                                    align="center"
                                    width="70px"
                            />
                            <el-table-column
                                    v-for="item in tableColData"
                                    :key="item.prop"
                                    align="center"
                                    :prop="item.prop"
                                    :label="item.label"
                                    :minWidth="item.width"
                                    :show-overflow-tooltip=" ['eventsContent','bidSectionCode','bidSectionName'].includes(item.prop) ? false: tooltipOptions"
                            >
                                <template #default="{ row }">
                                    <div v-if="['isRead'].includes(item.prop)">
                    <span :class="{'font-blod':row[item.prop] == 0}">
                      <img :src="row[item.prop] == 0 ? nReader : sReader" alt="" class="wjIcon"/>
                    </span>
                                    </div>
                                    <div v-if="['riskLevel'].includes(item.prop)">
                    <span :class="riskLevelList[row[item.prop]].class">
                        {{ riskLevelList[row[item.prop]].label }}
                    </span>
                                    </div>
                                    <div v-if="['platformCode'].includes(item.prop)">
                                        <el-popover placement="right-start" trigger="hover" width="260">
                                            <template #reference>
                                                <span class="step-span">{{ platformOption.find(opt => opt.value === row[item.prop])?.label }}</span>
                                            </template>
                                            <div class="project-city-box">
                                                <span>{{ platformOption.find(opt => opt.value === row[item.prop])?.label }}</span>
                                            </div>
                                        </el-popover>
                                    </div>
                                    <div v-if="['eventsContent'].includes(item.prop)">
                                        <el-popover placement="right-start" trigger="hover" width="260">
                                            <template #reference>
                                                <span class="step-span">{{ row[item.prop] }}</span>
                                            </template>
                                            <div class="project-city-box">
                                                <span>{{ row[item.prop] }}</span>
                                            </div>
                                        </el-popover>
                                    </div>
                                    <div v-if="['bidSectionCode'].includes(item.prop)">
                                        <el-popover placement="right-start" trigger="hover" width="260">
                                            <template #reference>
                                                <span class="step-span">{{ row[item.prop] }}</span>
                                            </template>
                                            <div class="project-city-box">
                                                <div v-for="(val,index) in row[item.prop].split(',')" :key="val"><span>{{
                                                    index + 1
                                                    }}、</span>{{ val }}
                                                </div>
                                            </div>
                                        </el-popover>
                                    </div>
                                    <div v-if="['bidSectionName'].includes(item.prop)">
                                        <el-popover placement="right-start" trigger="hover" width="260">
                                            <template #reference>
                                                <span class="step-span">{{ row[item.prop] }}</span>
                                            </template>
                                            <div class="project-city-box">
                                                <div v-for="(val,index) in row[item.prop].split(',')" :key="val"><span>{{
                                                    index + 1
                                                    }}、</span>{{ val }}
                                                </div>
                                            </div>
                                        </el-popover>
                                    </div>
                                    <span
                                            v-if="
                      !['isRead', 'eventsContent','platformCode','riskLevel','bidSectionCode','bidSectionName'].includes(
                        item.prop
                      )
                    "
                                    >{{ row[item.prop] ? row[item.prop] : "--" }}</span
                                    >
                                </template>
                            </el-table-column>
                            <el-table-column align="center" label="操作" width="120px">
                                <template #default="{ row }">
                                    <el-button
                                            type="primary"
                                            class="my-detail-btn"
                                            plain
                                            size="small"
                                            round
                                            @click="handleViewDetail(row)"
                                    >查看详情
                                    </el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                        <div class="pagination-box">
                            <el-button
                                    class="my-instruct-btn"
                                    round
                                    type="primary"
                                    plain
                                    @click="handleClose"
                            >关 闭
                            </el-button
                            >
                            <el-pagination
                                    class="pagination"
                                    background
                                    v-model:current-page="searchInfo.pageNum"
                                    v-model:page-size="searchInfo.pageSize"
                                    :size="searchInfo.pageSize"
                                    :total="total"
                                    :page-sizes="[100, 200, 300, 400]"
                                    layout="total, sizes, prev, pager, next, jumper"
                                    @size-change="handleSizeChange"
                                    @current-change="handleChangePage"
                            />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </PageWrapper>
    <allDialogView ref="allDialogViewEl" :dialogTitle="dialogTitle">
        <template #content>
            <projectInstruct
                    v-if="allDialogViewEl.showDialog"
                    @handleClose="handleInitiate"
                    :type="instructType"
                    :tenderProjectGuid="tenderProjectGuid"
                    :detailRowGuid="detailRowGuid"
            ></projectInstruct>
        </template>
    </allDialogView>
    <el-drawer custom-class="my-drawer" modal-class="my-drawer-detail" :append-to-body="false" v-model="isShowDrawer"
               v-if="isShowDrawer" size="50%" :show-close="false">
        <template #header>
            <h2></h2>
        </template>
        <detailModule
                v-for="(item, index) in supervisionList"
                :key="index"
                :headerTitle="item['监察点名称']"
                :moduleInfo="item"
                class="detail-module"
        ></detailModule>
    </el-drawer>
    <el-drawer custom-class="my-drawer" modal-class="my-drawer-detail" :append-to-body="false" v-model="isAddDrawer"
               v-if="isAddDrawer" size="60%" :show-close="false">
        <template #header>
            <h2></h2>
        </template>
        <risk-warning-form ref="addModuleRef" @handle-close="handleRiskWaringClose"></risk-warning-form>
    </el-drawer>
    <TableDetail ref="tableDetailRef"/>
    <cardDetail ref="cardDetailRef"/>
  <allDialogView
      ref="abnormalDataTableDialog"
      :dialogTitle="'违法违规'"
      :dialogWidth="'95%'"
  >
    <template #content>
      <AbnormalDataTable
          v-if="abnormalDataTableDialog.showDialog"
          :abnormalData="abnormalData"
          @handleCloseDialog="handleAbnormalDataClose"
      ></AbnormalDataTable>
    </template>
  </allDialogView>
    <allDialogView ref="allDialogViewJS" :dialogTitle="dialogTitle">
        <template #content>
            <div style="height:680px;width: 100%;" v-loading="isAiLoading">
                <el-tabs v-model="tableHeaderCode" @tab-click="handleClickTab">
                    <el-tab-pane v-for="item in reviewItemsList" :key="item.tableHeaderName"
                                 :label="item.tableHeaderName" :name="item.tableHeaderCode"></el-tab-pane>
                </el-tabs>
                <div ref="refLine" style="width: 100%; height: 600px;border: 1px solid #ccc;"></div>
            </div>
            <div class="pagination-box">
                <el-button
                        class="my-instruct-btn"
                        round
                        type="primary"
                        plain
                        @click="allDialogViewJS.showDialog = false"
                >关 闭
                </el-button>
            </div>
        </template>
    </allDialogView>
</template>

<script setup>
import City from "./cityList/index.vue";
import AbnormalDataTable from "./components/AbnormalDataTable.vue";
import HeaderCard from "./headerCard/index.vue";
import projectInstruct from "@/pages/project/projectInstruct/index.vue";
import riskWarningForm from  "./components/RiskWarningForm.vue"
import {httpGet, httpPost} from "@wl-fe/http/dist";
import {DATA_ARRAY, htStatusRender, RELIST} from "./content.js";
import {ElMessage} from "element-plus";
import AccountManagementFilter from "@/filters/index.js";
import {getOptionList} from "@/pages/project/projectBadBehavior/formOptionList";
import nReader from "@/assets/home/<USER>";
import sReader from "@/assets/home/<USER>";
import TableDetail from "@/pages/smartRegulation/modelPage/components/tableDetail.vue";
import {ref} from "vue";
import CardDetail from "@/pages/smartRegulation/modelPage/components/cardDetail.vue";
import RiskWarningForm from "@/pages/home/<USER>/riskWarningList/components/RiskWarningForm.vue";
import useGloBalStore from "@/store/useGlobalStore"
const { user } = useGloBalStore();
const emit = defineEmits(["handleCloseDialog"]);
const isAiLoading = ref(false)
const tableHeaderCode = ref("全部")
const allDialogViewJS = ref(null)
const abnormalDataTableDialog = ref({
  showDialog: false
})
const abnormalData = ref({})
const refLine = ref(null)
const reviewItemsList = ref(RELIST)
const handleClickTab = (tab) => {
    console.log(tab.props.name, tableHeaderCode.value, reviewItemsList.value)
    reviewItemsList.value.forEach((item, index) => {
        if (item.tableHeaderCode == tab.props.name) {
            handleGetCompareProcess(item.data)
        }
    })
}
const handleGetCompareProcess = async (data) => {
    isAiLoading.value = true
    // const {data} = await compareProcesspdf({reviewGuid: this.tableHeaderCode, expertGuid: this.expertGuid})
    setTimeout(async () => {
        isAiLoading.value = false
        const cbscore = [];
        data.forEach((item, index) => {
            item.children.forEach((child, chiindex) => {
                console.log('index', index)
                console.log('child', child)
                if (index == 0) {
                    cbscore.push([child.insId])
                } else {
                    cbscore[chiindex].push(child.insId)
                }
            })
        })
        // actualScopeList
        console.log('child.insId', cbscore)
        const aiScopeList = data.map((item) => {
            return item.jsfs
        })
        const averageScopedList = data.map((item) => {
            return item.nodeFileHash
        })
        const companyList = data.map((item) => {
            return item.fileName
        })
        console.error(companyList, cbscore, aiScopeList)
        await nextTick();
        changeLine(companyList, cbscore, aiScopeList, averageScopedList, data)
    }, 1000);
}
// 添加电子监察点选项数据
const supervisionPointOptions = ref([
  { label: '投标环节异常', value: '1' },
  { label: '开标环节异常', value: '2' },
  { label: '评标环节异常', value: '3' },
  { label: '定标环节异常', value: '4' },
  { label: '合同履约异常', value: '5' }
]);
const handleRiskWaringClose = () => {
    isAddDrawer.value = false
}

const handleAbnormalDataClose = () => {
    abnormalDataTableDialog.value.showDialog = false;
}
const changeLine = async (companyList, actualScopeList, aiScopeList, averageScopedList, dataInfo) => {
    console.log('dataInfo', dataInfo)
    let xAxisData = companyList
    let yAxisData1 = aiScopeList // [10, 13, 35, 17, 18, 15, 18, 23];
    let yAxisData2 = actualScopeList // [23, 23, 20, 18, 19, 23, 21, 18];
    let yAxisData3 = averageScopedList
    let color = [
        "#8B5CFF"
    ];
    yAxisData2.forEach((xis, index) => {
        color.push("#00CA69")
    })
    color.push("#ff8b00")
    const hexToRgba = (hex, opacity) => {
        let rgbaColor = "";
        let reg = /^#[\da-f]{6}$/i;
        if (reg.test(hex)) {
            rgbaColor = `rgba(${parseInt("0x" + hex.slice(1, 3))},${parseInt(
                "0x" + hex.slice(3, 5)
            )},${parseInt("0x" + hex.slice(5, 7))},${opacity})`;
        }
        return rgbaColor;
    }
    const series = [
        {
            // name: "2018",
            name: "机器赋分趋势指数",
            type: "line",
            smooth: true,
            symbolSize: 8,
            zlevel: 3,
            lineStyle: {
                normal: {
                    color: color[0],
                    shadowBlur: 3,
                    shadowColor: hexToRgba(color[0], 0.5),
                    shadowOffsetY: 0
                }
            },
            symbol: 'circle',//数据交叉点样式
            data: yAxisData1
        }]

    yAxisData2.forEach((xis, index) => {
        series.push({
            name: dataInfo[0].children[index].fileUrl + "赋分趋势指数",
            type: "line",
            smooth: true,
            symbolSize: 8,
            zlevel: 3,
            lineStyle: {
                normal: {
                    color: color[1 + index],
                    shadowBlur: 3,
                    shadowColor: hexToRgba(color[1 + index], 0.5),
                    shadowOffsetY: 0
                }
            },
            symbol: 'circle',//数据交叉点样式 (实心点)
            data: xis
        })
    })

    series.push({
        // name: "2018",
        name: "平均赋分趋势指数",
        type: "line",
        smooth: true,
        symbolSize: 8,
        zlevel: 3,
        lineStyle: {
            normal: {
                color: color[color.length - 1],
                shadowBlur: 3,
                shadowColor: hexToRgba(color[color.length - 1], 0.5),
                shadowOffsetY: 0
            }
        },
        symbol: 'circle',//数据交叉点样式
        data: yAxisData3
    })

    var option = {
        backgroundColor: '#fff',
        color: color,
        legend: {
            top: 20,

        },
        tooltip: {
            trigger: "axis",
            formatter: function (params) {
                let html = '';
                params.forEach(v => {
                    html += `<div style="color: #666;font-size: 14px;line-height: 24px">
                      <span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${color[v.componentIndex]};"></span>
                      ${v.name}<span style="color:${color[v.componentIndex]};font-weight:700;font-size: 18px;margin-left:5px">${v.seriesName.replace('赋分趋势指数', '')}</span>赋分趋势指数
                      <span style="color:${color[v.componentIndex]};font-weight:700;font-size: 18px;margin-left:5px">${v.value}</span>
                      `;
                })
                return html
            },
            extraCssText: 'background: #fff; border-radius: 0;box-shadow: 0 0 3px rgba(0, 0, 0, 0.2);color: #333;',
            axisPointer: {
                type: 'shadow',
                shadowStyle: {
                    color: '#ffffff',
                    shadowColor: 'rgba(225,225,225,1)',
                    shadowBlur: 5
                }
            }
        },
        grid: {
            top: 100,
            left: 330,
            right: 330,

            containLabel: true
        },
        xAxis: [{
            type: "category",
            boundaryGap: false,
            axisLabel: {
                formatter: '{value}',
                textStyle: {
                    color: "#333"
                }
            },
            axisLine: {
                lineStyle: {
                    color: "#D9D9D9"
                }
            },
            data: xAxisData,
        }],
        yAxis: [{
            type: "value",
            name: '趋势指数',
            axisLabel: {
                textStyle: {
                    color: "#666"
                }
            },
            nameTextStyle: {
                color: "#666",
                fontSize: 12,
                lineHeight: 40
            },
            // 分割线
            splitLine: {
                lineStyle: {
                    type: "dashed",
                    color: "#E9E9E9"
                }
            },
            axisLine: {
                show: false
            },
            axisTick: {
                show: false
            }
        }],
        series: series
    };
    await nextTick();
    const myChart = echarts.init(refLine.value);
    myChart.setOption(option);
}
// 定义 tooltipOptions 对象
const tooltipOptions = ref({
    effect: 'light', // 提示框主题为亮色
    placement: 'top', // 提示框显示在单元格上方
    showArrow: true, // 显示提示框的箭头
    hideAfter: 200, // 提示框隐藏的延迟时间为 200 毫秒
    popperOptions: {
        strategy: 'fixed' // 使用 fixed 定位策略
    }
});
const tableDetailRef = ref(null)
const cardDetailRef = ref(null)
const riskLevelList = ref({
    1: {
        label: "低风险",
        class: "span-yellow"
    },
    2: {
        label: "中风险",
        class: "span-orign"
    },
    3: {
        label: "高风险",
        class: "span-red"
    }
});
const isShowDrawer = ref(false);
const isAddDrawer = ref(false);
const supervisionList = ref([]);
const handleClose = () => {
    emit("handleCloseDialog");
};
const {getInsStatusColor} = AccountManagementFilter();
console.log("getInsStatusColor", getInsStatusColor, AccountManagementFilter);

const router = useRouter();
const route = useRoute();
const indexMethod = (index) => {
    return index + 1;
};
const changeType = (val) => {
    searchInfo.illegalType = val;
    handleGetList();
};
const handleSearch = (val) => {
    // searchInfo.keyword = val;
    handleGetList();
};
const handleAddRisk = () => {
    isAddDrawer.value = true;
};
const isLoading = ref(false);
let total = ref(1);
let statsData = ref(null);
let {query} = toRefs(route);
const {nodeCode, parentNodeCode} = query.value;
const tableColData = ref(
    DATA_ARRAY["RISKLIST"]
);
const readOption = ref([
    {
        label: "未读",
        value: 0,
    },
    {
        label: "已读",
        value: 1,
    },
]);
let searchInfo = reactive({
    keyword: "",
    regionCode: "",
    pageNum: 1,
    pageSize: 10,
    nodeCode: "TENDER_RECORD",
    isToday: "1",
    approvalStatus: 1,
    platformCode: undefined,
    isRead: undefined,
    qualificationExamination: undefined,
    riskLevel: undefined, // 新增风险等级筛选字段
    supervisionPoint: undefined, // 新增电子监察点筛选
});
const dialogTitle = ref("");
const behaviorDetail = ref({});

const isShowAuditHeader = computed(() => {
    return (
        statsData.value != null
    );
});
const handleSelectCity = (item) => {
    searchInfo.regionCode = item.code;
    handleGetList();
    getRiskStatsData();
};
const tableRowClassName = ({rowIndex}) => {
    if (rowIndex % 2 !== 0) {
        return "warning-row";
    }
    return "";
};

const handleViewDetail = async (row) => {
    await httpGet("/riskController/updateRiskReadStatus/" + row.riskGuid);
    if (row.illegalType == 1) {
        isShowDrawer.value = true;
        supervisionList.value = row.supervisionList;
    } else if (row.illegalType == 2) {
        const params = {objectNo: row.illegalRecordGuid, platFormCode: row.platformCode};
        const res = await httpPost("/subSysComplaint/skipUrl", params);
        window.open(res.msg, "_blank");
        return
    } else if( row.illegalType == 3){
      abnormalData.value = row;
      abnormalDataTableDialog.value.showDialog = !abnormalDataTableDialog.value.showDialog;
    } else {
        if (row.modelCode == 23) {
            tableDetailRef.value.openDialog(true)
            tableDetailRef.value.getTableDetail("", row.modelCode, "", "", "12382;12383;12384;12385;12386;12387;12388")
        } else if (row.modelCode == 25) {
            cardDetailRef.value.openDialog(true)
            let obj = {
                "bidId": "63fbb520-7dc2-4660-9763-e5956b0ab235",
                "tbrId": "0af296d2-a9c7-4346-9a45-2528c1d4ca00",
                "modelCode": "25"
            }
            cardDetailRef.value.getDetail(obj, 25, "123123")
        } else if (row.modelCode == 50) {
            console.log("打分趋势")
            dialogTitle.value = "技术标评审评分分析表";
            allDialogViewJS.value.showDialog = !allDialogViewJS.value.showDialog;
            reviewItemsList.value.forEach((item, index) => {
                if (item.tableHeaderCode == '全部') {
                    handleGetCompareProcess(item.data)
                }
            })
        }


    }
};
let cityLists = ref([]);
const isShowList = computed(() => {
    return cityLists.value.length > 0;
});
const handleSizeChange = (val) => {
    console.log(val);
    searchInfo.pageSize = val
    handleGetList();
};
const handleChangePage = (val) => {
    console.log(val);
    searchInfo.pageNum = val;
    handleGetList();
};
const platformOption = ref([]);
const qualificationOption = ref([]);
onMounted(async () => {
    if (nodeCode != "BAD_BEHAVIOR") {
        const list = await httpGet("/api/sysRegion/list", {});
        cityLists.value = list;
        searchInfo.regionCode = user.regionCode ? user.regionCode : "";
    }
    // 获取电子监察点数据
    try {
      const illegalList = await httpGet("/riskController/getIllegalList");
      supervisionPointOptions.value = illegalList;
    } catch (error) {
      console.error("获取电子监察点数据失败:", error);
    }
    handleGetList();
    getRiskStatsData();
    platformOption.value = await getOptionList("jyptbm");
    qualificationOption.value = await getOptionList("zgscfs");
    console.log(
        "processingOptions",
        platformOption.value,
        qualificationOption.value
    );
});
let tableData = reactive([]);
const handleStatsApiList = (key) => {
    let api = "";
    switch (key) {
        case "AS_AGENCY_LOCKOUT":
            break;
        case "C_TENDER_SITCARD_SWIPING":
            break;
        case "AS_INSTRUCTION_MATTERS":
            break;
        case "C_TENDER_AGENT_EVALUATE":
            break;
        case "BAD_BEHAVIOR":
            break;
        default:
            api = `/inviteTender/tenderProcessNode/getStatsData`;
            break;
    }
    return api;
};
const handleGetList = async () => {
    isLoading.value = true;
    let data = await httpPost(
        `/riskController/getRiskEventsList?pageNum=${searchInfo.pageNum}&pageSize=${searchInfo.pageSize}`,
        searchInfo
    );


    isLoading.value = false;
    total.value = data.total;
    tableData = data.rows;
};
const getRiskStatsData = async () => {
    statsData.value = await httpPost(
        "/riskController/getRiskStatsData",
        searchInfo
    );
}

const allDialogViewEl = ref();
const instructType = ref("1");
const tenderProjectGuid = ref("");
const detailRowGuid = ref("");
const handleInitiate = (guid) => {
    tenderProjectGuid.value = "";
    instructType.value = "1";
    dialogTitle.value = "新增行政监管事项";
    if (guid) {
        tenderProjectGuid.value = guid;
    }
    if (guid == "refresh") {
        handleGetList();
    }
    allDialogViewEl.value.showDialog = !allDialogViewEl.value.showDialog;
};
const handleRevoke = (guid) => {
    ElMessageBox.alert("确认撤回？", "", {
        confirmButtonText: "确认",
        callback: async (action) => {
            if (action == "confirm") {
                const result = await httpPost(
                    `/inviteTender/badBehavior/removeBadBehavior/${guid}`
                );
                if (result.code == 200) {
                    ElMessage({
                        message: result.msg,
                        type: "success",
                    });
                    handleGetList();
                } else {
                    ElMessage({
                        message: result.msg,
                        type: "error",
                    });
                }
            }
        },
    });
};
const handleCompleted = async (guid) => {
    const result = await httpPost(
        "/inviteTender/asProjectPauseRestoration/completed",
        {rowGuid: guid}
    );
    if (result.code == 200) {
        ElMessage({
            message: result.msg,
            type: "success",
        });
        handleGetList();
    } else {
        ElMessage({
            message: result.msg,
            type: "error",
        });
    }
};

const handleDrive = () =>{
    handleInitiate()
}
</script>
<style scoped lang="less">
.project-list {
  width: 100%;
  height: 100%;

  .project-search-box {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;


    .custom-input {
      width: 600px; // 统一输入框和日期选择器的宽度
      transition: border-color 0.3s; // 边框颜色过渡效果
      margin-right: 16px;
      height: 35px;

      :deep(.el-input__wrapper) {
        border-radius: 50px;
      }
    }

    .custom-button {
      height: 35px;
      width: 120px;
    }

    .select {
      width: 200px;
      margin-right: 16px;
      border-radius: 50px;

      :deep(.el-select__wrapper) {
        border-radius: 50px;
        min-height: 35px
      }
    }

    .back-btn {
      width: 90px;
    }

    .success-btn {
      width: 150px;
    }
  }

  .project-content {
    width: 100%;
    height: 60%;
    margin-top: 1%;
    display: flex;
    justify-content: space-between;

    .project-city {
      width: 8%;
      height: 100%;
      margin-right: 10px;
      overflow: auto;
      flex-shrink: 0;
    }

    .project-list-box {
      height: 100%;
      flex: 1;
      width: 0;
      display: flex;
      flex-direction: column;

      .radio-box {
        text-align: right;
      }

      .project-list-conotent {
        width: 100%;
        // height: 82%;
        // margin-top: 1%;
        flex: 1;
        height: 0;

        .pagination-box {
          margin-top: 20px;
          display: flex;
          flex-direction: row;
          justify-content: space-between;
        }
      }
    }
  }
}

.my-instruct-btn {
  margin-left: 42%;
  width: 10%;
}

.table-view {
  width: 20px;
  height: 20px;
  border-radius: 20px;
  border: 1px solid #3366ff;
  text-align: center;
  line-height: 22px;
  cursor: pointer;
  margin: 0 auto;
  color: #3366ff;
}

.my-img {
  width: 20px;
  margin-right: 5px;
}

.wjIcon {
  width: 30px;
  height: auto;
}

.my-img-yj {
  width: 30px;
}

.span-red {
  color: #ff0000;

  &::before {
    content: "●";
    color: #ff0000;
    font-size: 15px;
    margin-right: 5px;
  }
}

.span-orign {
  color: #FFA500;

  &::before {
    content: "●";
    color: #FFA500;
    font-size: 15px;
    margin-right: 5px;
  }
}

.span-yellow {
  color: #f5e208;

  &::before {
    content: "●";
      color: #f5e208;
    font-size: 15px;
    margin-right: 5px;
  }
}

.step-span {
  display: inline-block;
  width: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.status-red {
  color: red;
}

.status-green {
  color: green;
}

.status-orign {
  color: #f4871c;
}

/* 设置滚动条的样式 */
.project-city-box::-webkit-scrollbar {
  width: 6px;
  box-sizing: border-box;
}

/* 滚动条滑块 */
.project-city-box::-webkit-scrollbar-thumb {
  height: 5px;
  border-radius: 5px;
  background-color: #c2cede;
}

/*滚动槽*/
.project-city-box::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0);
  border-radius: 10px;
  background: #ffffff;
}

/* 滚动条滑块 鼠标悬浮 */
.project-city-box::-webkit-scrollbar-thumb:hover {
  background-color: #c2cede;
  // border: 1px solid rgba(0, 0, 0, 0.21);
  cursor: pointer;
}

.my-table {
  :deep(.el-table__header-wrapper) {
    .el-table__cell {
      background: #f0f6ff;
      height: 53px;
      color: rgba(0, 0, 0, 0.88);
    }
  }

  :deep(.el-table__row) {
    height: 53px;
  }

  :deep(.warning-row) {
    background: #f9fbff;
  }
}

:deep(.my-descriptions) {
  // background: #dceaff;
  font-size: 16px;
  width: 22%;
  color: #000;
  text-align: center;
  background: #f6faff;
}
</style>
