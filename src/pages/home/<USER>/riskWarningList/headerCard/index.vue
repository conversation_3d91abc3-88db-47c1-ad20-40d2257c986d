<template>
  <div class="header-card">
    <div
      class="card-type"
      v-for="item in cardList"
      :key="item.text"
      :class="item.bgClass"
      @click="() => emit('changeType', item.type)"
    >
      <div class="card-text">{{ item.text }}</div>
      <div class="card-value">{{ statsData[item.value] }}</div>
    </div>
  </div>
</template>

<script setup>
import { reactive, defineProps } from "vue";
const emit =  defineEmits(["changeType"]);
let props = defineProps({
  statsData: {
    type: Object,
    default: {},
  },
});
let cardList = reactive([
  {
    text: "全部",
    value: "all",
    bgClass: "bg-blue",
    type: "",
  },
  {
    text: "电子监察",
    value: "dzjc",
    bgClass: "bg-purple",
    type: "1",
  },
  {
    text: "异议投诉",
    value: "yyts",
    bgClass: "bg-green",
    type: "2",
  },
  {
    text: "投标违规",
    value: "tbwg",
    bgClass: "bg-origan",
    type: "3",
  },
]);
</script>

<style lang="less" scoped>
.header-card {
  width: 100%;
  display: flex;
  justify-content: flex-end;
}
.card-type {
  width: 195px;
  height: 65px;
  margin-right: 30px;
  color: #fff;
  font-size: 20px;
  font-weight: bold;
  position: relative;
  cursor: pointer;
  .card-text {
    position: absolute;
    top: 8px;
    left: 47px;
  }
  .card-value {
    position: absolute;
    top: 38px;
    left: 47px;
  }
}
.bg-blue {
  background: url("/projectManagement/status1.png") no-repeat;
  background-size: 100% 100%;
}
.bg-purple {
  background: url("/projectManagement/status5.png") no-repeat;
  background-size: 100% 100%;
}
.bg-green {
  background: url("/projectManagement/status6.png") no-repeat;
  background-size: 100% 100%;
}
.bg-origan {
  background: url("/projectManagement/status7.png") no-repeat;
  background-size: 100% 100%;
}
</style>
