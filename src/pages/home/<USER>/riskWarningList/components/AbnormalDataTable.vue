<template>
  <PageWrapper>
    <div class="project-list" v-loading="isLoading">
      <div class="project-content">
        <div class="project-list-box">
          <div class="project-list-conotent">
            <el-table
                :data="tableData"
                style="width: 100%"
                :height="isShowAuditHeader ? 592 : 620"
                :row-class-name="tableRowClassName"
                class="my-table"
            >
              <el-table-column
                  label="序号"
                  type="index"
                  :index="indexMethod"
                  align="center"
                  width="70px"
              />
              <el-table-column
                  v-for="item in tableColData"
                  :key="item.prop"
                  align="center"
                  :prop="item.prop"
                  :label="item.label"
                  :minWidth="item.width"
                  :show-overflow-tooltip=" ['bidSectionCode','bidSectionName'].includes(item.prop) ? false: tooltipOptions"
              >
                <template #default="{ row }">
                  <div v-if="['bidSectionCode'].includes(item.prop)">
                    <el-popover placement="right-start" trigger="hover" width="260">
                      <template #reference>
                        <span class="step-span">{{ row[item.prop] }}</span>
                      </template>
                      <div class="project-city-box">
                        <div v-for="(val,index) in row[item.prop].split(',')" :key="val"><span>{{
                            index + 1
                          }}、</span>{{ val }}
                        </div>
                      </div>
                    </el-popover>
                  </div>
                  <div v-if="['bidSectionName'].includes(item.prop)">
                    <el-popover placement="right-start" trigger="hover" width="260">
                      <template #reference>
                        <span class="step-span">{{ row[item.prop] }}</span>
                      </template>
                      <div class="project-city-box">
                        <div v-for="(val,index) in row[item.prop].split(',')" :key="val"><span>{{
                            index + 1
                          }}、</span>{{ val }}
                        </div>
                      </div>
                    </el-popover>
                  </div>
                </template>
              </el-table-column>
            </el-table>
            <div class="pagination-box">
              <el-button
                  class="my-instruct-btn"
                  round
                  type="primary"
                  plain
                  @click="handleClose"
              >关 闭
              </el-button
              >
              <el-pagination
                  class="pagination"
                  background
                  v-model:current-page="searchInfo.pageNum"
                  v-model:page-size="searchInfo.pageSize"
                  :size="searchInfo.pageSize"
                  :total="total"
                  :page-sizes="[100, 200, 300, 400]"
                  layout="total, sizes, prev, pager, next, jumper"
                  @size-change="handleSizeChange"
                  @current-change="handleChangePage"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </PageWrapper>
  <TableDetail ref="tableDetailRef"/>
</template>

<script setup>
import {httpGet, httpPost} from "@wl-fe/http/dist";
import {DATA_ARRAY} from "./content.js";
import TableDetail from "@/pages/smartRegulation/modelPage/components/tableDetail.vue";
import {ref} from "vue";
import useGloBalStore from "@/store/useGlobalStore"
// 使用从父组件传入的 abnormalData
const {user} = useGloBalStore();
const emit = defineEmits(["handleCloseDialog"]);

// 定义 tooltipOptions 对象
const tooltipOptions = ref({
  effect: 'light', // 提示框主题为亮色
  placement: 'top', // 提示框显示在单元格上方
  showArrow: true, // 显示提示框的箭头
  hideAfter: 200, // 提示框隐藏的延迟时间为 200 毫秒
  popperOptions: {
    strategy: 'fixed' // 使用 fixed 定位策略
  }
});
const tableDetailRef = ref(null)
const handleClose = () => {
  emit("handleCloseDialog");
};

const router = useRouter();
const route = useRoute();
const indexMethod = (index) => {
  return index + 1;
};
const changeType = (val) => {
  searchInfo.illegalType = val;
  handleGetList();
};
const isShowAuditHeader = computed(() => {
  return (
      statsData.value != null
  );
});
const props = defineProps({
  modelCode: {
    type: String,
    default: ''
  },
  abnormalData: {
    type: Object,
    default: () => ({})
  }
});
const isLoading = ref(false);
let total = ref(1);
let statsData = ref(null);
let {query} = toRefs(route);
const {nodeCode, parentNodeCode} = query.value;
const tableColData = ref('');
let searchInfo = reactive({
  keyword: "",
  regionCode: "",
  pageNum: 1,
  pageSize: 10,
  platformCode: undefined,
  modelCode: props.modelCode
});
const dialogTitle = ref("");

const tableRowClassName = ({rowIndex}) => {
  if (rowIndex % 2 !== 0) {
    return "warning-row";
  }
  return "";
};
const handleSizeChange = (val) => {
  console.log(val);
  searchInfo.pageSize = val
  handleGetList();
};
const handleChangePage = (val) => {
  console.log(val);
  searchInfo.pageNum = val;
  handleGetList();
};
onMounted(async () => {
  tableColData.value = DATA_ARRAY.WIN_BIDDER_LIST
  
  // 确保在有 abnormalData 数据时才调用 API
  if (props.abnormalData && Object.keys(props.abnormalData).length > 0) {
    handleGetList();
  }
});
let tableData = reactive([]);
const handleGetList = async () => {
  isLoading.value = true;
  let data = await httpPost(
      `/riskController/getAbnormalDataList?pageNum=${searchInfo.pageNum}&pageSize=${searchInfo.pageSize}`,
      props.abnormalData
  );

  isLoading.value = false;
  total.value = data.total;
  tableData = data.rows;
};

const allDialogViewEl = ref();
</script>
<style scoped lang="less">
.project-list {
  width: 100%;
  height: 100%;

  .project-search-box {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;

    .select {
      width: 200px;
      margin-right: 16px;
      border-radius: 50px;

      :deep(.el-select__wrapper) {
        border-radius: 50px;
      }
    }

    .back-btn {
      width: 90px;
    }

    .success-btn {
      width: 150px;
    }
  }

  .project-content {
    width: 100%;
    height: 60%;
    margin-top: 1%;
    display: flex;
    justify-content: space-between;

    .project-city {
      width: 8%;
      height: 100%;
      margin-right: 10px;
      overflow: auto;
      flex-shrink: 0;
    }

    .project-list-box {
      height: 100%;
      flex: 1;
      width: 0;
      display: flex;
      flex-direction: column;

      .radio-box {
        text-align: right;
      }

      .project-list-conotent {
        width: 100%;
        // height: 82%;
        // margin-top: 1%;
        flex: 1;
        height: 0;

        .pagination-box {
          margin-top: 20px;
          display: flex;
          flex-direction: row;
          justify-content: space-between;
        }
      }
    }
  }
}

.my-instruct-btn {
  margin-left: 42%;
  width: 10%;
}

.table-view {
  width: 20px;
  height: 20px;
  border-radius: 20px;
  border: 1px solid #3366ff;
  text-align: center;
  line-height: 22px;
  cursor: pointer;
  margin: 0 auto;
  color: #3366ff;
}

.my-img {
  width: 20px;
  margin-right: 5px;
}

.wjIcon {
  width: 30px;
  height: auto;
}

.my-img-yj {
  width: 30px;
}

.span-red {
  color: #ff0000;

  &::before {
    content: "●";
    color: #ff0000;
    font-size: 15px;
    margin-right: 5px;
  }
}

.span-orign {
  color: #FFA500;

  &::before {
    content: "●";
    color: #FFA500;
    font-size: 15px;
    margin-right: 5px;
  }
}

.span-yellow {
  color: #f5e208;

  &::before {
    content: "●";
    color: #f5e208;
    font-size: 15px;
    margin-right: 5px;
  }
}

.step-span {
  display: inline-block;
  width: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.status-red {
  color: red;
}

.status-green {
  color: green;
}

.status-orign {
  color: #f4871c;
}

/* 设置滚动条的样式 */
.project-city-box::-webkit-scrollbar {
  width: 6px;
  box-sizing: border-box;
}

/* 滚动条滑块 */
.project-city-box::-webkit-scrollbar-thumb {
  height: 5px;
  border-radius: 5px;
  background-color: #c2cede;
}

/*滚动槽*/
.project-city-box::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0);
  border-radius: 10px;
  background: #ffffff;
}

/* 滚动条滑块 鼠标悬浮 */
.project-city-box::-webkit-scrollbar-thumb:hover {
  background-color: #c2cede;
  // border: 1px solid rgba(0, 0, 0, 0.21);
  cursor: pointer;
}

.my-table {
  :deep(.el-table__header-wrapper) {
    .el-table__cell {
      background: #f0f6ff;
      height: 53px;
      color: rgba(0, 0, 0, 0.88);
    }
  }

  :deep(.el-table__row) {
    height: 53px;
  }

  :deep(.warning-row) {
    background: #f9fbff;
  }
}

:deep(.my-descriptions) {
  // background: #dceaff;
  font-size: 16px;
  width: 22%;
  color: #000;
  text-align: center;
  background: #f6faff;
}

.search-button-box {
  display: flex;
  align-items: center;
  width: 100%;
  justify-content: flex-start;
}

.search-form {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 20px;
}

:deep(.el-form-item) {
  margin-bottom: 0;
}

:deep(.el-input__wrapper),
:deep(.el-select .el-input__wrapper),
:deep(.el-date-editor.el-input__wrapper) {
  border-radius: 6px !important;
  border: 1px solid #e0e3ea !important;
  background: #fff !important;
  box-shadow: none !important;
  min-height: 36px;
  padding: 0 12px;
}

:deep(.el-input__inner) {
  border-radius: 6px !important;
  background: #fff !important;
  color: #333;
  font-size: 14px;
}

:deep(.el-select .el-input__inner) {
  border-radius: 6px !important;
}

:deep(.el-form-item__label) {
  color: #888;
  font-size: 14px;
  padding-right: 8px;
}

:deep(.el-input__inner::placeholder) {
  color: #bfbfbf;
  font-size: 14px;
}

:deep(.el-button):hover {
  background: #409eff !important;
}

:deep(.el-select__wrapper) {
  border-radius: 6px !important;
}

:deep(.el-date-editor) {
  border-radius: 6px !important;
}
</style>
