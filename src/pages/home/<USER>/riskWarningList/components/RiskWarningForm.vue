<template>
  <div id="supervisionWarningForm" v-loading="isLoading">
    <el-form
      ref="supervisionFormRef"
      :model="supervisionForm"
      :rules="FORM_RULES"
      label-width="120px"
      class="supervision-form"
    >
      <div class="unify-title unify-title1">新增监察信息</div>
      <el-row :gutter="20">
        <el-col :span="20">
          <el-form-item :label="formContent[0].label" :prop="formContent[0].prop">
            <el-select
              v-model="supervisionForm[formContent[0].prop]"
              :placeholder="formContent[0].placeholder"
              filterable
              class="full-width"
            >
              <el-option
                v-for="dict in formContent[0].optionList"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
        <el-row :gutter="20">
            <el-col :span="12">
                <el-form-item :label="formContent[1].label" :prop="formContent[1].prop">
                    <el-select
                            v-model="supervisionForm[formContent[1].prop]"
                            :placeholder="formContent[1].placeholder"
                            class="full-width"
                    >
                        <el-option
                                v-for="dict in formContent[1].optionList"
                                :key="dict.value"
                                :label="dict.label"
                                :value="dict.value"
                        />
                    </el-select>
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item :label="formContent[2].label" :prop="formContent[2].prop">
                    <el-select
                            v-model="supervisionForm[formContent[2].prop]"
                            :placeholder="formContent[1].placeholder"
                            class="full-width"
                            popper-append-to-body="true"
                    >
                        <el-option
                                v-for="dict in formContent[2].optionList"
                                :key="dict.value"
                                :label="dict.label"
                                :value="dict.value"
                        />
                    </el-select>
                </el-form-item>
            </el-col>
        </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item :label="formContent[3].label" :prop="formContent[3].prop">
            <el-input
              v-model="supervisionForm[formContent[3].prop]"
              :placeholder="formContent[3].placeholder"
              class="full-width"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item :label="formContent[4].label" :prop="formContent[4].prop">
            <el-input
              v-model="supervisionForm[formContent[4].prop]"
              type="textarea"
              :rows="4"
              :placeholder="formContent[4].placeholder"
              class="full-width"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="btn-box">
      <el-button
        type="primary"
        class="my-supervision-btn"
        round
        @click="handleSubmit()"
      >
        提 交</el-button
      >
      <el-button
        class="my-supervision-btn"
        round
        type="primary"
        plain
        @click="handleClose"
        >关 闭</el-button
      >
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { httpPost } from "@wl-fe/http/dist";
import {getOptionList} from "@/pages/project/projectInstruct/formOptionList.js";

const emit = defineEmits(["handleClose"]);
const props = defineProps({
  tenderProjectGuid: {
    type: String,
    default: "",
  },
});

const bidList = ref([]);
const formContent = ref([
  {
    label: "标段列表",
    prop: "bidSectionGuid",
    type: "SELECT",
    placeholder: "请选择/输入关键字搜索",
    optionList: []
  },
  {
    label: "监察预警类型",
    prop: "level",
    type: "SELECT",
    placeholder: "请选择监察预警类型",
    optionList: [
      { label: "高风险", value: "3" },
      { label: "中风险", value: "2" },
      { label: "低风险", value: "1" }
    ]
  },
  {
    label: "交易环节",
    prop: "nodeCode",
    type: "SELECT",
    placeholder: "请选择交易环节",
    optionList: []
  },
  {
    label: "监察点名称",
    prop: "supervisionContent",
    type: "INPUT",
    placeholder: "请输入监察点名称"
  },
  {
    label: "监察预警原因",
    prop: "reason",
    type: "TEXTAREA",
    placeholder: "请输入监察预警原因"
  }
]);

const data = reactive({
  supervisionForm: {
    tenderProjectGuid: props.tenderProjectGuid
  }
});

let { supervisionForm } = toRefs(data);

// 监听标段选择变化
watch(() => data.supervisionForm.bidSectionGuid, (newVal) => {
  if (newVal) {
    // 根据选中的标段找到对应的项目ID
    const selectedBid = bidList.value.find(item => item.bidSectionGuid === newVal);
    if (selectedBid) {
      data.supervisionForm.tenderProjectGuid = selectedBid.tenderProjectGuid;
    }
  }
});

onMounted(() => {
    handleGetBidsectionList();
    updateOptionList();
});

async function updateOptionList() {
    const tradeStepOptions = await getOptionList('jgywlc');
    // 更新交易环节选项
    const tradeStepIndex = formContent.value.findIndex(item => item.prop === 'nodeCode');
    if (tradeStepIndex !== -1) {
        formContent.value[tradeStepIndex].optionList = tradeStepOptions;
    }
}

const handleGetBidsectionList = async (val) => {
    let info = {
        keyword: val ? val : ''
    }
    let data = await httpPost('/inviteTender/bidSection/getBidsectionListByMonitor', info);
    console.log('data', data);
    // 转换数据格式
    bidList.value = data.map(item => ({
        bidSectionGuid: item.bidSectionGuid,
        bidSectionName: item.bidSectionName,
        tenderProjectGuid: item.tenderProjectGuid // 添加项目ID
    }));
    
    // 更新标段列表选项
    const bidSectionIndex = formContent.value.findIndex(item => item.prop === 'bidSectionGuid');
    if (bidSectionIndex !== -1) {
        formContent.value[bidSectionIndex].optionList = bidList.value.map(item => ({
            label: item.bidSectionName,
            value: item.bidSectionGuid
        }));
    }
}

const isLoading = ref(false);
const supervisionFormRef = ref();

const FORM_RULES = {
  bidSectionGuid: [
    { required: true, message: '请选择标段', trigger: 'change' }
  ],
  level: [
    { required: true, message: '请选择监察预警类型', trigger: 'change' }
  ],
  nodeCode: [
    { required: true, message: '请选择交易环节', trigger: 'change' }
  ],
  supervisionContent: [
    { required: true, message: '请输入监察点名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  reason: [
    { required: true, message: '请输入监察预警原因', trigger: 'blur' },
    { min: 5, max: 500, message: '长度在 5 到 500 个字符', trigger: 'blur' }
  ]
};

const handleSubmit = async () => {
  if (!supervisionFormRef.value) return;
  await supervisionFormRef.value.validate(async (valid, fields) => {
    if (valid) {
      isLoading.value = true;
      try {
        await httpPost('/inviteTender/personIllegalRecord/add', supervisionForm.value);
        isLoading.value = false;
        ElMessage({
          message: "提交成功！",
          type: "success",
        });
        handleClose('refresh');
      } catch (error) {
        isLoading.value = false;
        ElMessage({
          message: error.message || "提交失败，请重试",
          type: "error",
        });
      }
    } else {
      console.log('表单验证失败:', fields);
    }
  });
};

const handleClose = (refresh) => {
  // 重置表单
  if (supervisionFormRef.value) {
    supervisionFormRef.value.resetFields();
  }
  // 通知父组件关闭当前页面
  emit("handleClose", refresh);
};
</script>

<style scoped lang="less">
#supervisionWarningForm {
  width: 100%;
  height: auto;
  padding: 20px;

  .supervision-form {
    background: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

    .unify-title {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 20px;
      color: #333;
    }

    :deep(.el-form-item) {
      margin-bottom: 18px;

      .el-form-item__label {
        font-weight: 500;
      }
    }

    .full-width {
      width: 100%;
    }
  }

  .btn-box {
    width: 100%;
    height: 50px;
    margin-top: 20px;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    gap: 20px;

    .my-supervision-btn {
      width: 120px;
    }
  }

  .my-span-delete {
    cursor: pointer;
    display: inline-block;
    vertical-align: -webkit-baseline-middle;
    margin-left: 20px;
    &:hover {
      color: red;
    }
  }
}
</style>