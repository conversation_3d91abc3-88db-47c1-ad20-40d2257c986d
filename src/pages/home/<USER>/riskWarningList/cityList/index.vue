<template>
  <div class="project-city-box">
    <div
      @click="handleSelectCity(item)"
      class="city-box"
      :title="item.name"
      :class="{ isActive: selectCityId == item.code }"
      v-for="(item, index) in cityList"
      :key="index"
    >
    <span class="step-span" style="cursor: pointer;display: block;position: relative;"  v-if="!item.children">{{ item.name }}</span>
      <el-popover placement="right-start" trigger="hover"  v-else>
          <template #reference>
           <span class="step-span" style="cursor: pointer;display: block;"  >{{ item.name }}</span>
          </template>
          <div class="project-city-box" >
            <div
              @click="handleSelectCity(item1)"
              class="city-box"
              style="    height: 28px;line-height: 28px;"
              :title="item1.name"
              :class="{ isActive: selectCityId == item1.code }"
              v-for="(item1, index) in item.children"
              :key="index"
            >
              <span class="step-span" style="cursor: pointer;"  >{{ item1.name }}</span>
            </div>
          </div>
      </el-popover>
    </div>
  </div>
</template>

<script setup>
import { defineProps } from "vue";
const emit = defineEmits(["selectCity"]);
 defineProps({
  selectCityId: {
    type: String,
    default: "",
  },
  cityList: {
    type: Array,
    default: [],
  },
});
const handleSelectCity = (item) => {
  emit("selectCity", item);
};
</script>
  <style scoped lang="less">
.project-city-box {
  width: 100%;
  height: 100%;
  overflow: auto;
  .city-box {
    width: 100%;
    height: 28px;
    line-height: 28px;
    text-align: center;
    color: #000;
    background: #ecf3fe;
    border-radius: 5px;
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 10px;
    cursor: pointer;
    overflow:hidden;/*内容超出后隐藏*/
    text-overflow:ellipsis;/*超出内容显示为省略号*/
    white-space:nowrap;/*文本不进行换行*/
    &:hover {
      color: #fff;
      background: #438bfa;
    }
  }

  .isActive {
    color: #fff;
    background: #438bfa;
  }
}
</style>
    