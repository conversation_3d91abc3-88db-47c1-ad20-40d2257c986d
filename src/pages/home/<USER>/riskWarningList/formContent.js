import { getOptionList } from "@/pages/project/projectBadBehavior/formOptionList";

export const FORM_CONTENT_SEARCH = [
    {
        label: "状态筛选",
        prop: "isRead",
        type: "SELECT",
        placeholder: "请选择状态",
        span: 12,
        disabled: false,
        optionList: [
            {
                label: "未读",
                value: 0,
            },
            {
                label: "已读",
                value: 1,
            },
        ]
    },
    {
        label: "搜索",
        prop: "keyword",
        type: "INPUT",
        placeholder: "请输入标段名称或标段编号",
        span: 24,
        disabled: false
    }
]

export const FORM_RULES = {
    isRead: [{ required: false, message: "请选择状态", trigger: "change" }],
    keyword: [{ required: false, message: "请输入搜索内容", trigger: "blur" }]
} 