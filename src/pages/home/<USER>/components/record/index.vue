<script setup lang="ts">
import { httpPost } from '@wl-fe/http'
const list = ref<any[]>([])
const queryParams = ref({
    keyword: '',
    searchType: ''
});
const emit = defineEmits(["searchEvent",]);
const getList = async () => {
  try {
    let response = await httpPost("/homePageController/getSearchHistoryList")
    list.value = response.filter((item: any) => item.searchField)
  } catch (error) {
  }
};
onMounted(getList);
const handleClick = (item: any) => {
    queryParams.value.keyword = item.searchField
    queryParams.value.searchType = Number(item.searchType)
    emit('searchEvent', queryParams.value);
}
</script>
<template>
    <CardWrapper class="record">
        <LabelTitle title="您最近查找..." />
        <div class="content">
            <div class="item" v-for="(item, index) in list" :key="index" @click="handleClick(item)">
                {{ item.searchField }}
            </div>
        </div>
    </CardWrapper>
</template>

<style scoped lang="less">
.record {
    flex: 1;
    flex-shrink: 0;
    margin-left: 24px;

    .content {
        margin-top: 24px;
        display: flex;
        flex-wrap: wrap;

        .item {
            cursor: pointer;
            color: #9B938A;
            padding: 4px 16px;
            border: 1px solid #EEEEEE;
            margin: 4px 8px 4px 0px;
            border-radius: 20px;

            &:hover {
                color: #1180FF;
                border: 1px solid #1180FF;
            }
        }
    }
}
</style>
