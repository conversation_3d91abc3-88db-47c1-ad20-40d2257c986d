<script setup lang="ts">
import { defineProps } from 'vue'
const props = defineProps({
    time: {
    type: Number,
    required: true,
  },
  count: {
    type: Number,
    required: true,
  },
})
</script>
<template>
    <cardWrapper>
        <div class="total">
            <div class="text">
            找到相关结果约
            <span class="highlight">{{ count || 0 }}</span>条，用时
            <span  class="highlight">{{ time || 0 }}</span>秒
        </div>
        <slot></slot>
        </div>
    </cardWrapper>
  </template>

  <style scoped lang="less">
  .total{
    padding: 8px;
    display: flex;
    align-items: center;
    font-size: 16px;
    .text{
        .highlight{
            margin: 0px 4px;
            color: red;
        }
    }
    .tags{
        margin-left: 24px;
        display: flex;
        cursor: pointer;
        .item{
            color: #1180FF;
            padding: 4px 8px;
            border: 1px solid #1180FF;
            margin-right: 6px;
            border-radius: 20px;
            &.active{
                background-color:#1992FF ;
                color: #fff;
            }
        }
    }
}
</style>
