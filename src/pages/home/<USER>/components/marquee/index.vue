<template>
    <div class="marquee-container">
        <div class="marquee" :style="{ '--speed': speed + 's' }" @mouseover="pause" @mouseout="resume">
            <div class="marquee-content">
                <!-- 使用 slot 插槽来传入滚动内容 -->
                <slot></slot>
                <!-- 复制一份内容以确保头尾衔接 -->
                <div class="marquee-content-clone">
                    <slot></slot>
                </div>
            </div>
        </div>
    </div>
</template>
  
<script setup>
import { ref, onMounted, onUnmounted } from 'vue'

const props = defineProps({
    speed: {
        type: Number,
        default: 5 // 默认滚动速度，单位秒
    }
})

const isPaused = ref(false)

const pause = () => {
    isPaused.value = true
}

const resume = () => {
    isPaused.value = false
}

// 你可以在这里添加额外的逻辑，比如监听窗口大小变化等

// 监听组件挂载和卸载，以处理可能的性能问题
onMounted(() => {
    // 在这里添加组件挂载后的逻辑（如果需要）
})

onUnmounted(() => {
    // 在这里添加组件卸载后的清理逻辑（如果需要）
})
</script>
  
<style scoped>
.marquee-container {
    overflow: hidden;
    white-space: nowrap;
}

.marquee-content {
    display: flex;
    flex-shrink: 0;
    /* 防止内容在动画过程中收缩 */
    /* 可能需要的其他样式，比如内边距、字体等 */
}

.marquee-content-clone {
    /* 隐藏克隆的内容，但在动画过程中需要它来实现头尾衔接 */
    visibility: hidden;
    position: absolute;
    top: 0;
    left: 100%;
    /* 初始位置在容器外部 */
}

.marquee {
    display: flex;
    animation: marquee var(--speed) linear infinite;
}

@keyframes marquee {
    0% {
        transform: translateX(0);
    }

    100% {
        transform: translateX(-50%);
        /* 根据内容宽度和容器宽度调整这个值 */
    }
}

.marquee:hover {
    animation-play-state: paused;
}

/* 在 JavaScript 中控制暂停和恢复，使用动态绑定类名 */
</style>
  
  <!-- 注意：上面的样式部分没有直接支持鼠标悬停暂停，因为我们需要在 JS 中动态控制 -->