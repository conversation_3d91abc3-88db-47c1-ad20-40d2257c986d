<template>
    <PageWrapper>
        <div class="project-list" v-loading="isLoading">
            <div class="project-content">
                <div class="project-list-box">
                    <div class="project-list-conotent">
                        <el-table
                                :data="tableData"
                                style="width: 100%"
                                :height="isShowAuditHeader ? 592 : 620"
                                :row-class-name="tableRowClassName"
                                class="my-table"
                        >
                            <el-table-column
                                    v-for="(item) in heads"
                                    :label="item.label"
                                    :prop="item.prop"
                                    :align="item.prop === 'content' ? 'left' : 'center'"
                            />
                        
                        </el-table>
                        <div class="pagination-box">
                            <el-button
                                    class="my-instruct-btn"
                                    round
                                    type="primary"
                                    plain
                                    @click="handleClose"
                            >关 闭
                            </el-button
                            >
                            <el-pagination
                                    class="pagination"
                                    background
                                    v-model:current-page="searchInfo.pageNum"
                                    v-model:page-size="searchInfo.pageSize"
                                    :size="searchInfo.pageSize"
                                    :total="total"
                                    :page-sizes="[100, 200, 300, 400]"
                                    layout="total, sizes, prev, pager, next, jumper"
                                    @size-change="handleSizeChange"
                                    @current-change="handleChangePage"
                            />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </PageWrapper>
</template>

<script setup>

import {httpGet, httpPost} from "@wl-fe/http/dist";
import {ElMessage} from "element-plus";
import {ref} from "vue";

const emit = defineEmits(["handleCloseDialog"]);
const handleClose = () => {
    emit("handleCloseDialog");
};

let searchInfo = reactive({
    pageNum: 1,
    pageSize: 10,
});
const isLoading = ref(false);
let total = ref(1);

const tableRowClassName = ({rowIndex}) => {
    if (rowIndex % 2 !== 0) {
        return "warning-row";
    }
    return "";
};
const handleSizeChange = (val) => {
    searchInfo.pageSize = val
    handleGetList();
};
const handleChangePage = (val) => {
    searchInfo.pageNum = val;
    handleGetList();
};
const platformOption = ref([]);
const qualificationOption = ref([]);


onMounted(() => {
  handleHead();
  handleGetList(); // 确保此处调用了接口
});

const tableData = ref([]);
let heads=reactive([])

const handleHead=async ()=>{
  isLoading.value = true;
  heads = await httpGet(
        `/system/version/heads`
   );
};

const handleGetList = async () => {
    let data = await httpGet(
        `/system/version/page?pageNum=${searchInfo.pageNum}&pageSize=${searchInfo.pageSize}`
    );
    total.value = data.total;
    tableData.value = data.rows;
    isLoading.value = false;
};

</script>
<style scoped lang="less">
.project-list {
  width: 100%;
  height: 100%;

  .project-search-box {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;

    .select {
      width: 200px;
      margin-right: 16px;
      border-radius: 50px;

      :deep(.el-select__wrapper) {
        border-radius: 50px;
      }
    }

    .back-btn {
      width: 90px;
    }

    .success-btn {
      width: 150px;
    }
  }

  .project-content {
    width: 100%;
    height: 60%;
    margin-top: 1%;
    display: flex;
    justify-content: space-between;

    .project-city {
      width: 8%;
      height: 100%;
      margin-right: 10px;
      overflow: auto;
      flex-shrink: 0;
    }

    .project-list-box {
      height: 100%;
      flex: 1;
      width: 0;
      display: flex;
      flex-direction: column;

      .radio-box {
        text-align: right;
      }

      .project-list-conotent {
        width: 100%;
        // height: 82%;
        // margin-top: 1%;
        flex: 1;
        height: 0;

        .pagination-box {
          margin-top: 20px;
          display: flex;
          flex-direction: row;
          justify-content: space-between;
        }
      }
    }
  }
}

.my-instruct-btn {
  margin-left: 42%;
  width: 10%;
}

.table-view {
  width: 20px;
  height: 20px;
  border-radius: 20px;
  border: 1px solid #3366ff;
  text-align: center;
  line-height: 22px;
  cursor: pointer;
  margin: 0 auto;
  color: #3366ff;
}

.my-img {
  width: 20px;
  margin-right: 5px;
}

.wjIcon {
  width: 30px;
  height: auto;
}

.my-img-yj {
  width: 30px;
}

.span-red {
  color: #ff0000;

  &::before {
    content: "●";
    color: #ff0000;
    font-size: 15px;
    margin-right: 5px;
  }
}

.span-orign {
  color: #FFA500;

  &::before {
    content: "●";
    color: #FFA500;
    font-size: 15px;
    margin-right: 5px;
  }
}

.span-yellow {
  color: #f5e208;

  &::before {
    content: "●";
      color: #f5e208;
    font-size: 15px;
    margin-right: 5px;
  }
}

.step-span {
  display: inline-block;
  width: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.status-red {
  color: red;
}

.status-green {
  color: green;
}

.status-orign {
  color: #f4871c;
}

/* 设置滚动条的样式 */
.project-city-box::-webkit-scrollbar {
  width: 6px;
  box-sizing: border-box;
}

/* 滚动条滑块 */
.project-city-box::-webkit-scrollbar-thumb {
  height: 5px;
  border-radius: 5px;
  background-color: #c2cede;
}

/*滚动槽*/
.project-city-box::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0);
  border-radius: 10px;
  background: #ffffff;
}

/* 滚动条滑块 鼠标悬浮 */
.project-city-box::-webkit-scrollbar-thumb:hover {
  background-color: #c2cede;
  // border: 1px solid rgba(0, 0, 0, 0.21);
  cursor: pointer;
}

.my-table {
  :deep(.el-table__header-wrapper) {
    .el-table__cell {
      background: #f0f6ff;
      height: 53px;
      color: rgba(0, 0, 0, 0.88);
    }
  }

  :deep(.el-table__row) {
    height: 53px;
  }

  :deep(.warning-row) {
    background: #f9fbff;
  }
}

:deep(.my-descriptions) {
  // background: #dceaff;
  font-size: 16px;
  width: 22%;
  color: #000;
  text-align: center;
  background: #f6faff;
}
</style>
