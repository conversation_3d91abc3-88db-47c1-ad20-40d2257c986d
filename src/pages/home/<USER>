<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0"
    />
  
    <title>ICraft Player Animation Demo</title>
    <style>
      body {
        margin: 0;
        padding: 0;
      }
      #container {
        width: 100%;
        height: 100vh;
        position: relative;
      }
    </style>
    <script src="../../../public/icraft-player.min.js"></script>
  </head>
  <body>
    <div id="container"></div>
  </body>
  <script>
    // iframe页面中的代码
window.addEventListener('message', (event) => {
  // 确保消息来自可信来源
  if (event.origin !== 'https://your-vue-app-domain.com') return;
  // 处理接收到的数据
  console.log('Received message from parent:', event.data);
});
    const player = new ICraftPlayer({
      src: import.meta.env.VITE_MG_ADMIN_JS +"test.iplayer",
      container: document.getElementById("container"),
      autoPlay: true,
      loop: true,
      onReady: (player) => {
        // const user = player.getElementsByName("User")?.[0];
        // if (user) {
        //   player.playAnimationByElementKey(user.key, {
        //     animationDuration: 100,
        //     animationType: ICraftPlayer.AnimationType.Rotate,
        //   });
        // }
      },
      onClick: (e) => {
        const { instance: Element } = e;
        if (!Element) {
          player?.cancelAnimation();
          return;
        }
        if (Element.typeName !== "line") {
          player?.playAnimationByElementKey(Element.key, {
            animationDuration: 3,
            animationType: ICraftPlayer.AnimationType.HeartBeat,
            animationShowTip: true,
          });
        } else {
          player?.playAnimationByElementKey(Element.key, {
            animationDuration: 3,
            animationType: ICraftPlayer.AnimationType.LoopFlow,
          });
        }
        console.log('ElementElement',Element.options.name);
        // 向父页面发送消息
        const data = {
            type: 'response',
            name: Element.options.name
          };
          window.parent.postMessage(data, '*');
      },
    });
  </script>
</html>