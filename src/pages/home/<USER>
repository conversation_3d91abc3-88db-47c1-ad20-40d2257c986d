<script setup>

import Search from "./search/index.vue"
import Top5 from './top5/index.vue'
import Events from './events/index.vue'
import Calendar from './calendar/index.vue'
import Project from './project/index.vue'
import studyPage from './studyPage/index.vue'
import riskWarning from './riskWarning/index.vue'
import superviseMap from './superviseMap.vue'
import map from "@/assets/home/<USER>";
defineOptions({
  name: "home",
})
// 获取当前路由实例
const route = useRoute();

// 监听路由变化
watch(() => route.path, (newPath, oldPath) => {
  console.log('路由发生变化，旧路径:', oldPath, '新路径:', newPath,route.query.type);
  if (oldPath === '/smart_regulation' && route.query.type) {
    handleShowMap()
  }
  // 在这里可以添加你需要执行的逻辑，例如根据新的路由路径更新页面内容
  // 比如，当路由变化时，隐藏搜索框
  // showSearch.value = false; 
}, {
  immediate: true // 立即执行一次回调，以便在组件挂载时就能处理当前路由
});

const showSearch = ref(false);
const handleFocusEvent = (value) => {
  showSearch.value = true;
};
const handleReturnEvent = (value) => {
  showSearch.value = false
}
const isShowMap = ref(false)
const handleShowMap = () => {
  isShowMap.value = !isShowMap.value
}
</script>
<template>
    <keep-alive>
      <PageWrapper style="position: relative;">
        <div v-show="!isShowMap">
          <Search :showSearch="showSearch" @focusEvent="handleFocusEvent" @returnEvent="handleReturnEvent"></Search>
            <div v-if="!showSearch">
              <Project></Project>
              <Top5></Top5>
              <div style="display: flex;margin-top: 16px;height: 305px;">
                <Events></Events>
                <riskWarning></riskWarning>
                <studyPage></studyPage>
              </div>
            </div>
            <div  v-if="!showSearch" class="map-img-box">
              <span class="img-text">监管地图</span>
              <img :src="map" alt="" class="map-img" @click="handleShowMap()" />
            </div>
        </div>
      <div :class="isShowMap ? 'showMap'  : 'noMap'" v-if="isShowMap"><superviseMap @backHome="handleShowMap()"></superviseMap></div>
      </PageWrapper>
    </keep-alive>
</template>
<style lang="less" scoped>
.map-img-box{
  position: absolute;
  top: 2%;
  right: 2%;
  cursor: pointer;
  .map-img{
    width: 40px;height: 40px;
  }
  .img-text{
    display: inline-block;
    vertical-align: middle;
    font-size: 18px;
    font-weight: 600;
    margin-right: 5px;
    color: #3366ff;
  }
}

.noMap{
  position: absolute;
  top: 0;
  right: 3000px;
  transition: all 0.5s;
  width: 100%;
  height: 100%;
}
.showMap{
  position: absolute;
  top: 0;
  right: 0px;
  transition: all 0.5s;
  width: 100%;
  height: 100%;
  text-align: center;
  overflow: hidden;
}
</style>
