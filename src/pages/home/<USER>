<script setup>
import useGloBalStore from "@/store/useGlobalStore";
import xt from "@/assets/home/<USER>";
import home from "@/assets/home/<USER>";
import dMap from "@/assets/home/<USER>";
import { getMenus } from "@/layout/constant";
import { computed } from 'vue';
const { sysUserModules } = useGloBalStore();
const emit = defineEmits(["backHome"]);
const router = useRouter();
const videoUrl = ref("");
const videoText = ref("");
const videoCover = ref("");
const lcImg = ref("");
const zyimg = ref("");
const type = ref("");
// const ICraftPlayer = window.ICraftPlayer
// setTimeout(() => {
//   const player = new ICraftPlayer({
//       src: import.meta.env.VITE_MG_ADMIN_JS +"test.iplayer",
//       container: document.getElementById("container"),
//       autoPlay: 'true',
//       loop: 'true',
//       onReady: (player) => {
//         // const user = player.getElementsByName("User")?.[0];
//         // if (user) {
//         //   player.playAnimationByElementKey(user.key, {
//         //     animationDuration: 100,
//         //     animationType: ICraftPlayer.AnimationType.Rotate,
//         //   });
//         // }
//       },
//       onClick: (e) => {
//         const { instance: Element } = e;
//         console.log('ElementElement',Element.options.name);
//          if (Element.options.name === "主体信息库") {
//           isShowVideo.value = true;
//         }
//         // if (!Element) {
//         //   player?.cancelAnimation();
//         //   return;
//         // }
//         // if (Element.typeName !== "line") {
//         //   player?.playAnimationByElementKey(Element.key, {
//         //     animationDuration: 3,
//         //     animationType: ICraftPlayer.AnimationType.HeartBeat,
//         //     animationShowTip: true,
//         //   });
//         // } else {
//         //   player?.playAnimationByElementKey(Element.key, {
//         //     animationDuration: 3,
//         //     animationType: ICraftPlayer.AnimationType.LoopFlow,
//         //   });
//         // }
//       },
//     });
// }, 1000);
const handleShowHome = () => {
  emit("backHome");
};
const handleClose = () => {
  isShowVideo.value = false;
};
const handleJump = () => {
  router.push({ path: `/${targetObject.value.key}` });
};
const isShowVideo = ref(false);
const selectMapValue = ref('2D视图')
const mapOptions = ['2D视图', '3D立体']
const targetObject = ref('')

// Add computed property for iframe URL
const iframeUrl = computed(() => {
  return `${import.meta.env.VITE_MG_ADMIN_JS}autoPlay.html`
})

// 监听iframe发送的消息
const handleMessage = (event) => {
  // 确保消息来自可信来源
  if (event.origin !== "http://***************:8887") return;

  // 处理接收到的数据
  console.log("Received message from iframe:", event.data.name);
  const mapObj = {
    '主体信息库': '主体信息',
    "自主决策子系统": "自主决策",
    '投标盲盒子系统': '投标集成',
    "自主决策子系统": "自主决策",
    '异议投诉子系统': '异议投诉',
    "开标评标子系统": "远程监管",
    '定标子系统': '远程监管',
    "合同履约子系统": "合同履约",
    "执法直报子系统": "执法检查",
    "评标专家评价子系统": "主体评价",
    "招标代理机构评价子系统": "主体评价",
    "项目管理系统": "项目管理",

  }
  handleChangeObject(mapObj[event.data.name]);
};
const handleChangeObject = (title) => {
  targetObject.value = sysUserModules.find(item => item.title === title);
  console.log(targetObject.value);
  videoUrl.value = targetObject.value.mapUrl;
  videoText.value = targetObject.value.serviceContent;
  lcImg.value = targetObject.value.flowChartUrl;
  zyimg.value = targetObject.value.flowInsUrl;
  type.value = 3
  isShowVideo.value = true;
};
const handleView = (code) => {
  type.value = code;
}
onMounted(() => {
  console.log('sysUserModules', sysUserModules);
  window.addEventListener("message", handleMessage);
});

onUnmounted(() => {
  window.removeEventListener("message", handleMessage);
});
</script>
<template>
  <div
    class="home-video"
    :class="isShowVideo ? 'slide-in-elliptic-bottom-fwd' : 'box-none'"
  >
    <!-- <div class="video-title"></div> -->
    <div class="my-video">
      <div class="video-box" v-if="type == 3">
        <VideoPlay
          v-show="true"
          :videoUrl="videoUrl"
          :videoCover="videoCover"
          :width="'100%'"
          :height="'98%'"
          :autoplay="true"
          :controls="true"
          :loop="false"
          :muted="false"
          preload="auto"
          :showPlay="true"
          :playWidth="96"
          zoom="cotain"
        />
      </div>
      <!-- <img v-if="type == 1 || type == 2" :src="type == 1 ? lcImg : zyimg" class="all-img" alt="" /> -->
      <el-image
       v-if="type == 1 || type == 2"
        class="all-img"
        :src="type == 1 ? lcImg : zyimg" 
          :zoom-rate="1.2"
          :max-scale="7"
          :min-scale="0.2"
          fit="cover"
          :preview-src-list="[type == 1 ? lcImg : zyimg]"
          :initial-index="0"
        />
      <div class="content-box">
        <div class="c-header">
          <img :src="xt" class="header-img" alt="" />
          <div class="text">系统功能介绍</div>
        </div>
        <div class="c-line"></div>
        <div class="text-box">
          <div class="c-content">
            {{ videoText }}
          </div>
        </div>
      </div>
    </div>
    <div class="video-footer">
      <div>
        <el-button plain color="#626aef" style="width: 150px" @click="handleView(1)" round
          >流程图</el-button
        >
        <el-button plain color="#626aef" @click="handleView(2)" style="width: 150px" round
          >操作指引</el-button
        >
        <el-button plain color="#626aef" @click="handleView(3)" style="width: 150px" round
          >操作视频</el-button
        >
      </div>
      <div>
        <el-button type="primary" style="width: 150px" @click="handleJump()" round
          >进入系统</el-button
        >
        <el-button type="danger" @click="handleClose()" style="width: 150px" round
          >关闭</el-button
        >
      </div>
    </div>
  </div>

  <iframe
    v-if="selectMapValue === '3D立体'"
    frameborder="0"
    style="width: 100%; height: 99%; position: relative"
    :src="iframeUrl"
  ></iframe>
  <div class="map-img-box" v-else >
    <img :src="dMap" class="map-img" style="" alt="系统架构图" usemap="#systemMap" id="mainImage" />
    <a href="#"
       class="hotspot-overlay"
       style="left: 510px; top: 290px; width: 40px; height: 60px;" 
       @click="handleChangeObject('主体信息')"
       title="点击查看 主体信息">
    </a>
    <a href="#"
       class="hotspot-overlay"
       style="left: 582px; top: 330px; width: 40px; height: 60px;" 
       @click="handleChangeObject('自主决策')"
       title="点击查看 自主决策">
    </a>

    <a href="#"
       class="hotspot-overlay"
       style="left: 652px; top: 370px; width: 40px; height: 60px;" 
       @click="handleChangeObject('投标集成')"
       title="点击查看 投标集成">
    </a>
    <a href="#"
       class="hotspot-overlay"
       style="left: 722px; top: 410px; width: 40px; height: 60px;" 
       @click="handleChangeObject('异议投诉')"
       title="点击查看 异议投诉">
    </a>
    <a href="#"
       class="hotspot-overlay"
       style="left: 792px; top: 450px; width: 40px; height: 60px;" 
       @click="handleChangeObject('远程监管')"
       title="点击查看 开标评标">
    </a>
    <a href="#"
       class="hotspot-overlay"
       style="left: 862px; top: 490px; width: 40px; height: 60px;" 
       @click="handleChangeObject('远程监管')"
       title="点击查看 定标">
    </a>
    <a href="#"
       class="hotspot-overlay"
       style="left: 932px; top: 530px; width: 40px; height: 60px;" 
       @click="handleChangeObject('合同履约')"
       title="点击查看 合同履约">
    </a>
    <a href="#"
       class="hotspot-overlay"
       style="left: 1002px; top: 570px; width: 40px; height: 60px;" 
       @click="handleChangeObject('执法检查')"
       title="点击查看 执法检查">
    </a>
    <a href="#"
       class="hotspot-overlay"
       style="left: 450px; top: 640px; width: 40px; height: 60px;" 
       @click="handleChangeObject('主体评价')"
       title="点击查看 专家评价">
    </a>
    <a href="#"
       class="hotspot-overlay"
       style="left: 156px; top: 470px; width: 40px; height: 60px;" 
       @click="handleChangeObject('主体评价')"
       title="点击查看 代理机构评价">
    </a>
    <a href="#"
       class="hotspot-overlay"
       style="left: 650px; top: 200px; width: 40px; height: 60px;" 
       @click="handleChangeObject('项目管理')"
       title="点击查看 项目管理">
    </a>
  </div>
  <!-- <img :src="home" alt="" class="home-img" @click="handleShowHome()" style="" /> -->
  <div class="home-img-box">
      <span class="img-text">数据看板</span>
      <img :src="home" alt="" class="home-img" @click="handleShowHome()" style="" />
  </div>
  <div class="custom-style">
    <el-segmented v-model="selectMapValue" :options="mapOptions" />
  </div>
</template>
<style lang="less" scoped>
.map-img-box{
  width: 75%;
  height: 100%;
  margin: 0 auto;
  position: relative;
  .map-img{
    width: 100%;
    // height: 100%;
  }
  .hotspot-overlay {
            position: absolute; /* 绝对定位 */
            // background-color: rgba(59, 130, 246, 0.3); /* 半透明蓝色背景 (Tailwind: bg-blue-500/30) */
            // border: 1px solid rgba(37, 99, 235, 0.5); /* 蓝色边框 (Tailwind: border border-blue-600/50) */
            border-radius: 4px; /* 圆角 */
            cursor: pointer;
            transition: background-color 0.2s ease; /* 添加过渡效果 */
            box-sizing: border-box; /* 边框包含在宽高内 */
            /* 确保叠加层不会意外捕获不需要的事件 */
            pointer-events: auto;
        }
        .hotspot-overlay:hover {
            // background-color: rgba(59, 130, 246, 0.5); /* 悬停时更深的背景 (Tailwind: hover:bg-blue-500/50) */
        }
}
.home-img-box{
  position: absolute;
  top: 2%;
  right: 2%;
  cursor: pointer;
  .home-img{
    width: 40px;height: 40px;
  }
  .img-text{
    display: inline-block;
    vertical-align: middle;
    font-size: 18px;
    font-weight: 600;
    margin-right: 5px;
    color: #3366ff;
  }
}
.home-video {
  width: 84%;
  height: 93%;
  background-color: #fff;
  position: absolute;
  top: 3%;
  left: 8%;
  transform: translate(-50%, -50%);
  z-index: 100;
  display: flex;
  align-items: center;
  justify-content: space-around;
  flex-direction: column;
  box-shadow: 0px 16px 48px 16px rgba(0, 0, 0, 0.08),
    0px 12px 32px rgba(0, 0, 0, 0.12), 0px 8px 16px -8px rgba(0, 0, 0, 0.16);
  .video-title {
    width: 95%;
    height: 8%;
    background-color: #ccc;
  }
  .my-video {
    width: 95%;
    height: 80%;
    display: flex;
    align-items: center;
    justify-content: space-around;
    .all-img{
      height: 90%;
      margin-right: 10px;
      // height: 100%;
    }
  }
  .video-footer {
    width: 95%;
    height: 8%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-right: 20px;
  }
  .video-box {
    width: 55%;
    height: 80%;
    .m-video {
      display: inline-block;
      position: relative;
      background: #fff;
      cursor: pointer;
      width: 100%;
      height: 100%;
    }
  }
  .content-box {
    width: 35%;
    height: 80%;
    .c-header {
      height: 6%;
      display: flex;
      align-items: center;

      .header-img {
        width: 25px;
        height: 25px;
        margin-left: 10px;
      }
      .text {
        font-size: 22px;
        color: #333;
        margin-left: 10px;
        font-weight: 600;
      }
    }
    .c-line {
      width: 100%;
      height: 2px;
      background-color: #000;
      margin: 10px 0px;
    }
    .text-box {
      overflow-y: auto;
      .c-content {
        font-size: 16px;
        color: #333;
        padding: 5px;
        text-indent: 2em;
        line-height: 28px;
        text-align: left;
      }
    }
  }
}
.box-none {
  display: none;
}
.slide-in-elliptic-bottom-fwd {
  animation: slide-in-elliptic-bottom-fwd 0.7s
    cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
}
@keyframes bounce-in-fwd {
  0% {
    -webkit-transform: scale(0);
    transform: scale(0);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
    opacity: 0;
  }
  38% {
    -webkit-transform: scale(1);
    transform: scale(1);
    -webkit-animation-timing-function: ease-out;
    animation-timing-function: ease-out;
    opacity: 1;
  }
  55% {
    -webkit-transform: scale(0.7);
    transform: scale(0.7);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
  }
  72% {
    -webkit-transform: scale(1);
    transform: scale(1);
    -webkit-animation-timing-function: ease-out;
    animation-timing-function: ease-out;
  }
  81% {
    -webkit-transform: scale(0.84);
    transform: scale(0.84);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
  }
  89% {
    -webkit-transform: scale(1);
    transform: scale(1);
    -webkit-animation-timing-function: ease-out;
    animation-timing-function: ease-out;
  }
  95% {
    -webkit-transform: scale(0.95);
    transform: scale(0.95);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
  }
  100% {
    -webkit-transform: scale(1);
    transform: scale(1);
    -webkit-animation-timing-function: ease-out;
    animation-timing-function: ease-out;
  }
}
@keyframes slide-in-elliptic-bottom-fwd {
  0% {
    transform: translateY(600px) rotateX(30deg) scale(0);
    transform-origin: 50% 100%;
    opacity: 0;
  }
  100% {
    transform: translateY(0) rotateX(0) scale(1);
    transform-origin: 50% -1400px;
    opacity: 1;
  }
}
</style>
<style scoped>
.custom-style{
  position: absolute;
  bottom: 1%;
  right: 1%;
}
.custom-style .el-segmented {
  --el-segmented-item-selected-color: var(--el-text-color-primary);
  --el-segmented-item-selected-bg-color: #ffd100;
  --el-border-radius-base: 16px;
}
</style>
