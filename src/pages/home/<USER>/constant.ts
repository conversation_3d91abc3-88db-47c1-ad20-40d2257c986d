
import { ElButton } from 'element-plus';
export const calendarFormat = "YYYY-MM-DD"

export const getColumns = (onLink: (data: any) => void) => {
    return [
        {
            title: '标段唯一标识码',
            dataIndex: 'bidSingleCode',
            width: 140
        },
        {
            title: '标段名称',
            dataIndex: 'bidSectionName',
        }
        ,
        {
            title: '操作',
            width: 100,
            dataIndex: 'oper',
            render: (record: any, row: any, index: any) => {
                // 使用 h 函数创建 Element Plus 的 Button 组件
                return h(ElButton, {
                  onClick: () => onLink(record, row, index), // 使用 onClick 而不是 @click
                  type: 'text',
                }, '关联');
              }
        }

    ]
}


export const projectDetailColumns=[
    {
        title:"标段编号",
        dataIndex:"bidSectionCode"
    },
    {
        title:"标段名称",
        dataIndex:"bidSectionName"
    },
    {
        title:"招标人",
        dataIndex:"tendereeName"
    },
    {
        title:"招标方式",
        dataIndex:"tenderMode"
    },
    {
        title:"开标时间",
        dataIndex:"biddingTime"
    },
    {
        title:"代理机构",
        dataIndex:"dailiName"
    }
]