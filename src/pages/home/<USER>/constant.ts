import { MODULE_TYPE_ARCHIVE_INFORMATION, MODULE_TYPE_CONTRACT_PERFORMANCE, MODULE_TYPE_INFORMATION_DISCLOSURE, MODULE_TYPE_LAW_CHECK, MODULE_TYPE_OBJECTION_COMPLAINT, MODULE_TYPE_PROJECT, MODULE_TYPE_SUBJECT, MODULE_TYPE_SUBJECT_EVALUATION,MODULE_TYPE_AGENT_EVALUATION, MODULE_TYPE_TENDER, MODULE_TYPE_TRAINING_EXAMINATION } from "@/constant/module";
import { ProjectItemType } from "./type";
import { message } from "ant-design-vue";
export const MAX_ROW_COUNT = 5 // 最大行数
export const MAX_COLUMN_COUNT = 10 // 最大列数
const htly =  `/${import.meta.env.VITE_PROXY_SECMENU}/svg/htly.svg`;
const tbjc =  `/${import.meta.env.VITE_PROXY_SECMENU}/svg/tbjc.svg`;
const xmgl =  `/${import.meta.env.VITE_PROXY_SECMENU}/svg/xmgl.svg`;
const xxgk =  `/${import.meta.env.VITE_PROXY_SECMENU}/svg/xxgk.svg`;
const yyts =  `/${import.meta.env.VITE_PROXY_SECMENU}/svg/yyts.svg`;
const zfjc =  `/${import.meta.env.VITE_PROXY_SECMENU}/svg/zfjc.svg`;
const ztpj =  `/${import.meta.env.VITE_PROXY_SECMENU}/svg/ztpj.svg`;
const ztxx =  `/${import.meta.env.VITE_PROXY_SECMENU}/svg/ztxx.svg`;
export const DEFAULT_PROJECT_INDEX: ProjectItemType[] = [
    {
        value: MODULE_TYPE_PROJECT,
        title: '项目管理',
        bg:xmgl,
        children: [
            {
                value: "zbjhNum",
                title: '招标计划',
                isShow: true
            },
            {
                value: "xmdjNum",
                title: '招标备案',
                isShow: true
            },
            {
                value: "ggfbNum",
                title: '公告发布',
                isShow: true
            },
            {
                value: "wjscNum",
                title: '文件审查'
            },
            {
                value: "cqdyNum",
                title: '澄清答疑'
            },
            {
                value: "kbpbNum",
                title: '开标评标',
                isShow: true
            },
            {
                value: "gsfbNum",
                title: '公示发布'
            },
            {
                value: "zbtzNum",
                title: '中标通知'
            },
            {
                value: "smbgNum",
                title: '书面报告'
            },
            {
                value: "tszqNum",
                title: '特殊情况'
            }
        ]
    },
    {
        value: MODULE_TYPE_SUBJECT,
        title: '主体信息',
        bg:ztxx,
        children: [
            {
                value: 'zbrNum',
                title: '招标人',
                isShow:false
            },
            {
                value: 'tbrNum',
                title: '投标人',
                isShow:false
                
            },
            {
                value: 'dljgNum',
                title: '招标代理机构',
                isShow:false

            },
            {
                value: 'pbzjNum',
                title: '评标专家',
                isShow:false

            },
            {
                value: 'dhyNum',
                title: '待核验',
                isShow:false

            }
        ]
    },
    {
        value: MODULE_TYPE_TENDER,
        title: '投标集成',
        bg:tbjc,
        children: [
            {
                value: "allNum",
                title: '全部'
            },
            {
                value: "zzjxNum",
                title: '进行中'
            },
            {
                value: "ywcNum",
                title: '已完成'
            },
        ]
    },
    {
        value: MODULE_TYPE_OBJECTION_COMPLAINT,
        title: '异议投诉',
        bg:yyts,
        children: [
            {
                value: "tousuCont",
                title: '投诉项目'
            },
            {
                value: "tousuYwc",
                title: '已处理投诉'
            },
            {
                value: "tousuWwc",
                title: '待处理投诉'
            },
            {
                value: "yiyiWwc",
                title: '待处理异议'
            },
            {
                value: "yiyiCont",
                title: '异议项目'
            },
            {
                value: "yiyiYwc",
                title: '已答复异议'
            },
        ]
    },
    {
        value: MODULE_TYPE_CONTRACT_PERFORMANCE,
        title: '合同履约',
        bg:htly,
        children: [
            {
                value: "allNum",
                title: '全部'
            },
            {
                value: "signNum",
                title: '已签订'
            },
            {
                value: "overNum",
                title: '履约完成'
            },
            {
                value: "noSignNum",
                title: '未签订'
            },
            {
                value: "perNum",
                title: '履约中'
            },
           
        ]
    },
    {
        value: MODULE_TYPE_LAW_CHECK,
        title: '执法检查',
        bg:zfjc,
        children: [
            {
                value: "zxtjbd",
                title: '自行添加标段',
                isShow:false
            },
            {
                value: "ywczcbd",
                title: '已完成自查标段',
                isShow:false
            },
            {
                value: "fxwtbd",
                title: '发现问题标段'
            },
            {
                value: "xzcfbd",
                title: '行政处罚标段'
            },
            {
                value: "cfdwsl",
                title: '处罚单位数量'
            },
            {
                value: "cfrysl",
                title: '处罚人员数量'
            },
            {
                value: "xyzgbg",
                title: '信用中国曝光',
                isShow:false
            },
            // {
            //     value: "wzhqbd",
            //     title: '网站获取标段',
            //     isShow:false
            // },
        ]
    },
    {
        value: MODULE_TYPE_INFORMATION_DISCLOSURE,
        title: '信息公开',
        bg:xxgk,
        children: [
            {
                value: 1,
                title: '公示单位',
                isShow:false
            },
            {
                value: 2,
                title: '公示中信息',
                isShow:false
            },
            {
                value: 3,
                title: '被投诉',
                isShow:false
            },
            {
                value: 3,
                title: '已处理投诉',
                isShow:false
            },
        ]
    },
    {
        value: MODULE_TYPE_SUBJECT_EVALUATION,
        title: '主体评价',
        bg:ztpj,
        children: [
            {
                value: 1,
                title: '已受理',
                isShow: false,

            },
            {
                value: 2,
                title: '未受理',
                isShow: false,

            },
            {
                value: 3,
                title: '已处理',
                isShow: false,

            },
            {
                value: 3,
                title: '未处理',
                isShow: false,

            },
        ]
    },
	{
        value: MODULE_TYPE_AGENT_EVALUATION,
        title: '代理评价',
        bg:ztpj,
        children: [
            {
                value: 1,
                title: '已受理',
                isShow: false,

            },
            {
                value: 2,
                title: '未受理',
                isShow: false,

            },
            {
                value: 3,
                title: '已处理',
                isShow: false,

            },
            {
                value: 3,
                title: '未处理',
                isShow: false,

            },
        ]
    },
	
    // {
    //     value: MODULE_TYPE_TRAINING_EXAMINATION,
    //     title: '人才培养',
    //     children: [
    //         {
    //             value: 1,
    //             title: '申请培训单位',
    //             isShow:false
    //         },
    //         {
    //             value: 2,
    //             title: '申请人员',
    //             isShow:false
    //         },
    //         {
    //             value: 3,
    //             title: '创建课程',
    //             isShow:false
    //         },
    //         {
    //             value: 3,
    //             title: '已完成',
    //             isShow:false
    //         },
    //     ]
    // },
    // {
    //     value: MODULE_TYPE_ARCHIVE_INFORMATION,
    //     title: '档案信息',
    //     children: [
    //         {
    //             value: 1,
    //             title: '归档总数',
    //             isShow: false,


    //         },
    //         {
    //             value: 2,
    //             title: '归档比例',
    //             isShow: false,

    //         },
    //         {
    //             value: 3,
    //             title: '借出档案',
    //             isShow: false,

    //         },
    //         {
    //             value: 3,
    //             title: '销毁档案',
    //             isShow: false,

    //         }
    //     ]
    // }
]


export const transferList = (list: ProjectItemType[]) => {
    // eslint-disable-next-line no-unsafe-optional-chaining
    return list.filter(item => ((item.isShow ?? true) && ((item.children || [])?.filter(item => {
        return item.isShow ?? true
    })).length > 0))
}

export const checkMaxShowList = (list: ProjectItemType[]) => {
    let isValid = true
    let totalColumns = 0
    let maxRows = 0
    list.forEach(item => {
        const children = item.children?.filter(item => item.isShow ?? true)
        if (children && children.length > 0) {
            const columnCount = Math.ceil(children.length / MAX_ROW_COUNT)
            totalColumns = totalColumns + columnCount
            const rows = children.length / columnCount
            if (rows >= maxRows) {
                maxRows = rows
            }

        }
    })
    if (totalColumns > MAX_COLUMN_COUNT) {
        isValid = false
        message.warning(`当前列数已超上限${MAX_COLUMN_COUNT}，为了您的体验请进行删减！`)
    }
    if (maxRows > MAX_ROW_COUNT) {
        isValid = false
        message.warning(`当前行数已超上限${MAX_ROW_COUNT}，为了您的体验请进行删减！`)
    }
    return isValid
}
