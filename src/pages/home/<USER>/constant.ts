

export const EVENT_TYPE_TO_DO ='1'  // 代办
export const EVENT_TYPE_TO_READ='2' // 待阅

export const eventTypeList=[
    {
        key:EVENT_TYPE_TO_DO,
        label:"待办事项"
    }
    // {
    //     key:EVENT_TYPE_TO_READ,
    //     label:"待阅"
    // }
]
const mockData=[
    {
        title:"11",
        content:" 【招标备案】白沙河路跨中央公园桥工程总承包（EPC）",
        link:""
    },
    {
        title:"11",
        content:" 【招标备案】辽宁省沈抚改革创新示范区-总部基地市政基础设施建设工程总承包（EPC）   ",
        link:""

    },
    {
        title:"11",
        content:" 【招标备案】辽宁公安司法管理干部学院基础设施改造项目全过程咨询管理服务  ",
        link:""

    },
    {
        title:"11",
        content:" 【招标备案】瓦房店市主城区排水防涝通道治理及设施改造项目全过程造价       ",
        link:""
    },
    {
        title:"11",
        content:" 【招标备案】阜新市主城区雨污分流提升改造一期工程设备采购一标段",
        link:""

    }
]
export const eventMap:{[key:string]:any}={
    [EVENT_TYPE_TO_DO]:mockData,
    [EVENT_TYPE_TO_READ]:[...mockData].reverse()
}
