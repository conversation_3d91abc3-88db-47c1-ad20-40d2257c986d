<template>
  <div class="calendar">
    <div class="header bg-content">
      <img :src="dingIcon" alt="" class="icon" />
        系统操作指引专栏
    </div>
    <div class="content">
      <div class="list">
        <div
          class="item"
          v-for="(item, index) in videoList"
          :key="index"
          @click="openUrl(item)"
        >
          {{ index + 1 }}. {{ item.systemName }}
        </div>
      </div>
    </div>
  </div>
  <allDialogView ref="allDialogViewEl" :dialogTitle="dialogTitle" :dialogWidth="'70%'">
    <template #content>
      <div style="width: 100%; height: 700px;text-align: center">
        <VideoPlay
          v-show="true"
          :videoUrl="videoUrl"
          :videoCover="videoCover"
          :width="1200"
          :height="'98%'"
          :autoplay="true"
          :controls="true"
          :loop="false"
          :muted="false"
          preload="auto"
          :showPlay="true"
          :playWidth="96"
          zoom="cotain"
        />
      </div>
      <span class="dialog-footer">
        <el-button
          type="primary"
          round
          style="width: 100px"
          @click="handleClose()"
          >关闭</el-button
        >
      </span>
    </template>
  </allDialogView>
</template>

<script setup>
import VideoPlay from "@/components/VideoPlay.vue";
import dingIcon from "@/assets/home/<USER>";
import { httpGet } from "@wl-fe/http/dist";
const dialogTitle = ref("");
const allDialogViewEl = ref();
const videoUrl = ref("");
const videoCover = ref("");

onMounted(async () => {
  getMaxListeners();
});
const videoList = ref([]);
const getMaxListeners = async () => {
  const result = await httpGet("api/sysOperationVideo/list");
  console.log("resultresult", result);
  videoList.value = result
};
const openUrl = async (item) => {
  if (item.systemType != 'MP4') {
    //   window.location.href = item.website;
      window.open(item.website,"_blank")
  } else {
    allDialogViewEl.value.showDialog = !allDialogViewEl.value.showDialog;
    videoUrl.value= item.website
    dialogTitle.value = item.systemName;
  //     ElMessage.error("暂无跳转链接!")
  }
};
const handleClose = () => {
  allDialogViewEl.value.showDialog = !allDialogViewEl.value.showDialog;
};
</script>

<style lang="less" scoped>
.calendar {
  flex: 0.6;
  border: 1px solid rgba(175, 194, 221, 1);
  margin-right: 20px;
  border-radius: 8px;
  min-height: 100%;
  overflow: hidden;
  .header {
    display: flex;
    align-items: center;
    background: rgba(224, 234, 250, 1);
    padding: 0px 20px;
    border-radius: 8px 8px 0px 0px;
    height: 46px;
     font-size: 16px;
    .icon {
      width: 24px;
      height: auto;
      margin-right: 6px;
      position: relative;
    }
  }

  .content {
    height: calc(100% - 55px);
    overflow-y: auto;
    .list {
      background-color: #fff;
      padding: 20px;

      .item {
        cursor: pointer;
        line-height: 44px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        font-size: 16px;
        &:hover {
          color: #3366ff;
          text-decoration: underline;
        }
      }
    }
  }
}
.dialog-footer {
  display: flex;
  justify-content: center;
}
</style>