<script setup lang="ts" >
import { httpPost } from "@wl-fe/http/dist";
import { Calendar as ACalendar } from 'ant-design-vue';
import { Drawer as ADrawer } from 'ant-design-vue';
import dingIcon from '@/assets/home/<USER>';
import Content from './content/index.vue';
const date = ref();
const open = ref(false);
const currentDay = ref('')
const records = ref([])

import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import { calendarFormat } from './constant';
dayjs.locale('zh-cn')
const getRecord = async () => {
    const result = await httpPost("/sysMemorandumController/getMemorandumList", {})
    records.value = result
    open.value = false
}
const onOpen = async () => {
    const result = await httpPost("/sysMemorandumController/getMemorandumList", {})
    records.value = result
    open.value = false
    if(hasTodayDate(records.value,new Date()) && !sessionStorage.getItem(dayjs(new Date()).format('YYYY-MM-DD'))){
        open.value = true;
        onSelect(dayjs(new Date()))
        sessionStorage.setItem(dayjs(new Date()).format('YYYY-MM-DD'), '1')
    }
}
function hasTodayDate(dataArray, todayDate) {
  return dataArray.some(item =>dayjs(item.rivetingTime).format(calendarFormat)  === dayjs(todayDate).format(calendarFormat));
}
onMounted(() => {
    //  setList([{a:2}])
    onOpen()
})

const checkNote = (current: Dayjs) => {
    let hasNote = false
    if (records.value.find(item => {
        return dayjs(item.rivetingTime).format(calendarFormat) === dayjs(current).format(calendarFormat)
    })) {
        hasNote = true
    }
    return hasNote
}
const onSelect = (date: any) => {
    open.value = true
    currentDay.value = date.format(calendarFormat)
}
</script>
<template>
    <div class="calendar">
        <div class="header">
            备忘录
            <!-- <img src={dingIcon} alt='' className={styles.icon} /> -->
            <img :src="dingIcon" alt='' class="icon" />
        </div>
        <div class="content">
            <a-calendar v-model:date="date" :fullscreen="false" :dateFullCellRender="dateFullCellRender" mode="month"
                :defaultValue="dayjs()">
                <template #dateFullCellRender="{ current: current }">
                    <div @click="open = true; onSelect(dayjs(current))" class="date"
                        :class="{ current: dayjs().isSame(current, 'date'), note: checkNote(current) }">
                        <div class="content">{{ current.date() }}</div>
                    </div>
                </template>
            </a-calendar>
        </div>

        <a-drawer v-model:open="open" title="备忘录" width="35%" placement="right">
            <Content v-if="open" @onOk="getRecord" :records="records" :currentDay="currentDay"></Content>
        </a-drawer>
    </div>
</template>

<style scoped lang="less">
.calendar {
    flex: 1;
    border: 1px solid rgba(175, 194, 221, 1);
    border-radius: 8px;
    height: 100%;

    .header {
        display: flex;
        align-items: center;
        background: rgba(224, 234, 250, 1);
        padding: 0px 20px;
        border-radius: 8px 8px 0px 0px;
        height: 46px;

        .icon {
            width: 12px;
            height: auto;
            margin-left: 6px;
            position: relative;
            top: -8px;
        }
    }

    .content {
        .date {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 22px;

            &:hover {
                color: rgb(22, 119, 255);
                text-decoration: underline;
            }

            >.content {
                width: 24px;
                height: 24px;
                border-radius: 50%;
                position: relative;
                z-index: 2;
            }

            &.note {
                >.content {
                    border: 1px solid rgba(255, 97, 97, 1);
                    background: rgba(248, 142, 57, 1);

                    &::before {
                        content: "";
                        background: url('/home/<USER>');
                        background-size: 100% 100%;
                        position: absolute;
                        display: block;
                        width: 12px;
                        height: 14px;
                        top: -10px;
                        right: -8px;
                        z-index: 1;
                    }
                }
            }

            &.current {
                >.content {
                    background: rgba(57, 129, 248, 1);
                    color: #fff;
                }
            }
        }
    }
}

:deep(.ant-picker-calendar-mode-switch) {
    display: none
}

// :deep(:where(.css-dev-only-do-not-override-19iuou).ant-picker-calendar .ant-picker-cell .ant-picker-cell-inner) {
//     width: 24px;
//     height: 24px;
//     border-radius: 50%;
// }

// :deep(:where(.css-dev-only-do-not-override-19iuou).ant-picker-calendar .ant-picker-cell-in-view.ant-picker-cell-today .ant-picker-cell-inner::before) {
//     border: 0
// }

:deep(.ant-picker-calendar-mini .ant-picker-content) {
    height: 0!important;
}

:deep(.ant-picker-calendar .ant-picker-calendar-header) {
    padding: 8px 0
}
</style>
  