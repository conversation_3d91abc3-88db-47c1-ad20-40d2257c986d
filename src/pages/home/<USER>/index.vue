<template>
  <div class="calendar">
    <div class="header bg-content">
      <div style="font-size:16px;">
        <img :src="dingIcon" alt=""  class="icon" />
        风险预警
      </div>
      <span class="more" @click="handleMore()">更多</span>
    </div>
    <div class="content">
      <div class="list" v-if="videoList.length != 0">
        <div
          class="item"
          v-for="(item, index) in videoList"
          :key="index"
          @click="openUrl(item)"
        >
          <span
            class="my-span span1"
            :class="{ 'font-blod': item.isRead == 0 }"
            :title="item.tenderProjectName"
          >
            {{ index + 1 }}. {{ item.tenderProjectName }}</span
          >
          <span
            class="my-span span2"
            :class="{ 'font-blod': item.isRead == 0 }"
            :title="item.eventsContent"
          >
            {{ item.eventsContent }}</span
          >
          <span
            class="my-span span3"
            :class="{ 'font-blod': item.isRead == 0 }"
          >
            <img
              :src="item.isRead == 0 ? nReader : sReader"
              alt=""
              class="wjIcon"
            />
          </span>
          <span
            class="my-span span4"
            :class="{ 'font-blod': item.isRead == 0 }"
            >{{ item.riskTime }}</span
          >
        </div>
      </div>
      <div class="listNone" v-else>
        <el-empty :image='none' :image-size="100" description="暂无内容" />
      </div>
    </div>
  </div>
  <allDialogView
    ref="allDialogViewEl"
    :dialogTitle="'风险预警'"
    :dialogWidth="'95%'"
  >
    <template #content>
      <riskWarningList
        v-if="allDialogViewEl.showDialog"
        @handleCloseDialog="handleMore"
      ></riskWarningList>
    </template>
  </allDialogView>
  <el-drawer
    custom-class="my-drawer"
    modal-class="my-drawer-detail"
    :append-to-body="false"
    v-model="isShowDrawer"
    v-if="isShowDrawer"
    size="50%"
    :show-close="false"
  >
    <template #header>
      <h2></h2>
    </template>
    <div class="project-detail-info">
      <detailModule
        v-for="(item, index) in supervisionList"
        :key="index"
        :headerTitle="item['监察点名称']"
        :moduleInfo="item"
      ></detailModule>
    </div>
  </el-drawer>
</template>

<script setup>
import dingIcon from "@/assets/home/<USER>";
import nReader from "@/assets/home/<USER>";
import sReader from "@/assets/home/<USER>";
import riskWarningList from "./riskWarningList";
import { httpGet, httpPost } from "@wl-fe/http/dist";
const allDialogViewEl = ref();
onMounted(async () => {
  getMaxListeners();
});
const videoList = ref([]);
const isShowDrawer = ref(false);
const supervisionList = ref([]);
const none =  `/${import.meta.env.VITE_PROXY_SECMENU}/bid/none1.png`;
const getMaxListeners = async () => {
  const result = await httpPost("/riskController/getRiskEventsList", {
    isRead: "",
  });
  videoList.value = result.rows;
};
const handleMore = () => {
  allDialogViewEl.value.showDialog = !allDialogViewEl.value.showDialog;
};

const openUrl = async (row) => {
  if (row.illegalType == 1) {
    isShowDrawer.value = true;
    supervisionList.value = row.supervisionList;
    await httpGet("/riskController/updateRiskReadStatus/" + row.riskGuid);
    await getMaxListeners();
  } else {
    const params = {
      objectNo: row.illegalRecordGuid,
      platFormCode: row.platformCode,
    };
    const res = await httpPost("/subSysComplaint/skipUrl", params);
    window.open(res.msg, "_blank");
    return;
  }
};
</script>

<style lang="less" scoped>
.calendar {
  flex: 1;
  border: 1px solid rgba(175, 194, 221, 1);
  margin-right: 20px;
  border-radius: 8px;
  min-height: 100%;
  overflow: hidden;
  .header {
    display: flex;
    align-items: center;
    background: rgba(224, 234, 250, 1);
    padding: 0px 20px;
    border-radius: 8px 8px 0px 0px;
    height: 46px;
    justify-content: space-between;
    .icon {
      width: 24px;
      height: auto;
      margin-right: 6px;
      position: relative;
      vertical-align: text-top;
      font-size: 16px;
    }
    .more {
      cursor: pointer;
      color: #3366ff;
      font-size: 14px;
    }
  }

  .content {
    height: calc(100% - 66px);
    overflow-y: auto;
    .list {
      background-color: #fff;
      padding: 20px;

      .item {
        cursor: pointer;
        line-height: 34px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        font-size: 16px;
        &:hover {
          color: #3366ff;
          text-decoration: underline;
        }

        .my-span {
          display: inline-block;
          width: 40%;
          text-overflow: ellipsis;
          white-space: nowrap;
          overflow: hidden;
          margin: 0px 3px;
          text-align: center;
          .wjIcon {
            width: 30px;
            height: auto;
          }
        }
        .font-blod {
          //font-weight: bold;
        }
        .span1 {
          text-align: left;
        }
        .span2 {
          width: 25%;
        }
        .span3 {
          width: 10%;
        }
        .span4 {
          width: 20%;
        }
      }
    }
    .listNone{
      width:100%;
      height: 100%;
    }
  }
}
.dialog-footer {
  display: flex;
  justify-content: center;
}
</style>
<style >
 .my-descriptions {
    font-size: 16px !important;
    width: 22% !important;
    color: #000 !important;
    text-align: center !important;
    background: #f6faff !important;
  }
</style>