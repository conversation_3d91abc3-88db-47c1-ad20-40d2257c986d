

<script setup lang="ts">
import { List as AList } from 'ant-design-vue';
// import { groupBy } from 'lodash-es';
import { httpPost } from "@wl-fe/http/dist";
import { httpGet } from "@wl-fe/http"
import { getSearchTypes } from "./constant"
import Total from "./components/total/index.vue"
import Record from "./components/record/index.vue"
import { getModuleMap } from "@/constant/module"
const searchTypes = getSearchTypes
const queryParams = reactive({
  searchType: searchTypes[0].value,
  keyword: ''
});
const spinning = ref(false);
const responseTime = ref(0);
const props = defineProps(['showSearch']);
const moduleMap = getModuleMap()
import { openUrl } from "@/utils"
const jump = async (item: any) => {
  const { urlType, businessCode } = item
  const singlePointUrl = moduleMap[urlType].singleUrl
  const result = await httpGet(`${singlePointUrl}?objectNo=${businessCode}`, null, {
    transferResult: (result) => {
      return result
    }
  })
  openUrl(result)
}
const handleFocusEvent = () => {
  list.value = []
  responseTime.value = 0
  emit('focusEvent');
}
const handleReturn = () => {
  queryParams.keyword = ''
  emit('returnEvent');
}
// const list = ref<any[]>([])
interface DataItem {
  title: string,
  type: string
}
const list = ref<DataItem[]>([])
// const currentKey = ref(result[0]?.type || '');
// const groupedResult = computed(() => groupBy(result, 'type'));
// const list = computed(() => groupedResult.value[currentKey.value] || result);
const emit = defineEmits(["focusEvent", "returnEvent"]);
async function onSearch(params: any) {
  const queryParams = {
    searchType: params.searchType,
    keyword: params.keyword
  }
  spinning.value = true;
  const startTime = Date.now();
  try {
    // 使用 axios 发送 GET 请求到指定的 URL
    const response = await httpPost("/homePageController/globalSearchData", queryParams) || [];
    // 将获取到的数据保存到响应式数据中
    spinning.value = false;
    list.value = response;
    if (list.value.length > 0) {
      const endTime = Date.now();
      responseTime.value = Math.round((endTime - startTime) / 1000)
    }


  } catch (error) {
    spinning.value = false;
  }
}
// onMounted(fetchData);
</script>

<template>
  <div class="search">
    <div class="header">
      <el-select class="select" v-if="showSearch" style="width: 95px;margin-right: 16px;" v-model="queryParams.searchType"
        placeholder="Select" size="large">
        <el-option v-for="item in searchTypes" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-input v-model="queryParams.keyword" style="width: 500px" class="input-with-select" @focus="handleFocusEvent">
        <template #append>
          <el-button size="large" @click="onSearch(queryParams)"> 搜 索 </el-button>
        </template>
      </el-input>
      <div v-if="showSearch" class="back">
        <el-button round @click="handleReturn">返 回</el-button>
      </div>
    </div>
    <div v-if="showSearch" class="result">
      <div class="left">
        <Total :count="list.length" :time="responseTime">
        </Total>
        <div class="info">
          <!-- <el-table v-if="list.length > 0" :data="list" style="width: 100%">
          </el-table> -->
          <a-spin :spinning="spinning">
            <a-list v-if="list.length > 0" item-layout="horizontal" :data-source="list" style="height: 500px">
              <template #renderItem="{ item }">
                <a-list-item @click="jump(item)">
                  <a-list-item-meta>
                    <template #title>
                      <a href="#" v-html="item.title"></a>
                    </template>
                  </a-list-item-meta>
                </a-list-item>
              </template>
            </a-list>
            <div v-else class="empty">
              <el-empty description="暂无数据" />
            </div>
          </a-spin>
        </div>
      </div>
      <Record v-if="showSearch" :result="list" @searchEvent="onSearch"></Record>
    </div>
  </div>
</template>

<style scoped lang="less">
.search {
  border-radius: 8px;
  display: flex;
  // align-items: center;
  flex-direction: column;

  .el-input {
    height: 40px;
    :deep(.el-input__wrapper) {
      border-top-left-radius: 24px;
      border-bottom-left-radius: 24px;
      padding-left: 20px;
      // margin-right: 10px;
    }
    :deep(.el-input-group__append) {
      border-top-right-radius: 24px;
      border-bottom-right-radius: 24px;
      color: #fff;
      background: #1677ff;
      border:1px solid #1677ff;
      box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0);
    }
  }

  .header {
    display: flex;
    align-items: center;
    position: relative;

    .select {
      margin-right: 16px;
    }

    .date-range {
      margin-left: 48px;
      display: flex;
      align-items: center;
    }

    .back {
      position: absolute;
      right: 16px;
    }
  }

  .tip {
    margin-left: 24px;
    display: flex;
    align-items: center;

    .icon {
      width: 18px;
      height: 18px;
      margin-right: 4px;
    }
  }

  .result {
    display: flex;
    margin-top: 24px;
    flex: 1;
    height: 0px;

    .left {
      display: flex;
      flex-direction: column;
      height: 100%;
      width: 73%;

      .info {
        flex: 1;
        height: 100%;
        margin-bottom: 16px;
        overflow-y: auto;

        .empty {
          height: 100%;
          display: flex;
          justify-content: center;
          height: 100%;
          align-items: center;
          // margin: 12% auto;
        }
      }
    }
  }
}
</style>
