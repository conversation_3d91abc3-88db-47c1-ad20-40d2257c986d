
<script setup>
import { MAX_ROW_COUNT, transferList, DEFAULT_PROJECT_INDEX } from "./constant.ts"
import { fixedNumber } from "@/utils/number.ts"
import { VALUE_UNKNOWN } from "@/constant/index.tsx"
import Edit from "./Edit/index.vue"
import ts from 'typescript';
import useCacheList from '@/hooks/useCacheList.ts'
import { httpPost } from "@wl-fe/http/dist";
const spinning = ref(false)
const [list, setList] = useCacheList((DEFAULT_PROJECT_INDEX), "project_index_list")
console.log(list, 'list');
console.log(DEFAULT_PROJECT_INDEX, 'DEFAULT_PROJECT_INDEX');
const indexList = computed(() => {
  return transferList(list.value)
})
watch(indexList, (newval) => {
  indexList.value = newval
})
const data = ref([])
const getProjectData = async () => {
  spinning.value = true
  try {
    const result = await httpPost("/statisticalAnalysisController/getHomePageStatisticalData", {})
    data.value = result
    indexList.value = transferList(list.value)
    spinning.value = false
  } catch (error) {
    spinning.value = false
  }
}
onMounted(() => {
  //  setList([{a:2}])
  getProjectData()
})
</script>

<template>
  <!-- <div>
        {{ list }}
        ------
        <el-button @click="setList([{a:Math.random()}])">更新数据</el-button>
    </div> -->
  <div class="project">
    <a-spin :spinning="spinning">
      <div class="container">
        <div v-for="(item, index) in indexList" :key="index" class="module-index"
          :style="{ flex: Math.ceil(item.children.filter(v => v.isShow ?? true)?.length / MAX_ROW_COUNT) }">
          <div :class=" index %2===0 ? 'title' : 'title title1' "> 
            <span class="my-svg" :style="{ background: `url(${item.bg})` ,backgroundSize: '100% 100%' }"></span>
            <span>{{ item.title }}</span></div>
          <div class="list"
            :style="{ gridTemplateColumns: `repeat(${Math.ceil(item.children.filter(v => v.isShow ?? true)?.length / MAX_ROW_COUNT)}, 1fr)` }"
            :class="{ 'flex-row': item.children?.filter(v => v.isShow ?? true)?.length || 0 > MAX_ROW_COUNT }">
            <template v-for="(child, index1) in item.children" :key="index1">
              <div :class=" index %2===0 ? 'indexItem' : 'indexItem indexItem1' " v-if="child.isShow ?? true">
                <span >{{ child.title }}</span>
                <span class="value"> {{ data[item.value]?.[child.value] || VALUE_UNKNOWN }}</span>
              </div>
            </template>
          </div>
        </div>
      </div> 
    </a-spin>
    <div class="edit">
      <Edit :allList="list" @onChange="(val) => setList(val)"></Edit>
    </div>
  </div>
</template>

<style scoped lang="less">
.project {
  margin-top: 20px;
  padding-top: 16px;
  background: url("/home/<USER>") no-repeat;
  background-size: 100% 100%;
  position: relative;
  border-radius: 8px;

  .container {
    display: flex;

    .module-index {
      display: flex;
      flex: 1;
      flex-direction: column;
      align-items: center;
      margin: 0px 24px;
      flex-shrink: 0;
      position: relative;

        // &::before{
        //   content: "";
        //   position: absolute;
        //   top: 0px;
        //   right: -24px;
        //   width: 2px;
        //   height: 298px;
        //   background-color: #158c3c3d;
        //   // border-radius: 8px;
        // }
      .title {
        width: 100%;
        color: rgba(51, 51, 51, 1);
        font-weight: bold;
        font-size: 16px;
        height: 42px;
        background:#4096ffba;
        // background: url("./lp_nor.svg");
        // background-repeat: no-repeat;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 8px;
        background-size: 100% 100%;
        background-repeat: no-repeat;
        margin-bottom: 12px;
        // cursor: pointer;
        .my-svg{
          width: 30px;
          height: 30px;
          margin-right: 5px;
        }
        span{
          color: #000;
        }
      }
      .title1{
        background:#01dfe3a3;
        span{
          color: #000;
        }
      }
      .list {
        display: grid;
        width: 100%;
        height: 260px;
        grid-gap: 8px 16px;
        flex-wrap: wrap;
        padding-bottom: 8px;
        grid-template-columns: 1fr 1fr;
        grid-template-rows: repeat(6, 1fr);
        overflow: hidden;

        .indexItem {
          font-size: 14px;
          font-weight: bold;
          text-align: center;
          // cursor: pointer;
          height: 42px;
          border-radius: 8px;
          padding: 4px 8px;
          display: flex;
          justify-content: center;
          flex-wrap: wrap;
          align-items: center;
          background:#7cb3ff2e;
          color: #000;
          // background: url("/home/<USER>");
          // background-repeat: no-repeat;
          // background-size: 100% 100%;
          // &:hover {
          //   background: url("/home/<USER>");
          //   background-repeat: no-repeat;
          //   background-size: 100% 100%;
          // }
    
          .value {
            margin: 0px 4px;
            font-size: 18px;
          }
        }
        .indexItem1{
          background:#01dfe326;
          color: #000;
          // background: url("/home/<USER>");
          // background-repeat: no-repeat;
          // background-size: 100% 100%;
        }
      }
    }
  }

  .edit {
    position: absolute;
    bottom: 8px;
    cursor: pointer;
    right: 16px;
  }
}</style>
