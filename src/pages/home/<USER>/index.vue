<script setup>
import { Tabs as ATabs, message } from 'ant-design-vue';
import { ElTabs, ElMessage } from "element-plus";
import { eventTypeList } from "./constant"
import { httpPost } from "@wl-fe/http/dist";
const none =  `/${import.meta.env.VITE_PROXY_SECMENU}/bid/none1.png`;
const currentTab = ref('1')
const openUrl = async (url,rowGuid) => {
    if(currentTab.value == 1){
        await  httpPost("syscHandingMattersController/updateReadStatus", {rowGuid:rowGuid})
    }
    if (url) {
        window.location.href = url;
    } else {
        ElMessage.error("暂无跳转链接!")
    }
}
const eventMap = ref([])
const setCurrentTab = (key) => {
    currentTab.value = key
    getMaxListeners()
}
onMounted(async () => {
    getMaxListeners()
})
const getMaxListeners = async () => {
    const result = await httpPost("syscHandingMattersController/list", {readType:currentTab.value})
    console.log('resultresult',result);
    eventMap.value = result.map((ele)=>{
        return{
            rowGuid:ele.rowGuid,
            content:ele.title            ,
            link:ele.url
        }
    })
}
</script>
<template>
    <div class="events">
        <div class="header bg-content">
            <el-tabs v-model="currentTab" @tab-change="setCurrentTab">
                <el-tab-pane v-for="(item,index) in eventTypeList" :name="item.key" :key="index" :label="item.label"></el-tab-pane>
            </el-tabs>
        </div>
        <div class="list" v-if="eventMap.length != 0">
            <div class="item" v-for="(item, index) in eventMap" :key="index" @click="openUrl(item.link,item.rowGuid)">
                {{ index + 1 }}. {{ item.content }}
            </div>
        </div>
        <div class="listNone" v-else>
            <el-empty :image='none' :image-size="100" description="暂无内容" />
        </div>
    </div>
</template>

<style scoped lang="less">
.events {
    width: 37.5%;
    border: 1px solid rgba(175, 194, 221, 1);
    margin-right: 20px;
    border-radius: 8px;
    height: 100%;

    .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: rgba(224, 234, 250, 1);
        padding: 0px 20px;
        border-radius: 8px 8px 0px 0px;

        .more {
            cursor: pointer;

            >span {
                margin-right: 4px;
            }
        }
    }

    .list {
        background-color: #fff;
        padding: 20px;
        height: 245px;
        .item {
            cursor: pointer;
            line-height: 42px;
            text-overflow: ellipsis;
            white-space: nowrap;
            overflow: hidden;
            width: 100%;
            font-size: 16px;
            &:hover {
                color: rgb(22, 119, 255);
                text-decoration: underline;
            }
        }
    }
}
</style>
