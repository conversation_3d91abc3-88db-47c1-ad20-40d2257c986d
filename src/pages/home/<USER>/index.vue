<script setup>
import { getMessages } from './constant'
import { httpPost } from "@wl-fe/http/dist";
import Icon, { SoundTwoTone } from '@ant-design/icons-vue';
import Marquee from "./components/marquee/index.vue"
import { openUrl } from '@/utils'
const messages = ref([])  // getMessages
const changTime = (time) =>{
    const [year, month, day] = time.split('-');
    const formattedDate = `${year}年${parseInt(month)}月${parseInt(day)}日`;
    return formattedDate;
}
const handleGetTop = async () => {
    const result = await httpPost("/api/sysAnnouncement/getSysAnnouncementDocs?pageNum=1&pageSize=10",{})
    const list = result.rows.slice(0, 5).map(item => {
        return {
            id: item.id,
            content: item.title,
            time: changTime(item.date),
            link:`http://**************/news/${item.id}`
        }
    })
    messages.value = list;
}
onMounted(() => {
    handleGetTop()
})
</script>
<template>
    <div class="top5">
        <div class="icon">
            <SoundTwoTone style="font-size:16px;" />
            <span> TOP5</span>
        </div>
        <div class="container">
            <Marquee speed="10">
                <div class="message" v-for="(item, index) in messages" :key="index">
                   <div @click="openUrl(item.link)">[ {{ item.time }} ] {{ item.content }}</div>
                </div>

            </Marquee>
        </div>
    </div>
</template>

<style scoped lang="less">
.top5 {
    margin-top: 16px;
    height: 54px;
    border-radius: 8px;
    background: rgba(195, 212, 231, 0.2);
    border: 1px solid rgba(175, 194, 221, 1);
    display: flex;
    align-items: center;

    .icon {
        display: flex;
        height: 100%;
        align-items: center;
        padding: 0px 20px;
         font-size: 16px;
        >span {
            margin-right: 4px;
        }

        background:rgba(224, 234, 250, 1);
    }

    .container {
        flex: 1;
        width: 0;
        padding-right: 24px;

        .message {
            margin-right: 42px;
            cursor: pointer;
            font-size: 16px;
            &:hover {
                color: rgb(22, 119, 255);
                text-decoration: underline;
            }
        }
    }

    .more {
        padding: 0px 16px;
        height: 100%;
        display: flex;
        align-items: center;
        cursor: pointer;
        background: rgba(224, 234, 250, 1);
        flex-shrink: 0;

        >span {
            margin-right: 4px;
        }

        // :global{
        //     .anticon{
        //         color: rgb(22, 119, 255);            ;
        //     }
        // }
    }

}</style>
