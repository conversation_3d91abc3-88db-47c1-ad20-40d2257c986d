<script setup lang="ts">
import { Tag } from 'ant-design-vue';
import { Popconfirm as APopconfirm } from 'ant-design-vue';
import Icon, { ToolOutlined } from '@ant-design/icons-vue';
import useCacheList from '@/hooks/useCacheList.ts'
import { MAX_ROW_COUNT, transferList, DEFAULT_PROJECT_INDEX, checkMaxShowList } from "../constant.ts"
const updatedArray = ref([])
const props = defineProps({
    allList: {
        type: Array,
        default: [] // 默认滚动速度，单位秒
    }
})
const emit = defineEmits(['onChange'])
const replaceExistingItems = (oldArray, newArray) => {
    return oldArray.map(oldItem => {
        const newItem = newArray.find(newItem => newItem.title === oldItem.title); // 假设数组元素是对象，且每个对象都有一个唯一的id属性
        return newItem ? newItem : oldItem;
    });
}
const list = ref([])
watch(
    () => props.allList,
    (newVal) => {
        list.value = newVal
    },
    { immediate: true }
);
const updateList = (idx, childIdx, checked) => {


    checked = !checked

    const newList = JSON.parse(JSON.stringify(list.value))
    newList[idx].children![childIdx].isShow = checked

    const isValid = checkMaxShowList(newList)
    if (isValid) {
        list.value = newList
    }

}
</script>
<template>
    <div>
        <a-popconfirm title="更新展示指标" placement="topLeft" ok-text="确定" cancel-text="取消" @confirm="emit('onChange', list)"
            @cancel="list = props.allList">
            <template #description>
                <div class="edit">
                    <div class="module" v-for="(item, index) in list" :key="index">
                        <div class="title">{{ item.title }}</div>
                        <div class="list">
                            <div class="index" v-for="(child, childIdx) in item.children" :key="childIdx">
                                <a-checkable-tag :checked="child.isShow ?? true"
                                    @change="updateList(index, childIdx, child.isShow ?? true)">{{ child.title
                                    }}</a-checkable-tag>
                            </div>
                        </div>
                    </div>
                </div>
            </template>
            <ToolOutlined />
        </a-popconfirm>
    </div>
</template>

<style scoped lang="less">
.edit {
    display: flex;
    flex-direction: column;

    .module {
        display: flex;
        margin-bottom: 8px;

        .title {
            margin-right: 8px;
            min-width: 80px;
            text-align: right;
        }

        .list {
            display: flex;
        }
    }
}
</style>
