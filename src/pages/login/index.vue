<script setup lang="ts">
import { El<PERSON>ard, ElMessage, ElTabPane } from "element-plus";
import { getLoginTypes } from "./constant.tsx";
import { checkUserInfo, LOCAL_STORAGE_TOKEN_KEY } from "@/constant/index.tsx";
import { useRouter } from "vue-router";
const router = useRouter();
localStorage.removeItem("project_index_list")
const onLoginSuccess = async (token: string) => {
  try {
    localStorage.setItem(LOCAL_STORAGE_TOKEN_KEY, token);
    sessionStorage.setItem('CaPdfTokenConfig', `3@@@@@@${LOCAL_STORAGE_TOKEN_KEY}@@@Authorization`)
    const isSuccess = await checkUserInfo();
    if (isSuccess) {
      ElMessage.success("登录成功，欢迎您的使用！");
      router.push({
        path:"/home"
      });
    }
  } catch (error) {
    localStorage.removeItem(LOCAL_STORAGE_TOKEN_KEY);
  }
};
function handleCurrentChange(tab) {
    console.log(tab.props.name)
    sessionStorage.setItem('loginType',tab.props.name)
}
const loginTypes = getLoginTypes();
const activeTab = ref(loginTypes[0].key);
</script>
<template>
  <div class="login">
    <div class="left">
      <div class="title">
        <img src="/login/title.png" alt="" />
      </div>
    </div>
    <div class="right">
      <div class="container">
        <ElCard
          class="card"
          :body-style="{
            width: 'auto',
          }"
        >
          <ElTabs v-model="activeTab"  @tab-click="handleCurrentChange">
            <ElTabPane
              v-for="item in loginTypes"
              :key="item.key"
              :label="item.label"
              :name="item.key"
              lazy
            >
              <keep-alive>
                <component
                  :is="item.component"
                  @onSuccess="onLoginSuccess"
                ></component>
              </keep-alive>
            </ElTabPane>
          </ElTabs>
        </ElCard>
      </div>
      <div class="tip">
        推荐使用谷歌
        <img src="/login/google.png" />
        或者火狐<img src="/login/firefox.png" />浏览器登录本系统，设置成1920 X
        1080分辨率，获得更好的操作体验……
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.login {
  display: flex;
  height: 100%;
  width: 100%;
  background-image: url("/login/bg.png");
  background-size: 100% 100%;
  .left {
    width: 30%;
    font-size: 32px;
    color: #fff;
    font-weight: bold;
    line-height: 32px;
    text-align: right;
    .title {
      margin-top: 25%;
      margin-right: 8%;
      > div {
        margin-bottom: 20px;
        padding-right: 20px;
      }
      img{
        width: 370px;
      }
    }
  }
  .right {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    .container {
      position: relative;

      .card {
        border-radius: 8px;
        :deep .el-tabs__content {
          width: 100%;
        }
        .el-tabs {
          //.el-tab-pane {
          //  height: 360px;
          //}
          :deep .el-tabs__nav {
            display: flex;
            justify-content: space-between;
            width: 100%;
          }
          :deep .el-tabs__active-bar {
            background-color: #1677ff;
          }
          :deep .el-tabs__item {
            padding: 0px 16px;
            font-size: 16px;
          }
        }
      }
    }
    .tip {
      position: absolute;
      bottom: 50px;
      color: #fff;
      font-size: 12px;
      display: flex;

      > img {
        width: 16px;
        height: auto;
        margin: 0px 2px;
      }
    }
  }
}
</style>
