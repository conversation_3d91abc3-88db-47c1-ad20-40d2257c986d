<script setup lang="ts">
import { fullBase64Url } from "@/utils/url";
import { User } from "@element-plus/icons-vue";
import { httpGet, httpPost } from "@wl-fe/http";
import { ElButton, ElInput, ElMessage } from "element-plus";
const loading = ref(false);
const smsSendNumber = ref(0);
const emit = defineEmits(["onSuccess"]);
const submitData = ref({
  smscode: "",
  username: "",
});
const imgCodeInfo = ref<
  Partial<{
    img: string;
    uuid: string;
    code: string;
  }>
>({});
const generateSmsCode = async () => {
  const result = await httpGet("captchaImage");
  imgCodeInfo.value = {
    img: result.img,
    uuid: result.uuid,
  };
  submitData.value.smscode=""
};

const sendSmsTimeout = () => {
  let count = 60;
  const timer = setInterval(() => {
    count = count - 1;
    smsSendNumber.value = count - 1;
    if (count <= 1) {
      clearInterval(timer);
    }
  }, 1000);
};

const sendMessage = async () => {
  sendSmsTimeout();
  try {
    await httpPost("/sendRegPhone", {
      username: submitData.value.username,
      ...imgCodeInfo.value,
    });
    ElMessage.success("短信发送成功,请查收!");
  } catch (error: any) {
    ElMessage.error(error.msg);
  }
};
const onSubmit = async () => {
  loading.value = true;
  try {
    const result = await httpPost("/phonelogin", {
      ...submitData.value,
      ...imgCodeInfo.value
    });
    emit("onSuccess", result.token);
  } finally {
    loading.value = false;
  }
};
onMounted(() => {
  generateSmsCode();
});
</script>
<template>
  <div class="sms" v-loading="loading">
    <div class="title">手机号登录</div>
    <div class="content">
      <div class="row">
        <ElInput
          size="large"
          :prefix-icon="User"
          placeholder="请输入手机号"
          v-model="submitData.username"
        />
      </div>
      <div class="row">
        <ElInput
          size="large"
          placeholder="请输入图形验证码"
          v-model="imgCodeInfo.code"
        />
        <img
          @click="generateSmsCode"
          class="sms-code"
          :src="fullBase64Url(imgCodeInfo.img as string)"
          alt=""
        />
      </div>
      <div class="row">
        <ElInput
          size="large"
          placeholder="请输入短信验证码"
          v-model="submitData.smscode"
        />
        <ElButton
          class="send"
          size="large"
          @click="sendMessage"
          :disabled="!imgCodeInfo.code && smsSendNumber > 0"
          >{{
            smsSendNumber > 0 ? `${smsSendNumber}s后再试` : "发送验证码"
          }}</ElButton
        >
      </div>
    </div>
    <ElButton type="primary" size="large" class="login" @click="onSubmit"
      >登录</ElButton
    >
  </div>
</template>

<style scoped lang="less">
.sms {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  //height: 100% !important;
  width: 300px;
  margin: 0px auto;
  height: 360px;
  .title {
    margin: 0px auto;
    font-size: 20px;
    font-weight: bold;
    color: #2a2a2a;
    margin: 24px;
  }
  .content {
    display: flex;
    flex-direction: column;
    width: 100%;
    margin-top: 24px;
    .row {
      margin-bottom: 18px;
      display: flex;
      align-items: center;
      .send,
      .check {
        margin-left: 8px;
        font-size: 14px;
      }
      .sms-code {
        width: 110px;
        height: 40px;
        margin-left: 12px;
        cursor: pointer;
      }
      &.check {
        position: relative;
        .el-checkbox {
          height: auto;
        }
      }
      :deep .el-input__wrapper {
        background-color: #f5f5f5 !important;
        .el-input__inner {
          background-color: #f5f5f5 !important;
        }
      }
      &:last-child {
        position: relative;
        top: 0px;
      }
    }
  }
  .login {
    width: 100%;
    position: absolute;
    bottom: 20px;
    border-radius: 24px;
    color: #fff;
    background: #1677ff;
    font-size: 16px;
  }
}
</style>
