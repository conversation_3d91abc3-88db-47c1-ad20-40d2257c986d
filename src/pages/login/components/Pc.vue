<template>
  <div id="pcBox">
    <div class="title">pc登录</div>
    <div class="content">
      <img
        v-show="smctimg"
        id="mask"
        src="/login/smct.png"
        width="340"
        height="306"
      />
      <div class="faceContent" v-show="!smctimg">
        <div class="radio-box">
          <el-radio-group v-model="faceForm.roleType" @change="handleChange">
            <el-radio-button label="1">企业用户</el-radio-button>
            <el-radio-button label="2">个人用户</el-radio-button>
          </el-radio-group>
        </div>
        <div class="video-box">
          <video id="video" ref="video" preload autoplay loop muted></video>
          <canvas id="canvas" width="265" height="200"></canvas>
        </div>
        <el-input
          v-model="faceForm.username"
          auto-complete="off"
          :placeholder="
            faceForm.roleType == 1 ? '请输入管理员姓名' : '请输入姓名'
          "
          class="my-input"
        >
        </el-input>

        <el-input
          v-model="faceForm.idcard"
          auto-complete="off"
          :placeholder="
            faceForm.roleType == 1 ? '请输入管理员身份证号' : '请输入身份证号'
          "
          class="my-input"
        >
        </el-input>

        <ElButton
          type="primary"
          v-if="!loading"
          size="large"
          class="my-button"
          @click="handGetFaceInfo()"
          >识别登录</ElButton
        >
      </div>
    </div>
  </div>
</template>

<script setup>
import "@/utils/libs/track/tracking-min.js"; // 需要引入(下载链接在文末)
import "@/utils/libs/track/face-min.js"; // // 需要引入(下载链接在文末)
let loading = ref(false);
let smctimg = ref(false);
let faceForm = ref({ roleType: 1, username: "", idcard: "", face: "" });
onMounted(() => {
  getCompetence();
  checkFace();
});
function handleChange(val) {
  faceForm.value.roleType = val;
}
function handGetFaceInfo() {
//   if (!faceForm.value.username || !faceForm.value.idcard) {
//     ElMessage.error({ message: "请输入姓名或者身份证号" });
//     return;
//   }
//   if (!isValidChineseID(faceForm.value.idcard)) {
//     ElMessage.error({ message: "请输入正确身份证号" });
//     return;
//   }
  var imgrl = document.getElementById("mask");
  imgrl.style.display = "none";
  let canvas = document.getElementById("canvas");
  let context = canvas.getContext("2d");
  let video = document.getElementById("video");
  context.drawImage(video, 0, 0, 340, 250);
  var image = new Image();
  image.src = canvas.toDataURL("image/jpeg", 1.0);
  postFace(image.src.replace("data:image/jpeg;base64,", ""));
}
// 人脸识别完毕
function postFace(imgfile) {
  console.log("imgfile", imgfile);
  faceForm.value.face = imgfile;
  faceForm.value.username = faceForm.value.username.replace(/\s/g, "");
  console.log("this.faceForm", faceForm.value);
}

// 对焦 获取脸部框
function checkFace() {
  var canvas = document.getElementById("canvas");
  var context = canvas.getContext("2d");
  var tracker = new tracking.ObjectTracker("face");
  tracker.setInitialScale(4);
  tracker.setStepSize(2);
  tracker.setEdgesDensity(0.1);
  var trackerTask = tracking.track("#video", tracker, { camera: true });
  tracker.on("track", function (event) {
    if (event.data.length <= 0) {
      return;
    }
    context.clearRect(0, 0, canvas.width, canvas.height);
    event.data.forEach(function (rect) {
      context.strokeStyle = "#a64ceb";
      context.strokeRect(rect.x, rect.y, rect.width, rect.height);
      context.font = "11px Helvetica";
      context.fillStyle = "#fff";
      context.fillText(
        "x: " + rect.x + "px",
        rect.x + rect.width + 5,
        rect.y + 11
      );
      context.fillText(
        "y: " + rect.y + "px",
        rect.x + rect.width + 5,
        rect.y + 22
      );
    });
  });
}
function checkCameraPermission() {
  return navigator.mediaDevices
    .getUserMedia({ video: true })
    .then((stream) => {
      return { hasPermission: true, stream: stream };
    })
    .catch((error) => {
      if (error.name === "NotAllowedError") {
        return { hasPermission: false };
      } else {
        // 其他错误情况可以根据需要处理
        console.error("获取摄像头权限时发生错误：", error);
        return { hasPermission: false };
      }
    });
}
//开启摄像头
function getCompetence() {
  checkCameraPermission().then(({ hasPermission, stream }) => {
    if (hasPermission) {
      var myVideo = document.getElementById("video");
      myVideo.src = stream;
      smctimg.value = false;
      console.log("摄像头权限已开启");
    } else {
      console.log("摄像头权限未开启");
      smctimg.value = true;
      ElMessage.error({ message: "请检查摄像头是否被占用或未开启" });
    }
  });
}
function isValidChineseID(id) {
  // 15位或18位身份证号码的正则表达式
  const reg =
    /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}([0-9]|X)$|^[1-9]\d{5}\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}$/;
  return reg.test(id);
}
</script>

<style lang="less" scoped>
#pcBox {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  height: 460px !important;
  width: 300px;
  margin: 0px auto;

  .title {
    margin: 0px auto;
    font-size: 20px;
    font-weight: bold;
    color: #2a2a2a;
    margin: 24px;
  }
  .content {
    display: flex;
    flex-direction: column;
    width: 100%;
    align-items: center;
    .faceContent {
      display: flex;
      flex-direction: column;
      align-items: center;
      .radio-box {
        margin: 5px;
        width: 100%;
        display: flex;
        justify-content: center;
        :deep .el-radio-button__inner {
          width: 150px;
        }
      }
      .video-box {
        position: relative;
        width: 265px;
        height: auto;
        #video {
          width: 99%;
          height: 99%;
        }
        #canvas {
          position: absolute;
          top: 0;
          left: 0;
        }
      }
      .my-input {
        margin: 7px;
        :deep .el-input__wrapper {
          background-color: #f5f5f5 !important;
          .el-input__inner {
            background-color: #f5f5f5 !important;
          }
        }
      }
      .my-button {
        width: 100%;
        position: absolute;
        bottom: 5px;
        border-radius: 24px;
        font-size: 16px;
      }
    }
  }
}
</style>