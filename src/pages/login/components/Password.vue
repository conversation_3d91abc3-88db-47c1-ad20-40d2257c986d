<script setup lang="ts">
import { Lock, User } from "@element-plus/icons-vue";
import { httpPost } from "@wl-fe/http/dist";
import { ElButton, ElCheckbox, ElInput } from "element-plus";
const loading = ref(false);
const emit = defineEmits(["onSuccess"]);
const submitData = ref({
  password: "",
  username: "",
  checked: false,
});

const onSubmit = async () => {
  loading.value = true;
  try {
    const result = await httpPost("/login", submitData.value);
    emit("onSuccess", result.token);
  } finally {
    loading.value = false;
  }
};
</script>
<template>
  <div class="sms" v-loading="loading">
    <div class="title">账号密码</div>
    <div class="content">
      <div class="row">
        <ElInput
          size="large"
          :prefix-icon="User"
          placeholder="请输入账号"
          v-model="submitData.username"
        />
      </div>
      <div class="row">
        <ElInput
          :prefix-icon="Lock"
          size="large"
          type="password"
          placeholder="请输入密码"
          v-model="submitData.password"
        />
      </div>
      <div class="row">
        <ElCheckbox v-model="submitData.checked" />
        <span class="check"> 记住密码</span>
      </div>
    </div>
    <ElButton type="primary" size="large" class="login" @click="onSubmit"
      >登录</ElButton
    >
  </div>
</template>

<style scoped lang="less">
.sms {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  //height: 100% !important;
  width: 300px;
  margin: 0px auto;
  height: 360px;
  .title {
    margin: 0px auto;
    font-size: 20px;
    font-weight: bold;
    color: #2a2a2a;
    margin: 24px;
  }
  .content {
    display: flex;
    flex-direction: column;
    width: 100%;
    margin-top: 24px;
    .row {
      margin-bottom: 30px;
      display: flex;
      align-items: center;
      .send,
      .check {
        margin-left: 8px;
        font-size: 14px;
      }
      &.check {
        position: relative;
        .el-checkbox {
          height: auto;
        }
      }
      :deep .el-input__wrapper {
        background-color: #f5f5f5 !important;
        .el-input__inner {
          background-color: #f5f5f5 !important;
        }
      }
      &:last-child {
        position: relative;
        top: -12px;
      }
    }
  }
  .login {
    width: 100%;
    position: absolute;
    bottom: 20px;
    border-radius: 24px;
    color: #fff;
    background: #1677ff;
    font-size: 16px;
  }
}
</style>
