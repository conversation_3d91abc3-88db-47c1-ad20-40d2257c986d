<script setup lang="ts">
import { httpPost } from "@wl-fe/http/dist";
import { QRCode, QRCodeProps } from "ant-design-vue";
const emit = defineEmits(["onSuccess"]);
const qrData = ref({
  hyperlinkText: "",
  qrCodeImageBase64: "",
  qrCodeJumpUrl: "",
  qrCodeShowText: "",
  tid: "",
});
const timer = ref();
// 获取二维码
const generateImg = async () => {
  try {
    const result = await httpPost("https://zsqz.lnwlzb.com/back/api/zsqz/getQr");
    qrData.value = result;
  } catch (err) {}
};

const login = async (userInfo: any) => {
  console.log("useeeeeeeeeeee",userInfo);
  const result = await httpPost("/hrxtLogin", userInfo);
  if (result.token) {
    emit("onSuccess", result.token);
  }
};

// 轮训扫码结果
const getUserInfo = async () => {
  let result = await httpPost(
      "https://zsqz.lnwlzb.com/back/api/zsqz/getOrgAuthInfo",
      {
        tid: qrData.value.tid,
      }
  );
  console.log("result", result);
  //  result = {
  //   "msg": "查询成功！",
  //   "code": 200,
  //   "data": {
  //     "tid": "X2100000027e06681cd4fdb47ca9ff52f3982c67c1b",
  //     "orgTransactionCode": null,
  //     "personalTransactionCode": null,
  //     "idCardHash": "210124199108080230",
  //     "orgCode": "91210700MA0XLQM535",
  //     "orgName": "变更企业",
  //     "loginUserType": "02",
  //     "personalName": "于家欢",
  //     "signatureCert": "MIICjDCCAjCgAwIBAgIIMwAAABBAcWEwDAYIKoEcz1UBg3UFADBhMQswCQYDVQQGEwJDTjEwMC4GA1UECgwnQ2hpbmEgRmluYW5jaWFsIENlcnRpZmljYXRpb24gQXV0aG9yaXR5MSAwHgYDVQQDDBdDRkNBIEFDUyBURVNUIFNNMiBPQ0EzMzAeFw0yNDEwMjUwMjM5NDRaFw0yOTEwMjAxMTM3MzZaMHExCzAJBgNVBAYTAkNOMQwwCgYDVQQKDANIS0UxFTATBgNVBAsMDExOV0xMb2NhbEhLRTEVMBMGA1UECwwMSU5ESVZJRFVBTC0xMSYwJAYDVQQDDB0xQOS6juWutuasokAxNzI5ODIzOTg0ODM5QDY3OTBZMBMGByqGSM49AgEGCCqBHM9VAYItA0IABMOP09B7SNb/ByaEYELU0eNZ1/thMd9z6CelLE/uk0iQTV03IclyeJEej5YYXO5gWhkeZJzeINbnZ//ZqY4aITyjgb8wgbwwHwYDVR0jBBgwFoAUDm0tMwSO8ApuPPy2fLUz9boRe/gwDAYDVR0TAQH/BAIwADA9BgNVHR8ENjA0MDKgMKAuhixodHRwOi8vdWNybC5jZmNhLmNvbS5jbi9PQ0EzMy9TTTIvY3JsNjA2LmNybDAOBgNVHQ8BAf8EBAMCBsAwHQYDVR0OBBYEFO6BRprX6UYYybMQ24qF7TGEl8E/MB0GA1UdJQQWMBQGCCsGAQUFBwMCBggrBgEFBQcDBDAMBggqgRzPVQGDdQUAA0gAMEUCIFm5aFlH8ZSFV2XLGpeQ7nDyH3drFkjwCEoJzlk5e116AiEAy7BQ2N9+WqGqmPBXCotz0MKNGBjpx4MYvpxQpGLqBO4=",
  //     "publicKeyAlgorithm": "00",
  //     "telephoneNumber": "15141067515",
  //     "idCardType": "00",
  //     "orgLicenceType": "97",
  //     "caOrgCode": "010001",
  //     "signatureCertSn": "3300000010407161",
  //     "dataSignatureValue": "MIIDpwYKKoEcz1UGAQQCAqCCA5cwggOTAgEBMQ4wDAYIKoEcz1UBgxEFADAMBgoqgRzPVQYBBAIBoIICkDCCAowwggIwoAMCAQICCDMAAAAQQHFhMAwGCCqBHM9VAYN1BQAwYTELMAkGA1UEBhMCQ04xMDAuBgNVBAoMJ0NoaW5hIEZpbmFuY2lhbCBDZXJ0aWZpY2F0aW9uIEF1dGhvcml0eTEgMB4GA1UEAwwXQ0ZDQSBBQ1MgVEVTVCBTTTIgT0NBMzMwHhcNMjQxMDI1MDIzOTQ0WhcNMjkxMDIwMTEzNzM2WjBxMQswCQYDVQQGEwJDTjEMMAoGA1UECgwDSEtFMRUwEwYDVQQLDAxMTldMTG9jYWxIS0UxFTATBgNVBAsMDElORElWSURVQUwtMTEmMCQGA1UEAwwdMUDkuo7lrrbmrKJAMTcyOTgyMzk4NDgzOUA2NzkwWTATBgcqhkjOPQIBBggqgRzPVQGCLQNCAATDj9PQe0jW/wcmhGBC1NHjWdf7YTHfc+gnpSxP7pNIkE1dNyHJcniRHo+WGFzuYFoZHmSc3iDW52f/2amOGiE8o4G/MIG8MB8GA1UdIwQYMBaAFA5tLTMEjvAKbjz8tny1M/W6EXv4MAwGA1UdEwEB/wQCMAAwPQYDVR0fBDYwNDAyoDCgLoYsaHR0cDovL3VjcmwuY2ZjYS5jb20uY24vT0NBMzMvU00yL2NybDYwNi5jcmwwDgYDVR0PAQH/BAQDAgbAMB0GA1UdDgQWBBTugUaa1+lGGMmzENuKhe0xhJfBPzAdBgNVHSUEFjAUBggrBgEFBQcDAgYIKwYBBQUHAwQwDAYIKoEcz1UBg3UFAANIADBFAiBZuWhZR/GUhVdlyxqXkO5w8h93axZI8AhKCc5ZOXtdegIhAMuwUNjfflqhqpjwVwqLc9DCjRgY6ceDGL6cUKRi6gTuMYHbMIHYAgEBMG0wYTELMAkGA1UEBhMCQ04xMDAuBgNVBAoMJ0NoaW5hIEZpbmFuY2lhbCBDZXJ0aWZpY2F0aW9uIEF1dGhvcml0eTEgMB4GA1UEAwwXQ0ZDQSBBQ1MgVEVTVCBTTTIgT0NBMzMCCDMAAAAQQHFhMAwGCCqBHM9VAYMRBQAwDQYJKoEcz1UBgi0BBQAERzBFAiAZred73g2Vlkoz81PL93yWHDG4AYVTtqchcwUNcefBqgIhANoT0/AePhFmJLHiZ0asF64c3wB71KtWFBcFFXboDmAX",
  //     "signatureData": "X2100000027e06681cd4fdb47ca9ff52f3982c67c1b"
  //   }
  // }
  // console.log("result", result);
  if (result.signatureCertSn) {
    login(result);
    clearInterval(timer.value);
  }
};

const queryResult = async () => {
  if (qrData.value.tid) {
    const result = await httpPost(
        "https://zsqz.lnwlzb.com/back/api/zsqz/queryQRCodeScannedStatus",
        {
          ...qrData.value,
        }
    );
    if (result.scannedStatus === DIGIT_STATUS_OK) {
      clearInterval(timer.value);
      getUserInfo();
    }
  }
};

onMounted(() => {
  generateImg();
  timer.value = setInterval(() => {
    getUserInfo();
  }, 5000);
});
onUnmounted(() => {
  clearInterval(timer.value);
});

</script>
<template>
  <div class="app">
    <div class="title">扫码登陆</div>
    <div class="content">
      <div className="code" @click="generateImg">
        <img :src="`data:image/png;base64,${qrData.qrCodeImageBase64}`" />
      </div>
<!--      <div class="row" v-html="qrData.hyperlinkText"></div>-->
      <div  class="row"><span>请使用</span><el-link type="primary" style="font-size: 22px;font-weight: bold;" href="http://cashare.cebpubservice.com/#/allActivated?tradingSystemCode=X2100000027" target="_blank">
      CA互认共享APP
    </el-link><span>扫码登录</span></div>
<!--      <span>-->
<!--        {{ qrData.tid }}-->
<!--      </span>-->
    </div>
  </div>
</template>

<style lang="less">
.app {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 360px;
  .title {
    margin: 0px auto;
    font-size: 20px;
    font-weight: bold;
    color: #2a2a2a;
    margin: 24px;
  }
  .content {
    display: flex;
    flex-direction: column;
    width: 100%;
    align-items: center;
    .code {
      margin-bottom: 24px;
      cursor: pointer;
      img {
        width: 200px;
        height: 200px;
      }
    }
    .row {
      margin: 8px;
      display: flex;
      font-weight: bold;
    }
  }
}
</style>
