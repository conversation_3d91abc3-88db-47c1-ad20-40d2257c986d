<script setup lang="ts">
import { httpPost } from "@wl-fe/http/dist";
import { QRCode, QRCodeProps } from "ant-design-vue";
const emit = defineEmits(["onSuccess"]);
const qrData = ref({
  content: "",
  qrcodeid: "",
});
const qrcodeidRef = ref("");
const qrStatus = ref<QRCodeProps["status"]>("expired");
const generateImg = async () => {
  qrStatus.value = "loading";
  try {
    const result = await httpPost("/createQR");
    qrcodeidRef.value = result.qrcodeid;
    qrData.value = result;
    qrStatus.value = "active";
  } catch (err) {
    qrStatus.value = "expired";
  }
};

const login = async (username: string) => {
  const result = await httpPost("/lytlogin", {
    username,
  });
  if (result.token) {
    emit("onSuccess", result.token);
  }
};

const queryResult = async () => {
  if (qrcodeidRef.value) {
    const result = await httpPost("/queryResult", {
      qrcodeid: qrcodeidRef.value,
    });
    if (result.username) {
      login(result.username);
    }
  }
};

onMounted(() => {
  generateImg();
  const timer = setInterval(() => {
    queryResult();
  }, 5000);
  return () => {
    clearInterval(timer);
  };
});

</script>
<template>
  <div class="app">
    <div class="title">扫码登陆</div>
    <div class="content">
      <div className="code">
        <QRCode
          error-level="H"
          :value="qrData.content"
          :status="qrStatus"
          icon="/login/lyt.png"
        />
      </div>
      <div class="row">
        打开辽易通扫一扫登录
        <span
          class="link"
          @click="
            {
              generateImg();
            }
          "
          >刷新二维码</span
        >
      </div>
      <div className="row">下载辽易通APP,绑定后扫码即可直接登录平台</div>
    </div>
  </div>
</template>

<style lang="less">
.app {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 360px;
  .title {
    margin: 0px auto;
    font-size: 20px;
    font-weight: bold;
    color: #2a2a2a;
    margin: 24px;
  }
  .content {
    display: flex;
    flex-direction: column;
    width: 100%;
    align-items: center;
    .code {
      margin-bottom: 24px;
    }
    .row {
      margin: 8px;
      display: flex;
      font-weight: bold;
      font-size: 16px;
      .link {
        color: #e5e5e5;
        margin: 0px 4px;
        text-decoration: underline;
        cursor: pointer;
        color: rgb(54, 148, 253);
      }
    }
  }
}
</style>
