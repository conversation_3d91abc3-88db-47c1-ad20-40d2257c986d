<script setup lang="ts">
import {isAvailableBrow} from "@/utils";
import {ElMessage, ElButton} from "element-plus";
import CaModule, {CaInfoType} from "@/utils/libs/ca";
import {httpPost} from "@wl-fe/http/dist";
import {Lock} from "@element-plus/icons-vue";

type SubmitDataType = Partial<{
    username: string;
    password: string;
}>;
const caInfo = ref();
const caInstance = ref();
const loading = ref(true);
const submitData = ref<SubmitDataType>({});
const emit = defineEmits(["onSuccess"]);
let wlInstance = ref('');
const init = async (showError = true) => {
    loading.value = true;
    try {
        wlInstance.value = new WlModule({
            moduleType: MODULE_TYPE_CA,
            certType: CERT_TYPE_SIGN,
        });
        await wlInstance.value.init();
        const result = await wlInstance.value.getBaseInfo();
        caInfo.value = wlInstance.value.utils.ca.transferCaCertInfo(result);
        console.log("this.caInfo", caInfo.value);
        caInfo.value.list.forEach((item: any) => {
            switch (item.label) {
                case '锁名称':
                    caInfo.value['orgname'] = item.value;
                    break;
                case '证书颁发者':
                    caInfo.value['bfjg'] = item.value;
                    break;
                case '证书序列号':
                    caInfo.value['signCertSN'] = item.value;
                    break;
                case '证书有效期':
                    caInfo.value['dayTip'] = item.value;
                    break;

                default:
                    break;
            }
        });
        loading.value = false;
        submitData.value = {
            username: caInfo.value.key,
        };
        // caInstance.value = new CaModule();
        // await caInstance.value.init();
        // const result = await caInstance.value.read();
        // loading.value = false;
        // caInfo.value = result as CaInfoType;
        // submitData.value = {
        //   username: result.deviceNum,
        // };
    } catch (errMsg) {
        if (errMsg && typeof errMsg === "string" && showError) {
            ElMessage.error(errMsg);
        }
        // setTimeout(() => {
        //   init(false);
        // }, 2000);
    }
};

const onSubmit = async () => {

    try {
      await wlInstance.value.validatePin(submitData.value.password);
    }catch {
            ElMessage.error('密码错误！');
            return;
    }

        const result = await httpPost("/calogin", {username: submitData.value.username});
        emit("onSuccess", result.token);



};

onMounted(() => {
    if (isAvailableBrow()) {
        init();
    }
});
</script>
<template>
    <div class="ca">
        <div class="title">Ukey登录</div>
        <div class="content">
            <div class="empty" v-if="loading">
                <img src="/login/usb.gif" alt=""/>
                <div class="text">
                    驱动检查中,请稍等,
                    <a
                            href="https://download-fgw.lnwlzb.com:8888/辽易通证书助手.exe"
                    >驱动下载</a
                    >
                </div>
            </div>
            <div v-else>
                <div class="row">
                    <div class="label">锁名称:</div>
                    <div class="value">{{ caInfo.orgname }}</div>
                </div>
                <div class="row">
                    <div class="label">序列号:</div>
                    <div class="value">{{ caInfo.signCertSN }}</div>
                </div>
                <div class="row">
                    <div class="label">有效期:</div>
                    <div class="value">{{ caInfo.dayTip }}</div>
                </div>
                <div class="row">
                    <div class="label">颁发机构:</div>
                    <div class="value">{{ caInfo.bfjg }}</div>
                </div>
                <div class="row">
                    <ElInput
                            :prefix-icon="Lock"
                            size="large"
                            type="password"
                            placeholder="请输入密码"
                            v-model="submitData.password"
                    />
                </div>
            </div>
        </div>
        <ElButton
                type="primary"
                v-if="!loading"
                size="large"
                class="login"
                @click="onSubmit()"
        >登录
        </ElButton
        >
    </div>
</template>

<style lang="less">
.ca {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 300px;
  height: 360px;
  position: relative;
  margin: 0px auto;

  .title {
    margin: 0px auto;
    font-size: 20px;
    font-weight: bold;
    color: #2a2a2a;
    margin: 24px;
    margin-bottom: 0px;
  }

  .content {
    margin-top: 24px;

    .empty {
      display: flex;
      flex-direction: column;
      align-items: center;

      > img {
        width: 80%;
        height: auto;
      }

      .text {
        margin-top: 32px;
        color: #6e6e6e;

        a {
          text-decoration: underline;
        }
      }
    }

    .row {
      display: flex;
      align-items: baseline;
      margin-bottom: 12px;
      font-size: 16px;
      .label {
        color: #333333;
        width: 100px;
        text-align: right;
        margin-right: 8px;
      }

      .value {
        text-wrap: wrap;
        width: 300px;
      }
    }
  }

  .login {
    width: 100%;
    position: absolute;
    bottom: 0px;
    border-radius: 24px;
    color: #fff;
    background: #1677ff;
    font-size: 16px;
  }
}
</style>
