import Sms from "./components/Sms.vue"
import Password from "./components/Password.vue"
// import Ca from "./components/Ca.vue"
import Ca from "./components/NewCa.vue"
import App from "./components/App.vue"
import AppNew from "./components/AppNew.vue"
import Pc from "./components/Pc.vue"
export const LOGIN_TYPE_MESSAGE='sms'
export const LOGIN_TYPE_PASSWORD='password'
export const LOGIN_TYPE_CA='ca'
export const LOGIN_TYPE_APP='app'
export const LOGIN_TYPE_APP_NEW='appNew'
export const LOGIN_TYPE_PC='Pc'
export const getLoginTypes=()=>{
    return [
        {
            label: "实体CA登录",
            key: LOGIN_TYPE_CA,
            component:Ca
        },
        {
            label: '手机号登录',
            key: LOGIN_TYPE_MESSAGE,
            component:Sms
        },
        {
            label: "账号密码登录",
            key: LOGIN_TYPE_PASSWORD,
            component:Password
        },
        {
            label: "辽易通登录",
            key: LOGIN_TYPE_APP,
            component:App
        },
        {
            label: "全国互认登录",
            key: LOGIN_TYPE_APP_NEW,
            component:AppNew
        },
        {
            label: "PC证书登录",
            key: LOGIN_TYPE_PC,
            component:Pc
        }
    ]
}
