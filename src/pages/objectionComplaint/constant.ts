import { ColumnItemType } from "@/type";
import { StatusType, TabModuleItemType } from "@/models/searchList/type";
import dayjs from "dayjs";

type summaryType = {
    [key: string]: string;
};


const customStyles = {
    labelStyle: {
        marginTop: "12%",
        fontSize: "18px",
        paddingLeft: "12px"
    },
    countStyle: {
        paddingLeft: "12px"

    },
};


export const TYPE_COMPLAINT = "1";  // 异议
export const TYPE_PLAINT = '2' //投诉
export const TYPE_MAIN = '3' // 主体


export const summaryApi = "/subSysComplaint/queryStatistics";

const complaintApi = "/subSysComplaint/yiyiList";
const plaintApi = "/subSysComplaint/tousuList";
const mainPlaintApi = "/subSysComplaint/maintousuList";

// 异议状态
const COMPLAINT_STATUS: StatusType = {
    "10": {
        label: "编辑中",
        color: "#4D94FC",
    },
    "20": {
        label: "待回复",
        color: "#ad6800",
    },
    "30": {
        label: "已撤回",
        color: "#faad14",
    },
    "40": {
        label: "已答复",
        color: "#00B34C",
    },
    "50": {
        label: "未答复",
        color: "#E50000",
    },
};

// 投诉状态
const PLAINT_STATUS: StatusType = {
    "10": {
        label: "编辑中",
        color: "#4D94FC",
    },
    "20": {
        label: "待受理",
        color: "#ad6800",
    },
    "30": {
        label: "撤回申请中",
        color: "#eb2f96",
    },
    "40": {
        label: "已受理",
        color: "#00B34C",
    },
    "50": {
        label: "不予受理",
        color: "#E50000",
    },
    "60": {
        label: "已处理",
        color: "#10239e",
    },
    "70": {
        label: "已撤回 ",
        color: "#faad14",
    },
};
// 主体投诉状态
const MAIN_PLAINT_STATUS: StatusType = {
    "0": {
        label: "待受理",
        color: "#ad6800",
    },
    "2": {
        label: "不予受理",
        color: "#E50000",
    },
    "1": {
        label: "已处理",
        color: "#10239e",
    }
};

export const renderTabOptions = (summary: summaryType): TabModuleItemType[] => {
    return [
        {
            label: "异议",
            id: TYPE_COMPLAINT,
            columns: [
                {
                    title: "全部异议",
                    dataIndex: "yiyiCont",
                    styles: customStyles,
                },

                {
                    title: "招标人已答复",
                    dataIndex: "yiyiYwc",
                    styles: customStyles,
                },

                {
                    title: "招标人未答复",
                    dataIndex: "yiyiWwc",
                    styles: customStyles,
                },
            ].map((item: ColumnItemType) => {
                return {
                    ...item,
                    text: item.text ?? summary[item.dataIndex],
                };
            }),
        },
        {
            label: "投诉",
            id: TYPE_PLAINT,
            columns: [
                {
                    title: "全部投诉",
                    dataIndex: "tousuCont",
                    styles: customStyles,
                },

                {
                    title: "已处理投诉",
                    dataIndex: "tousuYwc",
                    styles: customStyles,
                },
                {
                    title: "未处理投诉",
                    dataIndex: "tousuWwc",
                    styles: customStyles,
                },
            ].map((item: ColumnItemType) => {
                return {
                    ...item,
                    text: item.text ?? summary[item.dataIndex],
                    tag: "",
                    styles:{
                        ...item.styles,
                        tagStyle:{
                            right:'14px'
                        }
                    }
                };
            }),
        },
        {
            label: "主体投诉",
            id: TYPE_MAIN,
            columns: [
                {
                    title: "主体投诉",
                    dataIndex: "maintousuCont",
                    styles: customStyles,
                },

                {
                    title: "已处理投诉",
                    dataIndex: "maintousuYwc",
                    styles: customStyles,
                },
                {
                    title: "未处理投诉",
                    dataIndex: "maintousuWwc",
                    styles: customStyles,
                },
            ].map((item: ColumnItemType) => {
                return {
                    ...item,
                    text: item.text ?? summary[item.dataIndex],
                    tag: "主体库",
                    styles:{
                        ...item.styles,
                        tagStyle:{
                            right:'8px'
                        }
                    }
                };
            }),
        },
    ];
};

export const renderTableColumns = (
): ColumnItemType[] => {
    return [

        {
            title: "标段唯一标识码",
            dataIndex: "bidSingleCode",
            width: 200
        },
        {
            title: "标段（包）编号",
            dataIndex: "bidSectionCode",
            width: 220
        },
        {
            title: "标段名称",
            dataIndex: "bidSectionName"
        },
        {
            title: "异议提交时间",
            dataIndex: "yiyiTime",
            width: 200,
            render: (text) => {
                if (!text) {
                    return h('span', '');
                }
                return h('span', dayjs(text).format("YYYY-MM-DD HH:mm:ss"));
            }
        },
        {
            title: "异议人",
            dataIndex: "bidderName",
            width: 250
        },
        {
            title: "招标人",
            dataIndex: "tendereeName",
            width: 250
        },
        {
            title: "异议答复时间",
            dataIndex: "dafuTime",
            width: 200,
            render: (text) => {
                if (!text) {
                    return h('span', '');
                }
                return h('span', dayjs(text).format("YYYY-MM-DD HH:mm:ss"));
            }
        },
        {
            title: "状态",
            dataIndex: "yiyiStatus",
            width: 100,
            render: (text) => {
                return h('span', {
                    style: {
                        color: COMPLAINT_STATUS[text]?.color
                    }
                }, [
                    h('span', {

                    }, COMPLAINT_STATUS[text]?.label)
                ])
            }
        }
    ];
};

export const renderPlaintTableColumns = (
    ): ColumnItemType[] => {
        return [

            {
                title: "标段唯一标识码",
                dataIndex: "bidSingleCode",
            },
            {
                title: "标段（包）编号",
                dataIndex: "bidSectionCode",
            },
            {
                title: "标段名称",
                dataIndex: "bidSectionName",
            },
            {
                title: "投诉提交时间",
                dataIndex: "tousuTime",
                render: (text) => h('span', dayjs(text).format("YYYY-MM-DD HH:mm:ss")),
            },
            {
                title: "投诉人",
                dataIndex: "bidderName",

            },
            {
                title: "招标人",
                dataIndex: "tendereeName",
            },

            {
                title: "投诉受理时间",
                dataIndex: "acceptTime",
                render: (text) => {
                    if (!text) {
                        return h('span', '');
                    }
                    return h('span', dayjs(text).format("YYYY-MM-DD HH:mm:ss"));
                }
            },
            {
                title: "投诉处理时间",
                dataIndex: "dealTime",
                render: (text) => {
                    if (!text) {
                        return h('span', '');
                    }
                    return h('span', dayjs(text).format("YYYY-MM-DD HH:mm:ss"));
                }
            },


            {
                title: "状态",
                dataIndex: "tousuStatus",
                render: (text) => {
                    return h('span', {
                        style: {
                            color: PLAINT_STATUS[text]?.color
                        }
                    }, [
                        h('span', {
                            style: {
                                marginLeft: "8px"
                            }
                        }, PLAINT_STATUS[text]?.label)
                    ])
                }
            },
        ]
    }
;

export const renderMainPlaintTableColumns = (
): ColumnItemType[] => {
    return [

        {
            title: "投诉编号",
            dataIndex: "mainComplaintNo",
            key: "mainComplaintNo",

        },
        {
            title: "投诉单位名称",
            dataIndex: "unitName",
            key: "unitName",

        },
        {
            title: "投诉类别",
            dataIndex: "typeName",
            key: "typeName",

        },
        {
            title: "被投诉单位",
            dataIndex: "btousuName",
            key: "btousuName",

        },
        {
            title: "提出时间",
            dataIndex: "tousuTime",
            key: "tousuTime",

        },
        {
            title: "状态",
            dataIndex: "infoStatus",
            render: (text) => {
                return h('span', {
                    style: {
                        color: MAIN_PLAINT_STATUS[text]?.color
                    }
                }, [
                    h('span', {
                        style: {
                            marginLeft: "8px"
                        }
                    }, MAIN_PLAINT_STATUS[text]?.label)
                ])
            }
        }
    ];
};

export const MAP_API = {
    [TYPE_COMPLAINT]: complaintApi,
    [TYPE_PLAINT]: plaintApi,
    [TYPE_MAIN]: mainPlaintApi,
};
export const MAP_PARAMS = {
    [TYPE_COMPLAINT]: "objectNo",
    [TYPE_PLAINT]: "complaintNo",
    [TYPE_MAIN]: "mainComplaintNo",
};

export const MAP_COLUMNS = {
    [TYPE_COMPLAINT]: renderTableColumns,
    [TYPE_PLAINT]: renderPlaintTableColumns,
    [TYPE_MAIN]: renderMainPlaintTableColumns,
};
