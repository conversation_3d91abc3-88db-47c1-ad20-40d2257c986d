<!-- 异议投诉界面 -->
<script setup lang="ts">
import SearchList from "@/models/searchList/index.vue";
import {
  renderTabOptions,
  MAP_API,
  MAP_COLUMNS,
  MAP_PARAMS,
  summaryApi,
  TYPE_COMPLAINT,
} from "./constant.ts";
import { formatIntNumber } from "@/utils/number";
import { ElMessage } from "element-plus";
import { httpPost } from "@wl-fe/http/dist";

const summaryData = ref({});
const type = ref(TYPE_COMPLAINT);

// 获取统计数据
const getSummaryData = async () => {
  try {
    const res = await httpPost(summaryApi);
    const result = {
      yiyiCont: formatIntNumber(res.yiyiCont),
      yiyiYwc: formatIntNumber(res.yiyiYwc),
      yiyiWwc: formatIntNumber(res.yiyiWwc),
      tousuCont: formatIntNumber(res.tousuCont),
      tousuYwc: formatIntNumber(res.tousuYwc),
      tousuWwc: formatIntNumber(res.tousuWwc),
      maintousuCont: formatIntNumber(res.maintousuCont),
      maintousuYwc: formatIntNumber(res.maintousuYwc),
      maintousuWwc: formatIntNumber(res.maintousuWwc),
    };
    summaryData.value = { ...result };
  } catch (error) {
    ElMessage.error("获取统计数据错误");
  }
};

//操作
const operation = async (record: any) => {
  const Num = MAP_PARAMS[type.value as keyof typeof MAP_PARAMS];
  const params = { [Num]: record[Num], platFormCode: record.platFormCode };
  try {
      if(MAP_PARAMS[type.value as keyof typeof MAP_PARAMS] == 'mainComplaintNo'){
          const res = await httpPost("/subSysComplaint/skipUrlMainTs", record);
          window.open(res.msg, "_blank");
          return;
      }
    const res = await httpPost("/subSysComplaint/skipUrl", params);
    window.open(res.msg, "_blank");
  } catch (error) {
    ElMessage.error("操作失败");
  }
};

//tabChange
const tabChange = (key: string) => {
  type.value = key;
};

onMounted(() => {
  getSummaryData();
});
</script>
<template>
  <PageWrapper>
    <SearchList
      :key="type"
      :tab="{
        options: renderTabOptions(summaryData),
        changeCallback: tabChange,
      }"
      :table="{
          api: MAP_API[type as keyof typeof MAP_PARAMS],
          onRowOperClick: operation,
          columns: MAP_COLUMNS[type as keyof typeof MAP_COLUMNS](),
          extraParams:{
            activeKey:type
          }
      }"
    />
  </PageWrapper>
</template>

<style scoped lang="less"></style>
