<template>
    <div id="customPie" ref="chartContainer"></div>
</template>

<script setup>
let props = defineProps({
  pieData: {
    type: Array,
    default: [],
  },
});
let colors = ['#ff6384', '#36a2eb', '#ffce56', '#4bc0c0', '#9966ff', '#ff9f40']; // 颜色数组

let option = {
    color: props.pieData.map((_, index) => colors[index % colors.length]), // 根据数据长度设置颜色
    legend: {
        top: "bottom",
        padding: [20, 0, 15, 0]
    },
    series: [
        {
            name: '',
            type: 'pie',
            radius: [30, 120],
            center: ['50%', '50%'],
            roseType: 'area',
            itemStyle: {
                borderRadius: 8
            },
            data: props.pieData.map(item => {
              return {
                value: item.value,
                name: item.name
              }
            }).sort(function (a, b) {
              return a.value - b.value;
            }),
        }
    ]
};
const chartContainer = ref(null);
onMounted(async () => {
  await nextTick();
  const myChart = echarts.init(chartContainer.value);
  myChart.setOption(option);
});
</script>

<style lang="less" scoped>
#customPie {
  width: 100%;
  height: 100%;
}
</style>