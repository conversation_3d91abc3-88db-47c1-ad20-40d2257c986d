<template>
  <div id="horizontalBarChart" ref="chartContainer"></div>
</template>
    
<script setup >
let props = defineProps({
  infoData: {
    default: {
       name:["第7天", "第5-6天", "第3-4天", "第1-2天"],
       value:[60, 30, 10, 0]
    },
  },
});
let color2 = {
  type: "linear",
  x: 0,
  y: 0,
  x2: 1,
  y2: 0,
  colorStops: [
    {
      offset: 0,
      color: "#ffa39e", // 0% 处的颜色
    },
    {
      offset: 1,
      color: "#f5222d", // 100% 处的颜色
    },
  ],
  global: false, // 缺省为 false
};
let color1 = {
  type: "linear",
  x: 0,
  y: 0,
  x2: 1,
  y2: 0,
  colorStops: [
    {
      offset: 0,
      color: "#91d5ff", // 0% 处的颜色
    },
    {
      offset: 1,
      color: "#1890ff", // 100% 处的颜色
    },
  ],
  global: false, // 缺省为 false
};
let option = {
  backgroundColor: "transparent",
  xAxis: {
    type: "value",
  },
  yAxis: {
    type: "category",
    inverse: true,
    data: props.infoData.name,

  },
  grid: {
    left: 20,
    right: 60,
    top: 20,
    bottom: 20,
    containLabel: true,
  },
  color: [color1],
  series: [
    {
      data: props.infoData.value,
      type: "bar",
      selectedMode: true,
      emphasis: {
        disabled: true,
      },
      itemStyle: {
        borderWidth: 0,
        borderRadius: [0, 6, 6, 0],
      },
      barWidth: 30,
      select: {
        itemStyle: {
          color: color2,
          borderWidth: 0,
        },
        label: {
          show: true,
          position: "right",
          formatter: (val) => {
            return `{a|${val.data}}{b|%}`;
          },
          rich: {
            a: {
              fontSize: 20,
              color: "gold",
            },
            b: {
              fontSize: 20,
              color: "#333",
            },
          },
        },
      },
    },
  ],
};

const chartContainer = ref(null);
onMounted(async () => {
  await nextTick();
  const myChart = echarts.init(chartContainer.value);
  myChart.setOption(option);
  // 设置选中select
  setTimeout(() => {
    myChart.dispatchAction({
      type: "select", //默认显示江苏的提示框
      seriesIndex: 0, //这行不能省
      dataIndex: 0,
    });
  }, 300);
  let dataIndex = 0;
  setInterval(() => {
    dataIndex += 1;
    if (dataIndex >= option.series[0].data.length) {
      dataIndex = 0;
    }
    myChart.dispatchAction({
      type: "select", //默认显示江苏的提示框
      seriesIndex: 0, //这行不能省
      dataIndex: dataIndex,
    });
  }, 2000);
});
</script>
    
  <style scoped lang="less">
#horizontalBarChart {
  width: 100%;
  height: 100%;
}
</style>