<template>
  <div id="barEchart" ref="chartContainer"></div>
</template>
      
      <script setup >
let props = defineProps({
  barData: {
    type: Array,
    default: [],
  },
  text: {
    type: String,
    default: ''
  }
});

let data =  props.barData
let names = [];
let values = [];
data.forEach((item, index) => {
  names.unshift(item.name);
  values.unshift(item.value);
});

var colorList = {
  type: "linear",
  x: 1,
  y: 1,
  x2: 0,
  y2: 0,
  colorStops: [
    {
      offset: 0,
      color: "#06DBF5", // 0% 处的颜色
    },
    {
      offset: 1,
      color: "#00d386", // 100% 处的颜色
    },
  ],
  globalCoord: false, // 缺省为 false
};
var option = {
  backgroundColor: "aliceblue",
  tooltip: {
    trigger: "axis",
    axisPointer: {
      type: "shadow",
    },
  },
  grid: {
    left: "1%",
    right: "10%",
    bottom: "5%",
    containLabel: true,
  },
  xAxis: {
    type: "value",
    nameLocation: 'end',
    nameGap: 50,
    axisLine: {
      lineStyle: {
        color: "black",
      },
    },
    splitLine: {
      show: true,
      lineStyle: {
        color: "rgba(255,255,255,0.3)",
      },
    },
  },
  yAxis: {
    type: "category",
    data: names,
    axisLine: {
      lineStyle: {
        color: "black",
      },
    },
    axisLabel: {
      interval: 0,
      // rotate: 40,
      textStyle: {
        fontFamily: "Microsoft YaHei",
      },
    },
  },
  series: [
    {
      name: "",
      type: "bar",
      barWidth: "60%",
      label: {
        normal: {
          show: true,
          position: "outside",
          formatter: function(params) {
            return props.text === '单位:家' ? params.value + '家': params.value ;
          },
          textStyle: {
            color: 'black',
            fontSize: 14
          }
        }
      },
      itemStyle: {
        color: function (params) {
          return colorList;
        },
      },
      emphasis: {
        itemStyle: {
          color: "#FBB419",
        },
      },
      data: values, // Set values as the data for series
    },
  ],
};

var app = {
  currentIndex: -1,
};
const chartContainer = ref(null);
onMounted(async () => {
  await nextTick();
  const myChart = echarts.init(chartContainer.value);
  myChart.setOption(option);
});
</script>
      
    <style scoped lang="less">
#barEchart {
  width: 100%;
  height: 100%;
}
</style>