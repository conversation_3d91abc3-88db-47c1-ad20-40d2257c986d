<template>
  <div class="competition-chart-container">
    <div class="chart-area">
      <div class="bars-wrapper">
        <div class="bars-container">
          <div
            v-for="(bar, index) in bars"
            :key="bar.id"
            class="bar-item"
            :style="{
              height: getBarHeight(bar.value, maxValue),
              backgroundColor: getBarColor(index, bars.length),
            }"
            :title="getApproximateLabel(index, bars.length)"
          ></div>
        </div>
      </div>
      <div class="bottom-gradient-bar"></div>
    </div>
    <!-- <div class="labels-container">
      <p v-for="labelItem in labelsData" :key="labelItem.id">
        {{ labelItem.label }}
      </p>
    </div> -->
  </div>
  <div class="eteName">
    大于500家小于1000家(含) (集群化竞争)
  </div>
</template>
<script setup>
// --- Configuration ---
const numBars = 20; // Number of vertical bars (adjust to match visual density)
const minHeightPercent = 5; // Minimum height for the first bar
const maxHeightPercent = 140; // Maximum height for the last bar (relative to bars-wrapper height)


const bars = ref(
  Array.from({ length: numBars }, (_, i) => ({
    id: i,
    value: i / (numBars - 1),
  }))
);

// Labels data matching the image
const labelsData = ref([
  { id: 1, label: "小于3家 (竞争不充分)" },
  { id: 2, label: "大于3家(含)小于10家(含) (标准竞争)" },
  { id: 3, label: "大于10家小于20家(含) (标准竞争)" },
  { id: 4, label: "大于20家小于30家(含) (充分竞争)" },
  { id: 5, label: "大于30家小于50家(含) (深度竞争)" },
  { id: 6, label: "大于50家小于100家(含) (完全竞争)" },
  { id: 7, label: "大于100家小于200家(含) (饱和竞争)" },
  { id: 8, label: "大于200家小于500家(含) (超量级竞争)" },
  { id: 9, label: "大于500家小于1000家(含) (集群化竞争)" },
  { id: 10, label: "大于1000家 (战略储备竞争)" },
]);

const maxValue = 1;
function getBarHeight(value) {
  const height =
    minHeightPercent + value * (maxHeightPercent - minHeightPercent);
  return `${height}%`;
}

function getBarColor(index, total) {
  const ratio = index / (total - 1); 
  let red, green;
  if (ratio < 0.5) {
    // Green to Yellow
    red = Math.round(255 * (ratio * 2));
    green = 255;
  } else {
    // Yellow to Red
    red = 255;
    green = Math.round(255 * (1 - (ratio - 0.5) * 2));
  }
  const blue = 0;
  if(getApproximateLabel(index, total) != "大于500家小于1000家(含) (集群化竞争)"){
    return `#f2f2f2`; 
  } 
  return `rgb(${red}, ${green}, ${blue})`;
}

function getApproximateLabel(index, totalBars) {
  const ratio = index / (totalBars - 1);
  const labelIndex = Math.min(
    Math.floor(ratio * labelsData.value.length),
    labelsData.value.length - 1
  );
  return labelsData.value[labelIndex]?.label || "";
}
</script>
  
  <style scoped>
.competition-chart-container {
  display: flex;
  align-items: flex-end; /* Align items to the bottom */
  padding: 20px;
  /* background-color: #f8f8f8; Optional background for contrast */
  font-family: sans-serif;
  gap: 20px; /* Space between chart area and labels */
  overflow: hidden; /* Prevent potential overflow issues */
  width: 100%;
  height: 86%;
}

.chart-area {
  display: flex;
  flex-direction: column;
  flex-grow: 1; /* Allow chart area to take available space */
  min-width: 300px; /* Minimum width for the chart */
}

.bars-wrapper {
  /* This wrapper defines the maximum height the bars can reach */
  height: 150px; /* Adjust as needed */
  width: 100%;
  display: flex;
  align-items: flex-end; /* Bars align to the bottom */
  margin-bottom: 5px; /* Space between bars and bottom gradient */
}

.bars-container {
  display: flex;
  align-items: flex-end; /* Bars grow upwards */
  width: 100%;
  height: 100%;
}

.bar-item {
  flex: 1; /* Distribute space evenly among bars */
  margin: 0 1px; /* Tiny space between bars */
  background-color: #ccc; /* Default color */
  transition: height 0.3s ease-out, background-color 0.3s ease; /* Smooth transitions */
  min-width: 5px; /* Minimum width for very narrow charts */
}

.bottom-gradient-bar {
  width: 100%;
  height: 15px; /* Adjust height as needed */
  background: linear-gradient(
    to right,
    rgb(0, 255, 0),
    rgb(255, 255, 0),
    rgb(255, 0, 0)
  );
  border-radius: 3px;
}

.labels-container {
  display: flex;
  flex-direction: column;
  justify-content: space-between; /* Distribute labels vertically */
  font-size: 12px; /* Adjust font size */
  color: #333;
  min-height: 150px; /* Match approx height of bars area */
  padding-bottom: 20px; /* Align bottom label roughly with bottom gradient */
  line-height: 1.4;
  white-space: nowrap; /* Prevent labels from wrapping */
}

/* Optional: Add hover effect for bars */
.bar-item:hover {
  opacity: 0.8;
}
.eteName{
  text-align: center;
  font-size: 18px;
  font-weight: 600;
}
</style>