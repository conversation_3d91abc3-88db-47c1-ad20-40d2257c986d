<template>
  <div id="barEchart" ref="chartContainer"></div>
</template>
    
    <script setup >
let props = defineProps({
  barData: {
    type: Array,
    default: [],
  },
});
let data =  props.barData
// [
//   {
//     name: "法库县",
//     value: 954,
//   },
//   {
//     name: "浑南区",
//     value: 1337,
//   },
//   {
//     name: "辽中区",
//     value: 1593,
//   },
//   {
//     name: "和平区",
//     value: 1827,
//   },
//   {
//     name: "康平县",
//     value: 1918,
//   },
//   {
//     name: "苏家屯区",
//     value: 1941,
//   },
//   {
//     name: "沈北新区",
//     value: 2168,
//   },
//   {
//     name: "大东区",
//     value: 2229,
//   },
//   {
//     name: "铁西区",
//     value: 2367,
//   },
//   {
//     name: "于洪区",
//     value: 2736,
//   },
//   {
//     name: "新民市",
//     value: 2844,
//   },
//   {
//     name: "皇姑区",
//     value: 2889,
//   },
//   {
//     name: "沈河区",
//     value: 3143,
//   },
// ];
let names = [];
let values = [];
data.forEach((item, index) => {
    alert(item.value)
  names.push(item.name);
  values.push(item.value);
});
let option = {
  backgroundColor: "aliceblue",
  tooltip: {
    trigger: "axis",
    axisPointer: {
      type: "shadow",
    },
  },
  grid: {
    left: 20,
    right: 40,
    top: 20,
    bottom: 20,
    containLabel: true,
  },
  xAxis: {
    name: '万元',// 这个设置只在末尾添加单位
    type: "value",
    splitNumber: 3,
    axisTick: {
      show: false,
    },
    axisLine: {
      show: false,
    },
    axisLabel: {
      textStyle: {
        color: "#000",
        fontSize: 14,
      },
    },
    splitLine: {
      show: false,
    },
  },
  yAxis: {
    type: "category",
    data: names,
    height: 30,
    axisTick: {
      show: false,
      alignWithLabel: true,
    },
    axisLine: {
      show: false,
    },
    axisLabel: {
      interval: 0,
      // rotate: 30,
      textStyle: {
        color: "#000",
        fontSize: 14,
      },
    },
    splitLine: {
      show: false,
    },
  },
  series: [
    {
      type: "bar",
      showBackground: true,
      backgroundStyle: {
        color: "rgba(180, 180, 180, 0.2)",
      },
      barWidth: 30,
      itemStyle: {
        borderRadius: 10,
        color: {
          type: "linear",
          x: 0,
          y: 0,
          x2: 1,
          y2: 0,
          colorStops: [
            { offset: 0, color: "#347CDD" }, // 0% 处的颜色
            { offset: 1, color: "#56fb93" }, // 100% 处的颜色
          ],
          global: false, // 缺省为 false
        },
      },
      label: {
        show: true,
        position: "right",
        color: "#000",
        fontSize: 14,
      },
      data: values,
    },
  ],
};

const chartContainer = ref(null);
onMounted(async () => {
  await nextTick();
  const myChart = echarts.init(chartContainer.value);
  myChart.setOption(option);
});
</script>
    
  <style scoped lang="less">
#barEchart {
  width: 100%;
  height: 100%;
}
</style>