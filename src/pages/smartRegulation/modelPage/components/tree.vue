<template>
  <div
    id="chartContainer"
    ref="chartContainer"
    style="height: 800px; width: 100%;"
  ></div>
</template>

<script setup >
let count = ref(0);
const  countChildren = (nodes) => {
  let total = 0;
  // 遍历每个节点
  nodes.forEach(node => {
    // 如果当前节点有 children 数组，则累加其长度
    if (node.children) {
      total += node.children.length;
      // 递归计算子节点的 children 总数
      total += countChildren(node.children);
    }
  });

  return total;
}
const router = useRouter();
const chartContainer = ref(null);
const handleInit = async (dataArray) => {
  count.value = countChildren(dataArray)

  let option = {
    tooltip: {
      //鼠标悬停在节点效果
      trigger: "item",
      triggerOn: "mousemove",
      formatter: function (params) {
        // 自定义显示的详细信息
        return params.data.name;
      },
    },
    series: [
      {
        // 当此节点下还有子节点时候，设置的节点样式，用于区别 没有子节点的节点的样式
        itemStyle: {
          normal: {
            color: "#06c",
            borderColor: "#06c",
          },
          emphasis: {
            color: "#ffffff",
            borderColor: "#06c",
          },
        },
        lineStyle: {
          //线的样式
          color: "#158C3C",
          height: 50,
        },
        triggerOn: "dblclick",
        type: "tree",
        roam: true,
        data: dataArray, //data为树图的数据，格式：{id:111，name:xxx,children:[{id:222,name:xxx,children:[...]},...]}
        left: count.value  <= 5? "20%" : "5%" ,
        right: count.value  <= 5? "20%" : "3%" ,
        top: "8%",
        bottom: "10%",
        symbol: "rect",
        symbolSize: 7, //节点大小
        // orient: "vertical",
        expandAndCollapse: true, //是否开启折叠功能
        edgeShape: "curve",  // curve polyline
        initialTreeDepth: 30, //初始展开的层级
        label: {
          //节点样式
          // fontFamily: "KaiTi",
          // position: "top", //节点文字在节点的上方
          // verticalAlign: "middle",
          align: "right",
          overflow: "hidden",
          margin: [2, 4],
          borderWidth: 1, //文字添加边框
          // borderColor: "#FFFFFF", //边框颜色
          // backgroundColor: "#fff", //节点模块背景色
          // backgroundColor: {
          //     image: 'http://120.201.111.145:8887/js/echartsBg.png'
          // }, //节点模块背景色
          borderRadius: [0,7,0,7], //圆角
          padding: [8, 4], //文字和边框的距离
          // rich: {
          //   keywords: {
          //     color: "red",
          //     fontSize: 12,
          //   },
          //   index: {
          //     fontSize: 12,
          //     color: "#2979ff",
          //     position: "10%",
          //   },
          // },
          textStyle: {
            width: 100, //定义节点文字长度
            height: 20, //定义节点文字高度
            fontSize: 16, //文字大小
            color: "#FFFFFF", //文字颜色
            align: "center", //文字的位置
          },
          formatter: function (params) {
            // console.log(params.data);
            // if (params.data.name.length > 6) {
            //   params.data.name = params.data.name.substring(0, 5) + ".."; //如果超出6个字符，显示5个+..
            // }
            // if(params.data.count > 1){ 
            //     // return params.data.name + "(" + params.data.count + ")";
            //     return `{title|${params.data.name}}{number|(${params.data.count})}`
            // }
            // return `{title|${params.data.name}}`
          },
          rich:{
            title:{
              fontSize:16,
              color:'#fff',
              padding:[3,0,0,0],
              align:'center',
            },
            number:{
              fontSize:16,
              color:'#FF9933',
              padding:[3,0,0,5],
              align:'center',
            }
          }
        },
        leaves: {
          label: {
            // position: "top",
            // verticalAlign: "middle",
            // align: 'left'
          },
        },
        animationDurationUpdate: 500, //动画过渡效果的时间，毫秒
      },
    ],
  }
  await nextTick();
  const myChart = echarts.init(chartContainer.value);
  myChart.setOption(option);
  // 同一级只展开一个字节点
  if (option && typeof option === "object") {
    myChart.setOption(option, true);
    myChart.on("mousedown", (e) => {
      const name = e.data.name;
      const curNode = myChart._chartsViews[0]._data.tree._nodes.find((item) => {
        return item.name === name;
      });
      const depth = curNode.depth;
      const curIsExpand = curNode.isExpand;
      myChart._chartsViews[0]._data.tree._nodes.forEach((item, index) => {
        if (item.depth === depth && item.name !== name && !curIsExpand) {
          item.isExpand = false;
        }
      });
    });
  }
  let clickTimer = null; // 全局定时器变量
  myChart.on("click", function (params) {
    clearTimeout(clickTimer);
    clickTimer = setTimeout(function () {
      handleJumpDetail(params.data)
    }, 1000);
  });
  myChart.on("dblclick", function (params) {
    clearTimeout(clickTimer);
    console.log("双击事件", params);
  });
};
const handleJumpDetail = (params) =>{
      router.push({
        path: "/projectStatisticaData/projectDetail",
          query: {
            nodeCode: params.currentNodeCode,
            bidSectionGuid: params.bidSectionGuid,
            rowGuid: params.currentNodeGuid,
            parentNodeCode:'PRESS'
          },
    });
}
defineExpose({ handleInit });
</script>

<style scoped lang="less">
</style>