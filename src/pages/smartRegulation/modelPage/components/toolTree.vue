<template>
    <div id="toolTreeChart" ref="chartContainer"></div>
     <div class="pagination-box">
      <el-button
        class="my-instruct-btn"
        round
        type="primary"
        plain
        @click="handleCloseDraw"
        >关 闭
      </el-button>
    </div>
</template>

<script setup>
let colors = [
    "#546fc6",
    "#546fc6",
    "#7ab1a6",
    "#bec985",
    "#1be471",
    "#fed360",
    "#c44eff",
]
const emit = defineEmits(["closeToolTree"]);
const handleCloseDraw = () => {
  emit('closeToolTree')
}
function addObjFieldToTree(tree) {
    function traverseAndAddObj(node) {
        if (node.children) {
            node.lineStyle = {
                color:colors[Number(node.number)-1]
            }
            node.children.forEach((child) => {
                traverseAndAddObj(child);
            });
        } else {
            node.lineStyle = {
                color:colors[Number(node.number)-1]
            }
        }

    }
    tree.forEach((rootNode) => {
        traverseAndAddObj(rootNode);
    });
    return tree;
}
// 数图配置项
import { ref } from "vue";
let props = defineProps({
    treeData: {
        type: Array,
        default: [],
    },
});
props.treeData = addObjFieldToTree(props.treeData);
console.log(props.treeData);

// 引用 DOM 容器
const chartContainer = ref(null);
let chartInstance = null;
// 计算树的深度，用于动态调整高度
const getTreeDepth = (node) => {
    if (!node.children || node.children.length === 0) return 1;
    return 1 + Math.max(...node.children.map((child) => getTreeDepth(child)));
};
const renderChart = () => {
    const option = {
        backgroundColor: "#E4F6EB",
        tooltip: {
            trigger: "item",
            formatter: "{b}",
        },
        legend: {
            top: "2%",
            left: "3%",
            bottom: "2%",
            orient: "radial",
            data: [
                {
                    name: "系统工具应用分析",
                    icon: "rectangle",
                },
            ],
            selected: {
                系统工具应用分析: true,
            },
            textStyle: {
                color: "#000",
            },
        },
        series: [
            {
                type: "tree",
                name: "系统工具应用分析",
                data: props.treeData,
                top: '0',
                left: '5%',
                bottom: '0',
                right: '15%',
                symbolSize: 6,
                initialTreeDepth: 100,
                label: {
                    normal: {
                        position: "center",
                        verticalAlign: "middle",
                        align: "left",
                        formatter: function (params) {
                            console.log(params)
                            let numberArray = ['1','2','3','4','5','6','7']
                            // 处理子节点文字长度
                            if (params.data.children) {
                                params.data.children.forEach((child, index) => {
                                    if (child.name && child.name.length > 10) {
                                        child.name = child.name.substring(0, 6) + "..."; // 如果超出6个字符，显示5个+..
                                    }
                                });
                            }
                            if(numberArray.indexOf(params.data.number) != -1){
                                if (params.data.name.length > 10) {
                                params.data.name = params.data.name.substring(0, 9) + "..."; //如果超出6个字符，显示5个+..
                                }
                            }
                            if (params.data.number == 1) {
                                return "{a|" + params.data.name + "}";
                            } else if (params.data.number == 2) {
                                if (params.data.name.length > 10) {
                                  params.data.name = params.data.name.substring(0, 6) + "..."; //如果超出6个字符，显示5个+..
                                }
                                return "{b|" + params.data.name + "}";
                            } else if (params.data.number == 3) {
                                return "{c|" + params.data.name + "}";
                            } else if (params.data.number == 4) {
                                return "{d|" + params.data.name + "}";
                            } else {
                                return "{e|" + params.name + "}";
                            }
                            },
                            rich: {
                            a: {
                                padding: 12,
                                borderRadius: 30,
                                fontSize: 16,
                                color: "#fff",
                                backgroundColor: "#546fc6",
                                width: 150,
                                align: "center"
                            },
                            b: {
                                padding: 12,
                                borderRadius: 100,
                                fontSize: 16,
                                color: "#fff",
                                backgroundColor: "#7ab1a6",
                                width: 100,
                                height: 100,
                                align: "center"
                            },
                            c: {
                                padding: 12,
                                borderRadius: 100,
                                fontSize: 16,
                                color: "#fff",
                                backgroundColor: "#bec985",
                                width: 100,
                                height: 100,
                                align: "center"
                            },
                            d: {
                                padding: 12,
                                borderRadius: 30,
                                fontSize: 16,
                                color: "#fff",
                                backgroundColor: "#1be471",
                                width: 150,
                                align: "center"
                            },
                            e: {
                                padding: 12,
                                borderRadius: 30,
                                fontSize: 16,
                                color: "#fff",
                                backgroundColor: "#fed360",
                            },
                        },
                    },
                },
                lineStyle: {
                    color: "#93B9FF",
                    height: 50,
                },
                leaves: {
                    label: {
                        normal: {
                            position: "center",
                            verticalAlign: "middle",
                            align: "left",
                            backgroundColor: "#c44eff",
                            borderRadius: 30,
                            formatter: ["{box|{b}}"].join("\n"),
                            rich: {
                                box: {
                                    padding: 6,
                                    borderRadius: 3,
                                    fontSize: 14,
                                    color: "#fff",
                                },
                            },
                        },
                    },
                },
                emphasis: {
                    // focus: 'descendant'
                },

                expandAndCollapse: false,
                animationDuration: 550,
                animationDurationUpdate: 750
            },
        ],
    };
    chartInstance.setOption(option);
    // 动态调整图表高度
    const treeDepth = getTreeDepth(props.treeData);
    // const newHeight = Math.max(treeDepth * 100, 600); // 根据树的深度动态调整高度
    // chartContainer.value.style.height = `${newHeight}px`;
    chartInstance.resize();
};

onMounted(async () => {
    await nextTick();
    // const myChart = echarts.init(chartContainer.value);
    chartInstance = echarts.init(chartContainer.value);
    // myChart.setOption(option);
    renderChart();
});
</script>

<style scoped lang="less">
#toolTreeChart {
    width: 100%;
    height: 92%;
}
.pagination-box {
          margin-top: 20px;
          display: flex;
          flex-direction: row;
          justify-content: center;
          .my-instruct-btn {
            width: 10%;
           }
        }
</style>