<template>
    <PageWrapper>
        <div style="display: flex" v-loading="isLoading">
            <div class="project-list-model">
                <div style="margin-right:20px;">{{ selectObj.firstCompanyName }}<br/>投标（递交文件）次数 <span
                        class="spanNum" style="color:green;">{{ selectObj.firstNum }}</span> 次
                </div>
                <div class="circle circle-1" ref="circle1">
                    <div class='line' ref="line">
                        <span class='spanLine'>共同投标 <span class="spanNum" style="color:#006600;">{{
                            selectObj.num
                            }}</span> 次</span>
                    </div>
                </div>
                <div class="circle circle-2" ref="circle2"></div>
                <div style="margin-left:20px;"> {{ selectObj.secondCompanyName }}<br/>投标（递交文件）次数<span
                        class="spanNum" style="color:#00ccff;">{{ selectObj.sencondNum }}</span>次
                </div>
            </div>
            <div class="project-list">
                <div class="project-content">
                    <div class="project-list-box">
                        <div style="margin-bottom: 12px; display: flex; align-items: center;">
                            <el-input
                                v-model="searchInfo.companyName"
                                placeholder="请输入投标单位名称"
                                clearable
                                style="width: 240px; margin-right: 8px;"
                                @keyup.enter="handleSearch"
                            />
                            <el-button type="primary" @click="handleSearch">搜索</el-button>
                        </div>
                        <div class="project-list-conotent">
                            <el-table
                                    :data="tableData"
                                    style="width: 100%"
                                    :height="620"
                                    :row-class-name="tableRowClassName"
                                    class="my-table"
                                    @row-click="handleRowClick"
                            >
                                <!-- <el-table-column
                                  label="序号"
                                  type="index"
                                  :index="indexMethod"
                                  align="center"
                                  width="70px"
                                /> -->
                                <el-table-column
                                        v-for="item in tableColData"
                                        :key="item.prop"
                                        align="center"
                                        :prop="item.prop"
                                        :label="item.label"
                                        :minWidth="item.width"
                                >
                                    <template #default="{ row }">
                                        <span>{{ row[item.prop] ? row[item.prop] : "--" }}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column align="center" label="操作" width="120px">
                                    <template #default="{ row }">
                                        <el-button
                                                type="primary"
                                                class="my-detail-btn"
                                                plain
                                                size="small"
                                                round
                                                @click="handleViewDetail(row)"
                                        >查看详情
                                        </el-button>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="pagination-box">
            <el-button
                    class="my-instruct-btn"
                    round
                    type="primary"
                    plain
                    @click="handleClose"
            >关 闭
            </el-button>
            <el-pagination
                    class="pagination"
                    background
                    v-model:current-page="searchInfo.pageNum"
                    v-model:page-size="searchInfo.pageSize"
                    :size="searchInfo.pageSize"
                    :total="total"
                    :page-sizes="[100, 200, 300, 400]"
                    layout="total, sizes, prev, pager, next, jumper"
                    @size-change="handleSizeChange"
                    @current-change="handleChangePage"
            />
        </div>
    </PageWrapper>
    <allDialogView
            ref="BidListEl"
            :dialogTitle="'共同标段列表'"
            :dialogWidth="'95%'"
    >
        <template #content>
            <bidList
                    v-if="BidListEl.showDialog"
                    :firstCompanyCode="firstCompanyCode"
                    :secondCompanyCode="secondCompanyCode"
                    @handleCloseDialog="closeDialog"
            ></bidList>
        </template>
    </allDialogView>
</template>

<script setup>
import bidList from "./bidList.vue";
import {httpPost} from "@wl-fe/http/dist";
import {DATA_ARRAY} from "./content.js";

let props = defineProps({
    stbh: {
        type: String,
        default: "",
    },
});
const emit = defineEmits(["handleCloseDialog"]);
const BidListEl = ref(null);
const firstCompanyCode = ref("");
const secondCompanyCode = ref("");

const handleClose = () => {
    emit("handleCloseDialog");
};

const indexMethod = (index) => {
    return index + 1;
};
const isLoading = ref(false);
let total = ref(1);
const tableColData = ref(DATA_ARRAY["JOINLIST"]);
let searchInfo = reactive({
    pageNum: 1,
    pageSize: 10,
    queryTime: "2025",
    companyName: ""
});
const tableRowClassName = ({rowIndex}) => {
    if (rowIndex % 2 !== 0) {
        return "warning-row";
    }
    return "";
};
const closeDialog = () => {
    BidListEl.value.showDialog = !BidListEl.value.showDialog;
};
const handleViewDetail = (row) => {
    console.log(row);
    firstCompanyCode.value = row.firstCompanyCode;
    secondCompanyCode.value = row.secondCompanyCode;
    BidListEl.value.showDialog = !BidListEl.value.showDialog;
};
const handleSizeChange = (val) => {
    console.log(val);
    searchInfo.pageSize = val;
    handleGetList();
};
const handleChangePage = (val) => {
    console.log(val);
    searchInfo.pageNum = val;
    handleGetList();
};
const handleSearch = () => {
    searchInfo.pageNum = 1;
    handleGetList();
};
onMounted(async () => {
    handleGetList();
});
let tableData = ref([]);
let selectObj = ref({});
const handleGetList = async () => {
    isLoading.value = true;
    let data = await httpPost(
        `/smartRegulationController/getXboxBidInfo?pageNum=${searchInfo.pageNum}&pageSize=${searchInfo.pageSize}`,
        searchInfo
    );
    isLoading.value = false;
    total.value = data.total;
    tableData.value = data.rows;
    selectObj.value = tableData.value[0]
    const {firstNum, sencondNum, num} = selectObj.value;
    hansdleRound(firstNum, sencondNum, num)
};

// 计算圆的半径
function calculateRadius(area) {
    return Math.sqrt(area / Math.PI);
}

// 计算相交面积对应的圆心距离 d'
function calculateNewDistance(r1, r2, intersectArea) {
    // 使用二分法逼近 d'
    let low = Math.abs(r1 - r2); // 最小距离
    let high = r1 + r2; // 最大距离
    let precision = 0.0001; // 精度
    let d = (low + high) / 2; // 初始猜测

    // 二分法迭代
    while (high - low > precision) {
        // 计算当前 d 对应的相交面积
        const area = calculateIntersectArea(r1, r2, d);

        // 调整搜索范围
        if (area < intersectArea) {
            high = d; // 面积太小，减小 d
        } else {
            low = d; // 面积太大，增大 d
        }

        // 更新猜测值
        d = (low + high) / 2;
    }

    return d;
}

// 计算两个圆的相交面积
function calculateIntersectArea(r1, r2, d) {
    if (d >= r1 + r2) return 0; // 无相交
    if (d <= Math.abs(r1 - r2)) return Math.PI * Math.min(r1, r2) ** 2; // 包含关系

    // 计算相交面积
    const part1 = r1 ** 2 * Math.acos((d ** 2 + r1 ** 2 - r2 ** 2) / (2 * d * r1));
    const part2 = r2 ** 2 * Math.acos((d ** 2 + r2 ** 2 - r1 ** 2) / (2 * d * r2));
    const part3 = 0.5 * Math.sqrt((-d + r1 + r2) * (d + r1 - r2) * (d - r1 + r2) * (d + r1 + r2));

    return part1 + part2 - part3;
}

// 主函数
function calculateMoveDistance(area1, area2, intersectArea) {
    // 计算半径
    const r1 = Math.round(calculateRadius(area1));
    const r2 = Math.round(calculateRadius(area2));
    console.log('r1', r1, r2)
    // 初始圆心距离（假设两圆相切）
    const initialDistance = r1 + r2;
    // 计算相交面积为给定值时的圆心距离
    const newDistance = calculateNewDistance(r1, r2, intersectArea);
    // 计算移动距离
    const moveDistance = initialDistance - newDistance;
    return moveDistance;
}

const round1 = ref(0)
const round2 = ref(0)
const moveDistance = ref(0)
const circle1 = ref(null)
const circle2 = ref(null)
const line = ref(null)
const hansdleRound = (firstNum, sencondNum, num) => {
    const area1 = firstNum;
    const area2 = sencondNum;
    const intersectArea = num;
    round1.value = Math.round(calculateRadius(area1)) * 10 * 2;
    round2.value = Math.round(calculateRadius(area2)) * 10 * 2;
    moveDistance.value = Math.round(calculateMoveDistance(area1, area2, intersectArea)) * 10;

    nextTick(() => {
        if (circle1.value) {
            circle1.value.style.width = `${round1.value}px`;
            circle1.value.style.height = `${round1.value}px`;
            line.value.style.height = `${round1.value}px`;
        }
        if (circle2.value) {
            circle2.value.style.width = `${round2.value}px`;
            circle2.value.style.height = `${round2.value}px`;
            circle2.value.style.marginLeft = `-${moveDistance.value}px`;
        }
    });
};
const handleRowClick = (row) => {
    selectObj.value = row;
    const {firstNum, sencondNum, num} = selectObj.value;
    hansdleRound(firstNum, sencondNum, num);
};
</script>
<style scoped lang="less">
.project-list-model {
  width: 50%;
  flex: 1;
  height: 646px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;

  .circle {
    border-radius: 50%;

    mix-blend-mode: multiply;
  }

  .line {
    width: 2px;
    height: 140px;
    background: #ccc;
    position: absolute;
    right: 13%;
    top: -50%;

    .spanLine {
      position: absolute;
      top: -23px;
      right: -48px;
      width: 125px;
    }
  }

  .circle-1 {
    width: 180px;
    height: 180px;
    background: green;
    position: relative;
  }

  .circle-2 {
    background: #00ccff;
    margin-left: -100px;
    width: 200px;
    height: 200px;
    border-radius: 50%;
  }

  .spanNum {
    font-size: 20px;
    margin: 0 5px;
  }
}

.project-list {
  width: 50%;
  height: 100%;

  .project-search-box {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;

    .select {
      width: 200px;
      margin-right: 16px;
      border-radius: 50px;

      :deep(.el-select__wrapper) {
        border-radius: 50px;
      }
    }

    .back-btn {
      width: 90px;
    }

    .success-btn {
      width: 150px;
    }
  }

  .project-content {
    width: 100%;
    height: 60%;
    margin-top: 1%;
    display: flex;
    justify-content: space-between;

    .project-city {
      width: 8%;
      height: 100%;
      margin-right: 10px;
      overflow: auto;
      flex-shrink: 0;
    }

    .project-list-box {
      height: 100%;
      flex: 1;
      width: 0;
      display: flex;
      flex-direction: column;

      .radio-box {
        text-align: right;
      }

      .project-list-conotent {
        width: 100%;
        // height: 82%;
        // margin-top: 1%;
        flex: 1;
        height: 0;


      }
    }
  }
}

.pagination-box {
  margin-top: 20px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}

.my-instruct-btn {
  margin-left: 42%;
  width: 10%;
}

.table-view {
  width: 20px;
  height: 20px;
  border-radius: 20px;
  border: 1px solid #3366ff;
  text-align: center;
  line-height: 22px;
  cursor: pointer;
  margin: 0 auto;
  color: #3366ff;
}

.my-img {
  width: 20px;
  margin-right: 5px;
}

.wjIcon {
  width: 30px;
  height: auto;
}

.my-img-yj {
  width: 30px;
}

.span-red {
  color: #ff0000;

  &::before {
    content: "●";
    color: #ff0000;
    font-size: 15px;
    margin-right: 5px;
  }
}

.span-orign {
  color: #ff9900;

  &::before {
    content: "●";
    color: #ff9900;
    font-size: 15px;
    margin-right: 5px;
  }
}

.span-yellow {
  color: #cc9b08;

  &::before {
    content: "●";
    color: #cc9b08;
    font-size: 15px;
    margin-right: 5px;
  }
}

.step-span {
  display: inline-block;
  width: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.status-red {
  color: red;
}

.status-green {
  color: green;
}

.status-orign {
  color: #f4871c;
}

/* 设置滚动条的样式 */
.project-city-box::-webkit-scrollbar {
  width: 6px;
  box-sizing: border-box;
}

/* 滚动条滑块 */
.project-city-box::-webkit-scrollbar-thumb {
  height: 5px;
  border-radius: 5px;
  background-color: #c2cede;
}

/*滚动槽*/
.project-city-box::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0);
  border-radius: 10px;
  background: #ffffff;
}

/* 滚动条滑块 鼠标悬浮 */
.project-city-box::-webkit-scrollbar-thumb:hover {
  background-color: #c2cede;
  // border: 1px solid rgba(0, 0, 0, 0.21);
  cursor: pointer;
}

.my-table {
  :deep(.el-table__header-wrapper) {
    .el-table__cell {
      background: #f0f6ff;
      height: 53px;
      color: rgba(0, 0, 0, 0.88);
    }
  }

  :deep(.el-table__row) {
    height: 53px;
  }

  :deep(.warning-row) {
    background: #f9fbff;
  }
}

:deep(.my-descriptions) {
  // background: #dceaff;
  font-size: 16px;
  width: 22%;
  color: #000;
  text-align: center;
  background: #f6faff;
}
</style>
