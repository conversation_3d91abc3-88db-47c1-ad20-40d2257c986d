<template>
    <el-dialog v-model="cardDialogVisible" width="90%" style="padding: 0px;" :before-close="handleClose" :show-close="false" class="detailClassCard">
        <template #header="{  }">
            <!-- <div class="my-header">
                <h4 :id="titleId" :class="titleClass">智能分析内容</h4>
            </div> -->
            <div class="dialog-header unify-title unify-title7">查看详情</div>
        </template>
        <div class="dialog-content">
            <div class="dialog-content-left">
                <div class="detail-item">
                    <div class="detail-item-header">
                        <img src="../../../../assets/projectAi/tbrmc.png" width="20" height="20" />
                        <span>投标人名称</span>
                    </div>
                    <div class="detail-item-content">
                        <span>{{ companyName }}</span>
                    </div>
                </div>
                <div class="detail-item">
                    <div class="detail-item-header">
                        <img src="../../../../assets/projectAi/yjjy.png" width="20" height="20" />
                        <span>表述非必要性雷同内容</span>
                    </div>
                    <div class="detail-item-content detail-item-content-all">
                        <div v-for="(item, index) in listData" :key="index" class="words">
                            <div @click.stop="checkPDF(item, index)" class="text">内容{{ index + 1}}: {{ item.words }} ( 出现次数 <span style="color: red;font-size: 16px;font-weight: 600;">{{ item.results.length }}</span> 次 )</div>
                            <div @click.stop="checkPDF(item, index)" class="check-btn" :style="{background: activeIndex === index ? '#139cd4': '#9a9a9a'}">查看</div>
                        </div>
                    </div>
                </div>
                <div class="detail-item">
                    <div class="detail-item-header">
                        <img src="../../../../assets/projectAi/zzcl.png" width="20" height="20" />
                        <span>佐证材料</span>
                    </div>
                    <div class="detail-item-content text-left detail-item-content-all1">
                        <div class="compare-title" v-show="pdfList.length > 0">
                            <span>根据智能文本比对,找到以下模块中包含</span><span>【<span style="color: #3d41d8;font-weight: bolder">内容{{ activeIndex + 1 }}</span>】</span><span>一致的内容:</span>
                        </div>
                        <div class="my-text" v-for="(p, i) in pdfList" :key="i">
                            <div @click="watchPDF(p.url)">

                                <span class="pdf-url"><span>{{ i + 1}}.</span>{{ p.fileName }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="dialog-content-right">
                <div class="detail-item">
                    <div class="detail-item-header">
                        <img src="../../../../assets/projectAi/tbrmc.png" width="20" height="20" />
                        <span>涉及评审因素</span>
                    </div>
                    <div class="detail-item-content" v-if="filePath.length > 0" style="height: 55vh">
                        <iframe
                                id="iframe"
                                ref="iframe"
                                width="100%"
                                height="100%"
                                frameborder="0"
                                :src="'https://netbid-new.lnwlzb.com/pdfjs/web/viewer.html?file='+filePath+'&keyword='+highlightWord+'' ">
                        </iframe>
                    </div>
                    <div class="detail-item-content" v-else style="height: 55vh">
                        <el-empty />
                    </div>
                </div>
            </div>
        </div>
        <template #footer>
            <div class="dialog-footer">
                <el-button @click="cardDialogVisible = false" class="close-btn" round>关闭</el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup>
import { ref } from 'vue'
import { httpPost } from "@wl-fe/http/dist";
defineProps({
  modelCode: {
    type: String,
    default: ''
  }
});
const cardDialogVisible = ref(false)
const activeIndex = ref(-1)
const openDialog = (status) => {
  cardDialogVisible.value = status
}
const filePath = ref('')
const highlightWord = ref('')
const listData = ref([])
const pdfList = ref([])
const companyName = ref('')
const handleClose = () => {
  console.log()
}
const checkPDF = (obj,index) => {
  activeIndex.value = index
  highlightWord.value = obj.words
  const foundItem = listData.value.find(item => item.id === obj.id);
  if (foundItem) {
    pdfList.value = foundItem.results.map(result => ({
      fileName: result[0],
      url: result[3]
    }));
  } else {
    pdfList.value = [];
  }
  // console.log(pdfList.value)
}
const getDetail = async (obj, code, keyword) => {
  companyName.value = keyword
  const param = {
    bidId: obj.bidId,
    tbrId: obj.tbrId,
    modelCode: code
  }
  const res = await httpPost("/smartRegulationController/getModelDetailByTSL", param);
  listData.value = res.ynxjc.ltxjc.map((item) => {
    item.results = JSON.parse(item.results)
    return {
      ...item
    }
  })
  checkPDF(listData.value[0], 0)
  watchPDF(pdfList.value[0].url)
}
const watchPDF = (url) => {
  console.log(url)
  filePath.value = url
}
defineExpose({
  openDialog,
  getDetail
})
</script>

<style scoped lang="less">
.my-header {
  background: #016ff8!important;
  padding: 6px;
  .el-dialog__title {
    color: #FFFFFF;
  }
}
.dialog-header {
  font-size: 18px;
  font-weight: bold;
  color: #000;
}
.unify-title {
  line-height: 45px;
  height: 45px;
  &:after{
    opacity: 0.4;
    left: -5px;
    width: 40%;
  }
  &:before{
    left: -5px;
  }
}
.dialog-content {
  padding: 30px;
  display: flex;
  background: #F2F6FD !important;
  // height: 60vh;
  &-left {
    width: 45%;
    height: 100%;
    .detail-item {
      width: 100%;
      height: 30%;
      &-header {
        font-size: 18px;
        font-weight: bolder;
        margin-bottom: 10px;
        margin-top: 10px;
        display: flex;
        align-items: center;
        img {
          margin-right: 10px;
        }
      }
      .text-left {
        text-align: left;
      }
      &-content {
        background: #FFFFFF;
        text-align: center;
        padding: 10px;
        .words {
          display: flex;
          justify-content: space-between;
        }
        .check-btn {
          width: 60px;
          background: #9a9a9a;
          text-align: center;
          border-radius: 8px;
          margin-bottom: 9px;
          color: #ffffff;
          cursor: pointer;
          font-size: 16px;
          margin-right: 10px;

        }
        .text{
          cursor: pointer;
          &:hover{
            text-decoration: underline;
          }
        }
        .is-check {
          background: #139cd4;
        }
        .pdf-url {
          text-decoration: underline;
          color: #2564bc;
          cursor: pointer;
          font-weight: bolder;
        }

      }
      .detail-item-content-all{
        height: 161px;
        overflow-y: auto;
      }
      .detail-item-content-all1{
        height: 221px;
        overflow-y: auto;
        .compare-title{
          font-size: 16px;
          line-height: 26px;
        }
        .my-text{
          line-height: 30px;
        }
      }
    }
  }
  &-right {
    width: 55%;
    margin-left: 20px;
    height: 100%;
    .detail-item {
      width: 100%;
      &-header {
        font-size: 18px;
        font-weight: bolder;
        margin-bottom: 10px;
        margin-top: 10px;
        display: flex;
        align-items: center;
        img {
          margin-right: 10px;
        }
      }
      .text-left {
        text-align: left;
      }
      &-content {
        background: #FFFFFF;
        text-align: center;
        padding: 10px;
        .words {
          display: flex;
        }
        .check-btn {
          width: 60px;
          background: #9a9a9a;
          text-align: center;
          border-radius: 8px;
          margin-bottom: 10px;
          color: #ffffff;
          cursor: pointer;
          font-size: 16px;
        }
        .is-check {
          background: #139cd4;
        }
        .pdf-url {
          text-decoration: underline;
          color: #2564bc;
          cursor: pointer;
          font-weight: bolder;
        }

      }
    }
  }
  .height-100 {
    height: 100%;
  }
}
.dialog-footer {
  display: flex;
  justify-content: center;
  align-items: center;
  padding-bottom: 20px;
  background: #F2F6FD !important;
  padding-top: 0px;
  .close-btn {
    width: 180px;
  }
}

</style>
<style  lang="less">
.detailClassCard {
  .el-dialog__header {
    padding-bottom: 0px ;
  }
  .el-dialog__footer {
    padding-top: 0px ;
  }
}
</style>
