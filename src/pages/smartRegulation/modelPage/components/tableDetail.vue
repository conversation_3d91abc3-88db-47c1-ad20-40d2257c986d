<template>
    <el-dialog v-model="tableDetailVisible" width="65%" style="padding: 0px;" :before-close="handleClose" :show-close="false"  class="detail-class">
        <template #header="{ }">
            <!-- <div class="my-header">
                <h4 :id="titleId" :class="titleClass">查看详情</h4>
            </div> -->
            <div class="dialog-header unify-title unify-title7">查看详情</div>
        </template>
        <div class="dialog-content">
            <el-table :data="tableData" max-height="50vh" class="my-table"  :row-class-name="tableRowClassName">
                <el-table-column
                    min-width="15%"
                    v-for="(column, index) in columnList"
                    :key="index"
                    :label="column.label"
                    :prop="column.prop">
                    <template #default="scope">
                        <div
                          v-if="['authorisationState', 'qualificationState', 'filedownstatus', 'fileuploadstatus', 'swipingState'].includes(column.prop)"
                          :style="{ color: scope.row[column.prop] === '已完成' ? 'green' : 'red' }">
                            {{ scope.row[column.prop] }}
                        </div>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <div class="flex-page">
            <el-pagination
                layout="prev, pager, next,total"
                :total="total"
                v-model:current-page="pageNum"
                v-model:page-size="pageSize"
                @current-change="handleChangePage" />
        </div>

        <template #footer>
            <div class="dialog-footer">
                <el-button @click="tableDetailVisible = false" class="close-btn" round>关闭</el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup>
import { ref } from 'vue'
import { httpPost } from "@wl-fe/http/dist";
const tableDetailVisible = ref(false)
const tableData = ref([])
const columnList = ref([])
const total = ref(0)
const pageNum = ref(1)
const pageSize = ref(10)
const typeIndex = ref('')
const modelCodeNum = ref('')
const keywordVal  = ref('')
const queryTimeVal = ref('')
const handleClose = () => {

}
const tableRowClassName = ({ rowIndex }) => {
  if (rowIndex % 2 !== 0) {
    return "warning-row";
  }
  return "";
};

const openDialog = (status) => {
    tableDetailVisible.value = status
}
const getTableDetail = async (type ,modelCode, keyword, queryTime, ids) => {
    typeIndex.value = type
    modelCodeNum.value = modelCode
    keywordVal.value = keyword
    queryTimeVal.value = queryTime
    const param = {
        type,
        modelCode,
        keyword,
        queryTime,
        pageNum: pageNum.value,
        pageSize: pageSize.value,
        ids: ids
    }
    const data = await httpPost("/smartRegulationController/getModelDetail" + `?pageNum=${pageNum.value}&pageSize=${pageSize.value}` , param);
    columnList.value = Object.entries(data.tableHeader).map(([prop, label]) => ({ label, prop }));
    tableData.value = data.rows
    total.value = data.total
}
const handleChangePage = (val) => {
    pageNum.value = val
    getTableDetail(typeIndex.value, modelCodeNum.value, keywordVal.value, queryTimeVal.value);
};
defineExpose({
    openDialog,
    getTableDetail
})
</script>

<style scoped lang="less">
.dialog-header {
  font-size: 18px;
  font-weight: bold;
  color: #000;
}
.unify-title {
  line-height: 45px;
  height: 45px;
  &:after{
    opacity: 0.4;
    left: -5px;
    width: 40%;
  }
  &:before{
    left: -5px;
  }
}
.my-header {
    background: #016ff8!important;
    padding: 6px;
    .el-dialog__title {
        color: #FFFFFF;
        margin-bottom: 0px;
        line-height: 35px;
    }
}
.dialog-content {
    padding: 30px;
    display: flex;
    max-height: 60vh;
    padding-top: 10px;
}
.flex-page {
    display: flex;
    justify-content: center;
    align-items: center;
}
.my-table {
  :deep(.el-table__header-wrapper) {
    .el-table__cell {
      background: #f0f6ff;
      height: 55px;
      color: rgba(0, 0, 0, 0.88);
    }
  }
  :deep(.el-table__row) {
    height: 55px;
  }
  :deep(.warning-row) {
    background: #f9fbff;
  }
}
.dialog-footer {
    display: flex;
    justify-content: center;
    align-items: center;
    padding-bottom: 20px;
    .close-btn {
        width: 180px;
    }
}
</style>
