<template>
  <div id="pinEchart" ref="chartContainer"></div>
</template>
  
  <script setup >
let props = defineProps({
  pieData: {
    type: Array,
    default: [],
  },
});
let option = {
  tooltip: {
    trigger: "item",
    formatter: "{b} : ({d}%)",
  },
  color: ["#9bc4fc", "#d1c3fc"],
  legend: {
    top: "bottom",
    padding: [0, 0, 0, 0]
  },
  series: [
    {
      type: "pie",
      radius: "80%",
      center: ["50%", "50%"],
      selectedMode: "single",
      hoverOffset: 3,
      selectedOffset: 5,
      label: {
        normal: {
          show: true,
          position: "inside",
          color: "#000",
          fontSize: 18,
          formatter: "{b}\n {d}%",
        },
      },
      data: props.pieData,
      // [
      //   {
      //     value: 2,
      //     name: "中标",
      //     selected: true,
      //   },
      //   {
      //     value: 8,
      //     name: "未中标",
      //   },
      // ],
      itemStyle: {
        normal: {
          borderColor: "#fff",
          borderWidth: 2,
          shadowBlur: 2,
          shadowOffsetX: 0,
          shadowColor: "#00a5f4",
          opacity: 1,
        },
      },
    },
  ],
};

const chartContainer = ref(null);
onMounted(async () => {
  await nextTick();
  const myChart = echarts.init(chartContainer.value);
  myChart.setOption(option);
});
</script>
  
<style scoped lang="less">
#pinEchart {
  width: 100%;
  height: 100%;
}
</style>