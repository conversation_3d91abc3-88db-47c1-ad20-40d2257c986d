export const DATA_ARRAY = {
    WIN_BIDDER_LIST: [
        {
            label: "标段（包）编号",
            prop: "bidSectionCode",
        },
        {
            label: "标段（包）名称",
            prop: "bidSectionName",
        },
        {
            label: "招标项目编号",
            prop: "tenderProjectCode",
        }, {
            label: "招标项目名称",
            prop: "tenderProjectName",
        },
        {
            label: "标段类型",
            prop: "bidSectionContentName",
        },
        {
            label: "项目地区",
            prop: "regionName",
        },
        {
            label: "招标人",
            prop: "tendererName",
        },
        {
            label: "代理机构",
            prop: "tenderAgentName",
        },
        {
            label: "开标时间",
            prop: "bidOpenTime",
        },
        {
            label: "中标时间",
            prop: "approvalTime",
        },
        {
            label: "中标单位",
            prop: "winBidderName",
        }, {
            label: "中标单位统一社会信用代码",
            prop: "winBidderCode",
        }, {
            label: "项目负责人",
            prop: "bidManager",
        }, {
            label: "中标金额",
            prop: "bidPrice"
        }
    ],
    TENDER_RECORD_LIST: [
        {
            label: "招标项目编号",
            prop: "tenderProjectCode",
        }, {
            label: "招标项目名称",
            prop: "tenderProjectName",
        },
        {
            label: "标段（包）编号",
            prop: "bidSectionCode",
        },
        {
            label: "标段（包）名称",
            prop: "bidSectionName",
        },
        {
            label: "标段合同估算价",
            prop: "contractReckonPrice",
        },
        {
            label: "项目地区",
            prop: "regionName",
        },
        {
            label: "招标人",
            prop: "tendererName",
        },
        {
            label: "代理机构",
            prop: "tenderAgentName",
        },
        {
            label: "备案状态",
            prop: "recordStatus",
        }, {
            label: "核验状态",
            prop: "approvalStatus"
        }, {
            label: "核验通过时间",
            prop: "approvalTime"
        }
    ],
    DC_RECORD_LIST: [
        {
            label: "招标项目名称",
            prop: "tenderProjectName",
        },
        {
            label: "标段（包）编号",
            prop: "bidSectionCode",
        },
        {
            label: "标段（包）名称",
            prop: "bidSectionName",
        },
        {
            label: "项目地区",
            prop: "regionName",
        },
        {
            label: "招标人",
            prop: "tendererName",
        },
        {
            label: "中标单位",
            prop: "winBidderName",
        }, {
            label: "项目负责人",
            prop: "bidManager",
        }, {
            label: "中标金额",
            prop: "bidPrice"
        },
        {
            label: "备案状态",
            prop: "recordStatus",
            width: 60,
        }, {
            label: "核验状态",
            prop: "approvalStatus",
            width: 60,
        }, {
            label: "核验通过时间",
            prop: "approvalTime"
        }
    ],
    TENDER_OPEN_LIST: [
        {
            label: "招标项目名称",
            prop: "tenderProjectName",
        },
        {
            label: "标段（包）编号",
            prop: "bidSectionCode",
        },
        {
            label: "标段（包）名称",
            prop: "bidSectionName",
        },
        {
            label: "项目地区",
            prop: "regionName",
        },
        {
            label: "招标人",
            prop: "tendererName",
        },
        {
            label: "代理机构",
            prop: "tenderAgentName",
        },
        {
            label: "开标时间",
            prop: "bidOpenTime",
        }, {
            label: "标段合同估算价",
            prop: "contractReckonPrice",
        }
    ]
}
