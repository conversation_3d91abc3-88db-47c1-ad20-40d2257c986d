<template>
    <div class="search-button-box">
        <el-form :inline="true" :model="form" class="search-form">
            <!-- 第一行表单 -->
            <template v-if="modelCode === '54' || modelCode === '55'">
                <el-form-item label="招标项目名称" class="custom-form-item">
                    <el-input v-model="form.tenderProjectName" placeholder="请输入招标项目名称" clearable/>
                </el-form-item>
                <el-form-item label="招标项目编号" class="custom-form-item">
                    <el-input v-model="form.tenderProjectCode" placeholder="请输入招标项目编号" clearable/>
                </el-form-item>
                <el-form-item label="标段名称" class="custom-form-item" v-if="modelCode === '54'">
                    <el-input v-model="form.bidSectionName" placeholder="请输入标段名称" clearable/>
                </el-form-item>
                <el-form-item label="标段编号" class="custom-form-item" v-if="modelCode === '54'">
                    <el-input v-model="form.bidSectionCode" placeholder="请输入标段编号" clearable/>
                </el-form-item>
                <el-form-item label="招标人" class="custom-form-item">
                    <el-input v-model="form.tendererName" placeholder="请输入招标人名称" clearable/>
                </el-form-item>
                <el-form-item label="招标代理" class="custom-form-item">
                    <el-input v-model="form.tenderAgentName" placeholder="请输入招标代理名称" clearable/>
                </el-form-item>
                <!-- 第二行表单 -->
                <el-form-item label="中标单位名称" class="custom-form-item" v-if="modelCode === '54'">
                    <el-input v-model="form.winBidderName" placeholder="请输入中标单位名称" clearable/>
                </el-form-item>
                <el-form-item label="项目负责人" class="custom-form-item" v-if="modelCode === '54'">
                    <el-input v-model="form.bidManager" placeholder="请输入项目负责人姓名" clearable/>
                </el-form-item>
                <el-form-item label="标段分类" class="custom-form-item">
                    <el-select v-model="form.bidSectionType" placeholder="请选择标段分类">
                        <el-option v-for="item in sectionCategoryOptions" :key="item.value" :label="item.label"
                                   :value="item.value"/>
                    </el-select>
                </el-form-item>
                <el-form-item label="备案时间" class="select-form-item" >
                    <el-date-picker
                        v-model="form.bidWinningTime"
                        type="daterange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        value-format="YYYY-MM-DD"
                    />
                </el-form-item>
                <el-form-item label="招标方式" class="custom-form-item" v-if="modelCode === '54'">
                    <el-select v-model="form.tenderMode" placeholder="请选择招标方式">
                        <el-option v-for="item in tenderTypeOptions" :key="item.value" :label="item.label"
                                   :value="item.value"/>
                    </el-select>
                </el-form-item>
                <el-form-item label="项目类别" class="custom-form-item">
                    <el-select v-model="form.industriesType" placeholder="请选择项目类别">
                        <el-option v-for="item in tradeCategoryOptions" :key="item.value" :label="item.label"
                                   :value="item.value"/>
                    </el-select>
                </el-form-item>
                <div v-if="!isExpanded && modelCode === '54'" style="margin-bottom: 10px; font-size: 20px;  font-weight: bold;">
                    中标金额合计：{{ props.model54Count }}
                </div>
                <!-- 搜索按钮和展开按钮在第二行末尾（未展开状态） -->
                <template v-if="!isExpanded">
                    <el-form-item>
                        <el-button type="primary" @click="onSearch">搜索</el-button>
                        <el-button type="warning" @click="onExport">导出</el-button>
                        <el-button @click="onReset">重置</el-button>
                    </el-form-item>
                    <el-form-item v-if="modelCode === '54'">
                        <el-button type="text" @click="toggleExpand">
                            {{ isExpanded ? '收起' : '展开' }}
                            <el-icon class="el-icon--right">
                                <component :is="isExpanded ? 'ArrowUp' : 'ArrowDown'"/>
                            </el-icon>
                        </el-button>
                    </el-form-item>
                </template>

                <!-- 展开后的额外表单 -->
                <template v-if="isExpanded">

                    <el-form-item label="外埠单位" class="custom-form-item">
                        <el-select v-model="form.isForeignUnit" placeholder="请选择">
                            <el-option label="是" value="1"/>
                            <el-option label="否" value="0"/>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="开标时间" class="select-form-item">
                        <el-date-picker
                                v-model="form.bidOpenTime"
                                type="daterange"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                                value-format="YYYY-MM-DD"
                        />
                    </el-form-item>
                    <el-form-item label="设计类中标金额>300万" class="custom-form-item">
                        <el-select v-model="form.isDesignOver3M" placeholder="请选择">
                            <el-option label="是" value="1"/>
                            <el-option label="否" value="0"/>
                        </el-select>
                    </el-form-item>
                    <div  style="margin-bottom: 10px; font-size: 20px;  font-weight: bold;">
                        中标金额合计：{{ props.model54Count }}
                    </div>
                    <!-- 搜索按钮和收起按钮在最后一行（展开状态） -->
                    <el-form-item>
                        <el-button type="primary" @click="onSearch">搜索</el-button>
                        <el-button type="warning" @click="onExport">导出</el-button>
                        <el-button @click="onReset">重置</el-button>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="text" @click="toggleExpand">
                            {{ isExpanded ? '收起' : '展开' }}
                            <el-icon class="el-icon--right">
                                <component :is="isExpanded ? 'ArrowUp' : 'ArrowDown'"/>
                            </el-icon>
                        </el-button>
                    </el-form-item>
                </template>
            </template>

            <!-- modelCode 为 55 时只显示招标项目名称和编号 -->
            <template v-else-if="modelCode === '55'|| modelCode === '56' || modelCode === '57'">
                <el-form-item label="招标项目名称" class="custom-form-item">
                    <el-input v-model="form.tenderProjectName" placeholder="请输入招标项目名称" clearable/>
                </el-form-item>
                <el-form-item label="标段名称" class="custom-form-item">
                    <el-input v-model="form.bidSectionName" placeholder="请输入标段名称" clearable/>
                </el-form-item>
                <el-form-item label="标段编号" class="custom-form-item">
                    <el-input v-model="form.bidSectionCode" placeholder="请输入标段编号" clearable/>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">搜索</el-button>
                    <el-button type="warning" @click="onExport">导出</el-button>
                    <el-button @click="onReset">重置</el-button>
                </el-form-item>
            </template>
        </el-form>
    </div>
</template>

<script setup>
import {reactive, ref} from 'vue';
import {getOptionList} from "@/pages/project/projectBadBehavior/formOptionList.js";
import {httpGet} from "@wl-fe/http/dist/index.js";
import {ArrowUp, ArrowDown} from '@element-plus/icons-vue'

const props = defineProps({
    modelCode: {
        type: String,
        default: ''
    },
    model54Count: {
        type: [String, Number],
        default: 0
    }
});

const emit = defineEmits(['search', 'export']);
const isExpanded = ref(false);

const form = reactive({
    bidSectionType: '',
    bidOpenTime: [],
    bidOpenStartTime: '',
    bidOpenEndTime: '',
    bidSectionName: '',
    bidSectionCode: '',
    tendererName: '',
    tenderAgentName: '',
    winBidderName: '',
    bidManager: '',
    tenderMode: '',
    industriesType: '',
    isForeignUnit: '',
    projectCategory: '',
    bidWinningTime: [],
    bidWinningStartTime: '',
    bidWinningEndTime: '',
    isDesignOver3M: '',
    tenderProjectName: '',
    tenderProjectCode: '',
});

const sectionCategoryOptions = ref([]);
const tradeCategoryOptions = ref([]);
const tenderTypeOptions = ref([]);

function onSearch() {
    if (form.bidOpenTime) {
        form.bidOpenStartTime = form.bidOpenTime[0];
        form.bidOpenEndTime = form.bidOpenTime[1];
    }
    if (form.bidWinningTime) {
        form.bidWinningStartTime = form.bidWinningTime[0];
        form.bidWinningEndTime = form.bidWinningTime[1];
    }
    emit('search', {...form});
}

function onExport() {
    if (form.bidOpenTime) {
        form.bidOpenStartTime = form.bidOpenTime[0];
        form.bidOpenEndTime = form.bidOpenTime[1];
    }
    if (form.bidWinningTime) {
        form.bidWinningStartTime = form.bidWinningTime[0];
        form.bidWinningEndTime = form.bidWinningTime[1];
    }
    emit('export', {...form});
}

function toggleExpand() {
    isExpanded.value = !isExpanded.value;
}

function onReset() {
    Object.keys(form).forEach(key => {
        if (Array.isArray(form[key])) {
            form[key] = [];
        } else {
            form[key] = '';
        }
    });
}

onMounted(async () => {
    sectionCategoryOptions.value = await getOptionList("bdwl");
    tradeCategoryOptions.value = await getOptionList("hyfl");
    tenderTypeOptions.value = await getOptionList("zbfs");
});
</script>

<style scoped>
.search-button-box {
    display: flex;
    align-items: center;
}

.search-form {
    width: 100%;
}

.custom-form-item {
    width: 300px;
}

.select-form-item {
    width: 400px;
}

:deep(.el-form-item__content) {
    margin-bottom: 10px;
}

.el-button--text {
    padding: 0;
    margin-left: 8px;
}

.el-icon--right {
    margin-left: 4px;
}
</style> 