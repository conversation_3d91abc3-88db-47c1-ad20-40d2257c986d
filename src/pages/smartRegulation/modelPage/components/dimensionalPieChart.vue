<template>
  <div class="first-type">
    <div class="content-box">
      <div class="content-con">
        <div class="content-blank">
          <div
            class="content-blank-box"
            v-for="(item, index) in pieInfo.infoData"
            :key="index"
          >
            <div class="text-left">{{ item.name }}</div>
            <div class="text-right">
              <span>{{ item.value }}</span>
              <span class="unit">%</span>
            </div>
          </div>
        </div>
        <div class="content-chart">
          <pieChart
            v-if="isShow"
            :pieData="pieInfo.pieData"
            :colorArray="selectColor()"
          ></pieChart>
        </div>
      </div>
    </div>
  </div>
</template>
  <script setup >
import { ref } from "vue";
defineProps({
  pieInfo: {
    type: Object,
    default: () => ({}),
  },
});
const isShow = ref(true);
import pieChart from "./pieChart.vue";
function selectColor() {
  console.log("走了");
  return ["#fabc05", "rgb(240 144 0)", "#e91d28"];
}
</script>
  
  <style scoped lang="less">
.first-type {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  .icon-box {
    width: 10%;
    height: auto;
  }
  img {
    width: 100%;
    height: auto;
  }
  .content-box {
    width: 100%;
    height: 100%;
    .content-her {
      height: 15%;
      width: 100%;
      border-radius: 8px;
      box-sizing: border-box;
      padding: 15px;
      .s-text {
        line-height: 30px;
        .text-f {
          font-size: 20px;
          font-weight: bold;
        }
      }
    }
    .content-con {
      height: 90%;
      width: 100%;
      margin: 2% 0%;
      border-radius: 8px;
      display: flex;
      justify-content: space-between;
      background: aliceblue;
      .content-blank {
        width: 35%;
        height: 100%;
        border-radius: 8px;
        box-sizing: border-box;
        padding: 10px 0px;
        overflow-y: auto;
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        .content-blank-box {
          width: 86%;
          height: 80px;
          // border: 1px solid #ccc;
          background: linear-gradient(to right, #a5c4fc, #7f70fd);
          border-radius: 5px;
          box-sizing: border-box;
          padding: 10px 12px;
          margin: 0 auto;
          //margin-bottom: 58px;
          //margin-top: 20px;
          position: relative;
          margin-bottom: 20px;
          .text-detail {
            position: absolute;
            bottom: -25px;
            right: 14px;
            color: blue;
            border-bottom: 1px solid blue;
            cursor: pointer;
          }
          .text-right {
            line-height: 26px;
            text-align: right;
            font-size: 24px;
            font-weight: bold;
            color: #ffcc33;
            .unit {
              color: #ffffff;
              font-size: 16px;
              margin-left: 2px;
            }
            .white {
              color: #ffffff;
              font-size: 16px;
            }
          }
          .text-left {
            line-height: 26px;
            color: #fff;
            .power-icon {
              width: 25px;
              height: 25px;
            }
            .flex-icon {
              display: flex;
              align-items: center;
            }
          }
        }
      }
      .content-chart {
        width: 63%;
        height: 100%;
        border-radius: 8px;
        // border: #ccc 1px solid;
      }
    }
  }
}
</style>
  <style >
.text-s-html {
  line-height: 30px;
  font-size: 18px;
  font-weight: 600;
  margin-top: 30px;
  font-family: SourceHanSansCN, SourceHanSansCN;
}
.text-f-html {
  font-size: 20px;
  font-weight: bold;
  color: #1f38c1;
}
</style>
  