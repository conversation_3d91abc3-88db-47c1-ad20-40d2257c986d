<template>
  <div id="funnelEchart" ref="chartContainer"></div>
</template>
      
      <script setup >
let props = defineProps({
  funnelData: {
    type: Array,
    default: () => [],
  },
});
let option = {
//   title: {
//     text: "漏斗分析图",
//     subtext: "网站用户行为统计－纯属虚构",
//     x: "center",
//     textStyle: {
//       color: "#fff",
//     },
//   },
  tooltip: {
    trigger: "item",
    formatter: function (params) {
      return params.name + '\n' + params.value;
    },
  },
//   backgroundColor: "#000237",
  color: ["#f59a23", "#c280ff", "#8080ff"],

  series: [
    {
      name: "漏斗图",
      type: "funnel",
      x: "10%",
      y: 60,
      //x2: 80,
      y2: 60,
      width: "80%",
      // height: {totalHeight} - y - y2,
      min: 0,
      max: 90,
      minSize: "40%",
      maxSize: "100%",
      sort: "descending", // 'ascending', 'descending'
      gap: 25,
      data:props.funnelData.sort(function (a, b) {
        return a.value - b.value;
      }),
      roseType: true,
      label: {
        normal: {
          color: "#fff",
          formatter: function (params) {
            return (
              params.name + "\n{style|" + params.value + "个" + "}"
            );
          },
          position: "center",
          rich: {
            style: {
              fontSize: "16px",
              // marginTop: 20
              lineHeight: 32
            },
            part: {
              color: "#eee",
              marginLeft: "10px",
            },
          },
        },
      },
      itemStyle: {
        normal: {
          borderWidth: 0,
          shadowBlur: 30,
          shadowOffsetX: 0,
          shadowOffsetY: 10,
          shadowColor: "rgba(0, 0, 0, 0.5)",
        },
      },
    },
  ],
};

const chartContainer = ref(null);
onMounted(async () => {
  await nextTick();
  const myChart = echarts.init(chartContainer.value);
  myChart.setOption(option);
});
</script>
      
    <style scoped lang="less">
#funnelEchart {
  width: 100%;
  height: 100%;
}
</style>