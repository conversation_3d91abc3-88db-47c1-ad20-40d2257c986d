<template>
    <div id="treeChart" ref="chartContainer"></div>
</template>

<script setup>
import { ref } from 'vue'
let props = defineProps({
    treeData: {
        type: Array,
        default: [],
    },
});
const colors = [
    "#00ADD0",
    "#FFA12F",
    "#B62AFF",
    "#604BFF",
    "#6E35FF",
    "#002AFF",
    "#20C0F4",
    "#95F300",
    "#04FDB8",
    "#AF5AFF"
]
const getData = () => {
    // let data = {
    //     name: "根节点1",
    //     value: 0,
    //     children: []
    // };
    // for (let i = 1; i <= 10; i++) {
    //     let obj = {
    //         name: "节点" + i,
    //         value: i,
    //         children: [],
    //     };
    //     for (let j = 1; j <= 5; j++) {
    //         let obj2 = {
    //             name: `节点1-${i}-${j}`,
    //             value: 1 + "-" + i + "-" + j,
    //         };
    //         if (j % 2 == 1) {
    //             obj2.children = [];
    //             for (let k = 1; k <= 3; k++) {
    //                 let obj3 = {
    //                     name: `节点1-${i}-${j}-${k}`,
    //                     value: 1 + "-" + i + "-" + j + '-' + k,
    //                 };
    //                 obj2.children.push(obj3);
    //             }
    //         }
    //         obj.children.push(obj2);
    //     }
    //     data.children.push(obj);
    // }
    let arr = props.treeData;
    arr = handle(arr, 0);
    console.log('arr', arr)
    return arr;
};
const handle = (data, index, color = '#00f6ff') => {
    return data.map((item, index2) => {
        if (index === 1) {
            color = colors[index2 % 10];
        }
        if (index === 0 || index === 1) {
            item.label = {
                position: "inside",
            };
        }
        switch (index) {
            case 0:
                item.symbolSize = 70;
                break;
            case 1:
                item.symbolSize = 50;
                break;
            default:
                item.symbolSize = 10;
                break;
        }
        item.lineStyle = { color: color };

        if (item.children) {
            item.itemStyle = {
                borderColor: color,
                color: color
            };
            item.children = handle(item.children, index + 1, color);
        } else {
            item.itemStyle = {
                color: 'transparent',
                borderColor: color
            };
        }
        return item;
    });
};
const option = {
    type: "tree",
    // backgroundColor: "#000",
    toolbox: {
        show: true,
        iconStyle: {
            borderColor: "#03ceda"
        },
        feature: {
            restore: {}
        }
    },
    tooltip: {
        trigger: "item",
        triggerOn: "mousemove",
        backgroundColor: "rgba(1,70,86,1)",
        borderColor: "rgba(0,246,255,1)",
        borderWidth: 0.5,
        textStyle: {
            fontSize: 10,
            color: '#fff'
        }
    },
    series: [
        {
            type: "tree",
            hoverAnimation: true,
            // data: props.treeData,
            data: getData(),
            layout: "radial",
            symbol: "circle",
            symbolSize: 10,
            itemStyle: {
                borderWidth: 1,
            },
            label: {
                color: "black",
                fontSize: 12,
                fontFamily: "SourceHanSansCN",
            },
            lineStyle: {
                width: 2,
                curveness: 0.5,
            }
        }
    ]
};
const chartContainer = ref(null);
onMounted(async () => {
    await nextTick();
    const myChart = echarts.init(chartContainer.value);
    myChart.setOption(option);
});

</script>

<style scoped lang="less">
#treeChart {
    width: 100%;
    height: 100%;
}
</style>