<template>
  <div class="content-for">
    <div style="display: flex;">
      <div
        class="list-name"
        :class="{ 'name-select': item.key == yearKey }"
        @click="handleSelectName(item)"
        v-for="(item, index) in yearList"
        :key="index"
      >
        {{ item.name }}
      </div>
    </div>
    <div v-if="isShow">
      <el-button @click="handleExport" class="back-btn" type="primary" plain round
        >导出数据
      </el-button>
    </div>
  </div>
</template>

<script setup >
import { httpPost } from "@wl-fe/http/dist";
const emit = defineEmits(["selectYear"]);
let props = defineProps({
  modelCode: {
    type: String,
    default: ''
  },
  keyword: {
    type: String,
    default: ''
  }
});
const isShow = computed(() => {
  const codeArray =[ '47','48','49','50','51','52' ]
  return codeArray.includes(props.modelCode)
})
const yearKey = ref('');
const yearList = [
  {
    name: "2024年",
    key: '2024',
  },
  {
    name: "2025年",
    key: '2025',
  },
];
function handleSelectName(item) {
  yearKey.value = item.key;
  emit('selectYear',item.key)
}
const handleExport = async () => {
  console.log('导出')
  const response = await httpPost(
    "/smartRegulationController/export",
    { modelCode: props.modelCode,keyword:props.keyword},
    { ignoreTransferResult: true ,responseType: 'blob'}
  );
  const blob = new Blob([response], {
    type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
  });
  const url = window.URL.createObjectURL(blob);
  const link = document.createElement("a");
  link.href = url;
  link.setAttribute("download", "统计表.xlsx");
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}
</script>

<style lang="less" scoped>
.content-for {
    height: 10%;
    width: 100%;
    background: aliceblue;
    border-radius: 8px;
    display: flex;
    align-items: center;
    padding: 5%;
    justify-content: space-between;
  .list-name {
    width: 113px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    background: #fff;
    border-radius: 20px;
    margin-right: 40px;
    font-size: 16px;
    cursor: pointer;
  }
  .name-select {
    font-weight: 600;
    color: #8080ff;
    background: linear-gradient(to right, #a9d7fa, #e2cafb);
  }
}
</style>