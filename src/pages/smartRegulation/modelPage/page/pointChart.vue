<template>
    <div class="pointChart">
        <div class="pointChart-content">
            <!--中间的显示的文字--->
            <div class="text-center">
                <div class="text-center-title">{{ chartInfo.bidSectionName }}</div>
            </div>
            <!--icon-->
            <div class="first-icon">
                <img src="../../../../assets/projectAi/1.png" />
            </div>
            <div class="second-icon">
                <img src="../../../../assets/projectAi/2.png" />
            </div>
            <div class="third-icon">
                <img src="../../../../assets/projectAi/3.png" />
            </div>
            <div class="four-icon">
                <img src="../../../../assets/projectAi/4.png">
            </div>
            <div class="five-icon">
                <img src="../../../../assets/projectAi/5.png" />
            </div>
            <div class="six-icon">
                <img src="../../../../assets/projectAi/6.png" />
            </div>
            <!--            <div class="chart-footer">-->
            <!--                <el-button @click="handleClose" class="btn-footer" type="primary" plain round>关 闭</el-button>-->
            <!--            </div>-->

            <!--icon对应的线上的文字-->
            <div class="first-line line-text">交易平台名称</div>
            <div class="second-line line-text">招标文件制作工具</div>
            <div class="third-line line-text">代理机构</div>
            <div class="four-line line-text">投标人名称</div>
            <div class="five-line line-text">造价软件</div>
            <div class="six-line line-text">投标文件制作工具</div>

            <!--投标人名称-->
            <div class="bidder-block">
                <div v-for="(item, index) in chartInfo.list" class="item-block" :key="index">{{ item.name }}</div>
            </div>
            <!--造价软件-->
            <div class="soft-block">
                <div v-for="(item, index) in chartInfo.list" class="item-block" :key="index">{{ item.costLockNumber }}</div>
            </div>
            <!--投标文件制作工具-->
            <div class="tool-block">
                <div v-for="(item, index) in chartInfo.list" class="item-block" :key="index">{{ item.productionTool }}</div>
            </div>

            <!--代理机构-->
            <div class="agency-block">
                <el-tooltip :content="chartInfo.tenderAgentCode" placement="top">
                    <div class="item-block">{{ chartInfo.tenderAgentName }}</div>
                </el-tooltip>
            </div>
            <!--交易平台名称-->
            <div class="platform-block">
                <el-tooltip :content="chartInfo.PLATFORM_CODE" placement="top">
                    <div class="item-block">{{ chartInfo.platformName }}</div>
                </el-tooltip>
            </div>
            <!--招标文件制作工具-->
            <div class="tender-tool-block">
<!--                <el-tooltip :content="chartInfo.PLATFORM_CODE" placement="top">-->
<!--                    <div class="item-block">{{ chartInfo.tenderToolName }}</div>-->
<!--                </el-tooltip>-->
                <div class="item-block">{{ chartInfo.tenderToolName }}</div>
            </div>
        </div>

    </div>
</template>

<script setup>
const emit = defineEmits(["closeDrawer"]);
const props = defineProps({
  chartInfo:{
    type: Object,
    default: {}
  }
})
const handleClose = () => {
  emit("closeDrawer");
}
</script>

<style scoped lang="less">
.pointChart {
  display: flex;
  align-items: center;
  justify-content: center;
  &-content {
    background: url("../../../../assets/projectAi/bg-chart.png") no-repeat center;
    background-size: contain;
    width: 1409px;
    height: 607px;
    position: relative;
    .first-icon {
      position: absolute;
      top: 190px;
      left: 336px;
    }
    .second-icon {
      position: absolute;
      top: 350px;
      left: 391px;
    }
    .third-icon {
      position: absolute;
      top: 470px;
      left: 550px;
    }
    .four-icon {
      position: absolute;
      top: 358px;
      right: 400px;
    }
    .five-icon {
      position: absolute;
      top: 190px;
      right: 342px;
    }
    .six-icon {
      position: absolute;
      top: 472px;
      right: 558px;
    }
    .line-text {
      color: #3366ff;
      font-size: 20px;
      font-weight: bolder;
    }
    .first-line {
      position: absolute;
      top: 116px;
      left: 146px;
    }
    .second-line {
      position: absolute;
      top: 344px;
      left: 170px;
    }
    .third-line {
      position: absolute;
      top: 524px;
      left: 322px;
    }
    .four-line {
      position: absolute;
      top: 116px;
      right: 142px;
    }
    .five-line {
      position: absolute;
      top: 344px;
      right: 198px;
    }
    .six-line {
      position: absolute;
      top: 524px;
      right: 274px;
    }
    .text-center {
      position: absolute;
      top: 162px;
      left: 600px;
      &-title {
        color: #FFFFFF;
        font-size: 28px;
        text-align: center;
        width: 200px;
      }
    }
    .item-block {
      width: 200px;
      height: 30px;
      padding-left: 4px;
      border-radius: 8px;
      margin-bottom: 5px;
      border: 1px solid #3366ff;
      color: #3366ff;
      text-align: left;
      font-size: 14px;
      box-sizing: border-box;
      font-weight: bolder;
      line-height: 30px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .bidder-block {
      position: absolute;
      top: 100px;
      right: -216px;
    }
    .soft-block {
      position: absolute;
      top: 340px;
      right: -160px;
    }
    .tool-block {
      position: absolute;
      top: 512px;
      right: -60px;
    }
    .agency-block {
      position: absolute;
      top: 548px;
      left: -50px;
    }
    .platform-block {
      position: absolute;
      top: 100px;
      left: -208px;
    }
    .tender-tool-block {
      position: absolute;
      top: 364px;
      left: -156px;
    }
  }
  .chart-footer {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100%;
    border-radius: 5px;
    display: flex;
    justify-content: center;
    padding: 10px 0;
  }
}
</style>