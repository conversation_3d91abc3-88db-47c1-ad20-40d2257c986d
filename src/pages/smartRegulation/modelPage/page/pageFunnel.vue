<template>
    <div class="first-type">
        <div class="icon-box">
            <img src="/projectAi/ai-j.png" alt="" />
        </div>
        <div class="content-box">
            <!-- v-html="parentProp.textData.text" -->
            <div class="content-her" v-html="parentProp.textData.text">

            </div>
            <div class="content-con">
                <div class="content-blank">
                    <div class="content-blank-box" v-for="(item,index) in parentProp.infoData" :key="index">
                        <div class="text-left">{{  item.name  }}</div>
                        <div class="text-right">
                            <span style="color: #FFCC33">{{ item.value }}</span>
                            <span style="margin-left: 2px;font-size: 16px">个</span>
                        </div>
                        <div class="text-detail" v-if="item.isDetail == 1" @click="openDetail(index)">点击查看详情</div>
                    </div>
                </div>
                <div class="content-chart">
                    <funnelEcharts  v-if="isShow" :funnelData="parentProp.pieData"></funnelEcharts>
                </div>
            </div>
            <yearSelect @selectYear="selectYear"></yearSelect>
        </div>
        <TableDetail ref="tableDetailRef" />
    </div>
</template>
<script setup >
import TableDetail from "@/pages/smartRegulation/modelPage/components/tableDetail.vue";

const props = defineProps({
  parentProp: {
    type: Object,
    default: {},
  },
  modelCode: {
    type: String,
    default: ''
  },
  keyword: {
    type: String,
    default: ''
  },
  queryTime: {
    type: String,
    default: ''
  }
});
import funnelEcharts  from '../components/funnelEcharts.vue'
import yearSelect from '../components/yearSelect.vue'
import {ref} from "vue";
const isShow = ref(true)
const tableDetailRef = ref(null)
const emit = defineEmits(["selectYear"]);
function  selectYear(year) {
  console.log(year)
    isShow.value = false
    setTimeout(() => {
        isShow.value = true
    }, 500)
  emit('selectYear',year)
}
const openDetail = (index = '') => {
  tableDetailRef.value.openDialog(true)
  tableDetailRef.value.getTableDetail(index, props.modelCode, props.keyword, props.queryTime)
}
</script>

<style scoped lang="less">
.first-type {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  .icon-box {
    width: 10%;
    height: auto;
  }
  img {
    width: 100%;
    height: auto;
  }
  .content-box {
    width: 75%;
    height: 75%;
    margin-left: 2%;
    .content-her {
      height: 15%;
      width: 100%;
      border-radius: 8px;
      box-sizing: border-box;
      padding: 15px;
      .s-text {
        line-height: 30px;
        .text-f {
          font-size: 20px;
          font-weight: bold;
        }
      }
    }
    .content-con {
      height: 66%;
      width: 100%;
      margin: 2% 0%;
      border-radius: 8px;
      display: flex;
      justify-content: space-between;
      background: aliceblue;
      .content-blank {
        width: 35%;
        height: 100%;
        border-radius: 8px;
        box-sizing: border-box;
         display: flex;
         flex-direction: column;
         justify-content: space-around;
        .content-blank-box {
          width: 86%;
          height: 18%;
          // border: 1px solid #ccc;
          background: linear-gradient(to right, #a5c4fc, #7f70fd);
          border-radius: 5px;
          box-sizing: border-box;
          padding: 10px 12px;
          margin: 0 auto;
          //margin-bottom: 58px;
          //margin-top: 20px;
          position: relative;
          .text-detail{
            position: absolute;
            bottom: -25px;
            right: 14px;
            color: blue;
            border-bottom: 1px solid blue;
            cursor: pointer;
          }
          .text-left {
            line-height: 26px;
            color: #fff;
          }
          .text-right {
            line-height: 26px;
            text-align: right;
            font-size: 24px;
            font-weight: bold;
            color: #fff;
          }
        }
      }
      .content-chart {
        width: 63%;
        height: 100%;
        border-radius: 8px;
        // border: #ccc 1px solid;
      }
    }
  }
}

</style>
<style >
.text-s-html {
    line-height: 30px;
    font-size: 18px;
    font-weight: 600;
    margin-top: 30px;
    font-family: SourceHanSansCN, SourceHanSansCN;
}
.text-f-html {
    font-size: 20px;
    font-weight: bold;
    color: #1f38c1;
}
</style>