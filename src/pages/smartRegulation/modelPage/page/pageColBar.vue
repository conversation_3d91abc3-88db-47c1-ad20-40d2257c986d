<template>
    <div class="first-type">
      <div class="icon-box">
        <img src="/projectAi/ai-j.png" alt="" />
      </div>
      <div class="content-box">
        <!-- v-html="parentProp.textData.text" -->
        <div class="content-her" v-html="parentProp.textData.text" />
<!--        <div class="content-con content-con-h" v-if="parentProp.infoData">-->
<!--          <div class="content-blank">-->
<!--            <div class="content-blank-box" v-for="(item,index) in parentProp.infoData" :key="index">-->
<!--              <div class="text-left">{{  item.name  }}</div>-->
<!--              <div class="text-right"><span class="v-text">{{item.value}}</span></div>-->
<!--              <div class="text-detail" v-if="item.isDetail == 1" @click="openDetail(index)">点击查看详情</div>-->
<!--            </div>-->
<!--          </div>-->
<!--        </div>-->
        <template v-if="modelCode === '10'">
            <div class="content-item-column" style="width: 100%">
                <div class="left">
                    <img src="../../../../assets/projectAi/card-left-icon.png" class="icon-img"/>
                </div>
                <div class="center" style="margin-right: 10px">
                    <div class="text-lg">标段名称:</div>
                    <div class="text-lm">{{ keyword }}</div>
                    <div class="center-border"></div>
                    <div style="display: flex;justify-content: space-between">
                        <div v-for="(item, index) in parentProp.infoData" :key="index">
                            <div class="text-lm">{{ item.name }}</div>
                            <div style="margin-bottom: 0">
                                <span style="color: #FFCC33;font-size: 18px;font-weight: 600">{{ item.value }} </span>
                                <span>家</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="right">
                    <div class="right-btn" @click="openDetail(index)">查看详情</div>
                </div>
            </div>
        </template>
          <template v-else>
              <div class="content-item-column" v-for="(item, index) in parentProp.infoData" :key="index">
                  <div class="left">
                      <img src="../../../../assets/projectAi/card-left-icon.png" class="icon-img"/>
                  </div>
                  <div class="center">
                      <div class="text-lg">标段名称:</div>
                      <div class="text-lm">{{ keyword }}</div>
                      <div class="center-border"></div>
                      <div class="text-lg">{{ item.name }}</div>
                      <div class="text-lm" style="margin-bottom: 0">{{ item.value }}</div>
                  </div>
                  <div class="right">
                      <div class="right-btn" @click="openDetail(index)">查看详情</div>
                  </div>
              </div>
          </template>
        <div class="content-con content-con-e">
          <div class="content-chart">
               <barColEcharts v-if="isShow"  :barData="parentProp.pieData" :text="modelCode === '5' ? '单位:万元':'单位:家'"></barColEcharts>
          </div>
        </div>
        <yearSelect @selectYear="selectYear" v-if="modelCode != '10'"></yearSelect>
      </div>
        <TableDetail ref="tableDetailRef" />
    </div>
  </template>
  <script setup>
  import TableDetail from "@/pages/smartRegulation/modelPage/components/tableDetail.vue";
  const isShow = ref(true)
  const props = defineProps({
    parentProp: {
      type: Object,
      default: {},
    },
    modelCode: {
      type: String,
      default: ''
    },
    keyword: {
      type: String,
      default: ''
    },
    queryTime: {
      type: String,
      default: ''
    }
  });
  import barColEcharts  from '../components/barColEcharts.vue'
  import yearSelect from '../components/yearSelect.vue'
  import {ref} from "vue";
  const emit = defineEmits(["selectYear"]);
  const tableDetailRef = ref(null)
  const openDetail = (index = '') => {
    tableDetailRef.value.openDialog(true)
    tableDetailRef.value.getTableDetail(index, props.modelCode, props.keyword, props.queryTime)
  }
function  selectYear(year) {
  console.log("1",year)
    isShow.value = false
    setTimeout(() => {
        isShow.value = true
    }, 500)
  emit('selectYear',year)
}
  </script>
  
  <style scoped lang="less">
  .first-type {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    .icon-box {
      width: 10%;
      height: auto;
    }
    img {
      width: 100%;
      height: auto;
    }
    .content-box {
      width: 75%;
      height: 100%;
      margin-left: 2%;
      .content-her {
        height: 15%;
        width: 100%;
        border-radius: 8px;
        box-sizing: border-box;
        padding: 15px;
        .s-text {
          line-height: 30px;
          .text-f {
            font-size: 20px;
            font-weight: bold;
          }
        }
      }
      .content-con {
        //height: 66%;
        width: 100%;
        margin: 2% 0%;
        border-radius: 8px;
        display: flex;
        justify-content: space-between;
        background: aliceblue;
      }
      .content-con-h{
        //height: 20%;
        .content-blank {
          width: 100%;
          height: 100%;
          border-radius: 8px;
          // border: #ccc 1px solid;
          box-sizing: border-box;
          padding: 10px 0px;
          // display: flex;
          // flex-direction: column;
          // align-items: center;
          // justify-content: space-around;
          .content-blank-box {
            width: 95%;
            height: 74%;
            // border: 1px solid #ccc;
            background: linear-gradient(to right, #a5c4fc, #7f70fd);
            border-radius: 5px;
            box-sizing: border-box;
            padding: 10px 18px;
            margin: 0 auto;
            margin-bottom: 30px;
            position: relative;
            .text-detail{
              position: absolute;
              bottom: -25px;
              right: 14px;
              color: blue;
              border-bottom: 1px solid blue;
              cursor: pointer;
            }
            .text-left {
              line-height: 26px;
              color: #fff;
            }
            .text-right {
              line-height: 26px;
              text-align: right;
              font-size: 24px;
              font-weight: bold;
              color: #fff;
              .v-text{
                color: #FFCC33;
                .unit{
                    font-size: 16px;
                    color: #fff;
                }
            }
            }
          }
        }
      }
      .content-con-e{
        height: 50%;
        background: aliceblue;
        .content-chart {
          width: 100%;
          height: 100%;
          border-radius: 8px;
          // border: #ccc 1px solid;
        }
      }
      .content-item-column {
        background: linear-gradient(to right, #7275FD, #A3B8FC);
        margin-bottom: 10px;
        border-radius: 8px;
        /*padding: 20px 8px;*/
        width: 668px;
        height: 153px;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .left {
          border-right: 1px dashed rgba(255,255,255, .7);
          margin-right: 10px;
          .icon-img {
            width: 88px;
            height: 87px;
          }

        }
        .center {
          flex: 1;
          .center-border {
            height: 1px;
            width: 98%;
            border: 1px dashed rgba(5, 11, 135, .4);
            margin-bottom: 8px;
          }
          .text-lg {
            font-family: SourceHanSansCN, SourceHanSansCN;
            font-weight: 600;
            font-size: 18px;
            color: #FFFFFF;
            text-align: left;
            font-style: normal;
            text-transform: none;
            margin-bottom: 8px;
          }
          .text-lm {
            font-family: SourceHanSansCN, SourceHanSansCN;
            font-weight: 400;
            font-size: 16px;
            color: #FFFFFF;
            line-height: 20px;
            text-align: left;
            font-style: normal;
            text-transform: none;
            margin-bottom: 8px;
          }
        }
        .right {
          .right-btn {
            background: url("../../../../assets/projectAi/card-left-btn.png");
            width: 89px;
            height: 46px;
            background-size: contain;
            color: #FFFFFF;
            font-size: 14px;
            line-height: 46px;
            font-weight: 600;
            text-align: center;
            cursor: pointer;
          }
        }
      }
    }
  }
  
  </style>
  <style >
  .text-s-html {
      line-height: 30px;
      font-size: 18px;
      font-weight: 600;
      margin-top: 30px;
      font-family: SourceHanSansCN, SourceHanSansCN;
  }
  .text-f-html {
      font-size: 20px;
      font-weight: bold;
      color: #1f38c1;
  }
  </style>