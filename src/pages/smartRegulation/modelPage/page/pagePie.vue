<template>
  <div class="first-type">
    <div class="icon-box">
      <img src="/projectAi/ai-j.png" alt="" />
    </div>
    <div class="content-box">
      <div class="content-her" v-html="parentProp.textData.text" />
      <div class="content-con">
        <div class="content-blank">
          <div class="content-blank-box" v-for="(item,index) in parentProp.infoData" :key="index">
            <template v-if="modelCode === '20'">
                <div class="text-left">

                    <template v-if="item.name === '中风险'">
                        <div class="flex-icon">
                            <img src="../../../../assets/projectManagement/orange.gif" class="power-icon"/>
                            <span>{{  item.name  }}</span>
                        </div>
                    </template>
                    <template v-if="item.name === '低风险'">
                        <div class="flex-icon">
                            <img src="../../../../assets/projectManagement/yellow.gif" class="power-icon"/>
                            <span>{{  item.name  }}</span>
                        </div>
                    </template>
                    <template v-if="item.name === '高风险'">
                        <div class="flex-icon">
                            <img src="../../../../assets/projectManagement/red.gif" class="power-icon"/>
                            <span>{{  item.name  }}</span>
                        </div>
                    </template>
                </div>
                <div class="text-right">
                    {{ item.value }}
                    <span class="white">个</span>
                </div>
            </template>
            <template v-else>
                <div class="text-left">{{  item.name  }}</div>
                <div class="text-right">
                    <span>{{ item.value }}</span>
                    <template v-if="modelCode === '1'">
                        <span v-if="item.name !=='中标率'" class="unit">个</span>
                        <span v-else class="unit">%</span>
                    </template>
                    <template v-else-if="modelCode === '3'">
                        <span class="unit">家</span>
                    </template>
                    <template v-else-if="modelCode === '9'">
                        <span class="unit">笔</span>
                    </template>
                    <template v-else>
                        <span class="unit">个</span>
                    </template>
                </div>
            </template>
            <template v-if="modelCode === '15' || modelCode === '16' || modelCode === '13' || modelCode === '14' || modelCode === '11' || modelCode === '12'">
                <div class="text-detail" v-if="item.isDetail == 1" @click="openTableDetail(item.code,'')">点击查看详情</div>
            </template>
            <template v-else>
                <div class="text-detail" v-if="item.isDetail == 1" @click="openTableDetail(index,item)">点击查看详情</div>
            </template>
          </div>
        </div>
        <div class="content-chart">
           <pieChart v-if="isShow" :pieData="parentProp.pieData" :colorArray="selectColor()" :modelCode="modelCode"></pieChart>
        </div>
      </div>
      <yearSelect @selectYear="selectYear" :modelCode="modelCode" :keyword="keyword"></yearSelect>
    </div>
      <TableDetail ref="tableDetailRef" />
  </div>
</template>
<script setup >
import { ref } from 'vue'
const props = defineProps({
  parentProp: {
    type: Object,
    default: () => ({}),
  },
  modelCode: {
    type: String,
    default: ''
  },
  keyword: {
    type: String,
    default: ''
  },
  queryTime: {
    type: String,
    default: ''
  }
});
const isShow = ref(true)
import pieChart from '../components/pieChart.vue'
import yearSelect from '../components/yearSelect.vue'
import TableDetail from "@/pages/smartRegulation/modelPage/components/tableDetail.vue";
const emit = defineEmits(["selectYear"]);
const tableDetailRef = ref(null)
function  selectYear(year) {
  console.log("2",year)
  // props.parentProp.pieData = {}
  isShow.value = false
  setTimeout(() => {
    isShow.value = true
  }, 500)
  // setTimeout(() => {
  //   isShow.value = false
  // }, 100)
  // isShow.value = true
  emit('selectYear',year)
}
if(props.modelCode == 53){
  // 计算总和
const sum = props.parentProp.infoData.reduce((total, item) => total + parseInt(item.value), 0);
props.parentProp.infoData = [
    { "name": "总计", "code": "TOTAL", "value": sum.toString(), "isDetail": "0" },
    ...props.parentProp.infoData
]
}
function selectColor(){
    console.log('走了')
    if(props.modelCode == 20 && props.parentProp.pieData){
        return props.parentProp.pieData.map(t=>{
            if(t.level == '1'){
                return '#fabc05'
            }else if(t.level == '2'){
                return 'rgb(240 144 0)'
            }else if(t.level == '3'){
                return '#e91d28'
            }
        })
    }
    return ['#fabc05','rgb(240 144 0)','#e91d28']
}
const openTableDetail = (index,item) => {
  tableDetailRef.value.openDialog(true)
  if(props.modelCode == 20){
      let level = 0
      if(item.name === '低风险'){
          level = 1
      }else if (item.name === '中风险'){
          level = 2
      }else {
          level = 3
      }

      tableDetailRef.value.getTableDetail(level, props.modelCode, props.keyword, props.queryTime)
  }else{
      tableDetailRef.value.getTableDetail(index, props.modelCode, props.keyword, props.queryTime)
  }
}
</script>

<style scoped lang="less">
.first-type {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  .icon-box {
    width: 10%;
    height: auto;
  }
  img {
    width: 100%;
    height: auto;
  }
  .content-box {
    width: 75%;
    height: 81%;
    margin-left: 2%;
    .content-her {
      height: 15%;
      width: 100%;
      border-radius: 8px;
      box-sizing: border-box;
      padding: 15px;
      .s-text {
        line-height: 30px;
        .text-f {
          font-size: 20px;
          font-weight: bold;
        }
      }
    }
    .content-con {
      height: 85%;
      width: 100%;
      margin: 2% 0%;
      border-radius: 8px;
      display: flex;
      justify-content: space-between;
      background: aliceblue;
      .content-blank {
        width: 35%;
        height: 100%;
        border-radius: 8px;
        box-sizing: border-box;
        padding: 10px 0px;
        overflow-y: auto;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        .content-blank-box {
          width: 86%;
          height: 18%;
          // border: 1px solid #ccc;
          background: linear-gradient(to right, #a5c4fc, #7f70fd);
          border-radius: 5px;
          box-sizing: border-box;
          padding: 10px 12px;
          margin: 0 auto;
          //margin-bottom: 58px;
          //margin-top: 20px;
          position: relative;
          margin-bottom: 27px;
          .text-detail{
            position: absolute;
            bottom: -25px;
            right: 14px;
            color: blue;
            border-bottom: 1px solid blue;
            cursor: pointer;
          }
          .text-right {
            line-height: 26px;
            text-align: right;
            font-size: 24px;
            font-weight: bold;
            color: #FFCC33;
            .unit {
              color: #FFFFFF;
              font-size: 16px;
              margin-left: 2px;
            }
            .white {
              color: #FFFFFF;
              font-size: 16px;
            }
          }
          .text-left {
            line-height: 26px;
            color: #fff;
            .power-icon {
              width: 25px;
              height: 25px;
            }
            .flex-icon {
              display: flex;
              align-items: center;
            }
          }
        }
      }
      .content-chart {
        width: 63%;
        height: 100%;
        border-radius: 8px;
        // border: #ccc 1px solid;
      }
    }
  }
}

</style>
<style >
.text-s-html {
    line-height: 30px;
    font-size: 18px;
    font-weight: 600;
    margin-top: 30px;
    font-family: SourceHanSansCN, SourceHanSansCN;
}
.text-f-html {
  font-size: 20px;
  font-weight: bold;
  color: #1f38c1;
}
</style>
