<template>
  <div class="first-type">
    <div class="icon-box">
      <img src="/projectAi/ai-j.png" alt="" />
    </div>
    <div class="content-box">
      <!-- v-html="parentProp.textData.text" -->
      <div class="content-her" v-html="parentProp.textData.text">

      </div>
      <div class="content-con">
        <div class="content-blank">
              <div class="content-blank-box" v-for="(val,index) in parentProp.info.allPrice.value" :key="index">
                  <div class="text-left">{{ val.name }}</div>
                  <div class="text-right"><span class="v-text">{{val.value}} </span><span style="font-size: 14px;margin-left: 4px;font-weight: 400">万元</span></div>
                  <div class="text-detail" v-if="val.isDetail == 1" @click="openDetail()">点击查看详情</div>
              </div>
              <div class="content-blank-list" v-for="(val,index) in parentProp.info.addressPrice.value" :key="index">
                  <div class="city-name">
                      <span class="n-text">{{ val.name }}</span>
                      <span class="v-text">{{ val.value }} <span class="unit">万元</span> </span>
                  </div>
                  <div class="text-detail-city" @click="openDetail(val.code)">点击查看详情</div>
              </div>

          </div>
        <div class="content-chart">
            <customPie v-if="isShow" :pieData="parentProp.infopage.addressPrice"/>
        </div>
      </div>
      <yearSelect @selectYear="selectYear"></yearSelect>
    </div>
      <TableDetail ref="tableDetailRef" />
  </div>
</template>
<script setup >
import { ref } from 'vue'
const isShow = ref(true)
const props = defineProps({
  parentProp: {
    type: Object,
    default: {},
  },
  modelCode: {
    type: String,
    default: ''
  },
  keyword: {
    type: String,
    default: ''
  },
  queryTime: {
    type: String,
    default: ''
  }
});
import customPie  from '../components/customPie.vue'
import yearSelect from '../components/yearSelect.vue'
import TableDetail from "@/pages/smartRegulation/modelPage/components/tableDetail.vue";
const emit = defineEmits(["selectYear"]);
const tableDetailRef = ref(null)
function  selectYear(year) {
  console.log("4",year)
    isShow.value = false
    setTimeout(() => {
        isShow.value = true
    }, 500)
  emit('selectYear',year)
}
const openTableDetail = (index) => {
  tableDetailRef.value.openDialog(true)
  tableDetailRef.value.getTableDetail(index, props.modelCode, props.keyword, props.queryTime)
}
const openDetail = (index = '') => {
  tableDetailRef.value.openDialog(true)
  tableDetailRef.value.getTableDetail(index, props.modelCode, props.keyword, props.queryTime)
}
</script>

<style scoped lang="less">
.first-type {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  .icon-box {
    width: 10%;
    height: auto;
  }
  img {
    width: 100%;
    height: auto;
  }
  .content-box {
    width: 75%;
    height: 75%;
    margin-left: 2%;
    .content-her {
      height: 15%;
      width: 100%;
      border-radius: 8px;
      box-sizing: border-box;
      padding: 15px;
      .s-text {
        line-height: 30px;
        .text-f {
          font-size: 20px;
          font-weight: bold;
        }
      }
    }
    .content-con {
      height: 66%;
      width: 100%;
      margin: 2% 0%;
      border-radius: 8px;
      display: flex;
      justify-content: space-between;
      background: aliceblue;
      overflow-y: auto;
      .content-blank {
        width: 35%;
        height: 100%;
        border-radius: 8px;
        // border: #ccc 1px solid;
        box-sizing: border-box;
        padding: 10px 0px;
        .content-blank-box {
          width: 86%;
          height: 18%;
          background: linear-gradient(to right, #a5c4fc, #7f70fd);
          border-radius: 5px;
          box-sizing: border-box;
          padding: 10px 18px;
          margin: 0 auto;
          margin-bottom: 30px;
          margin-top: 20px;
          position: relative;
          .text-detail {
            position: absolute;
            bottom: -25px;
            right: 14px;
            color: blue;
            border-bottom: 1px solid blue;
            cursor: pointer;
          }
          .text-left {
            line-height: 26px;
            color: #fff;
            font-size: 16px;
          }
          .text-right {
            line-height: 26px;
            text-align: right;
            font-size: 24px;
            font-weight: bold;
            color: #fff;
            .v-text{
              color: #FFCC33;
              .unit{
                font-size: 16px;
                color: #fff;
              }
            }
          }
        }
        .content-blank-list {
          width: 86%;
          //height: 62%;
          border-radius: 5px;
          box-sizing: border-box;
          margin: 0px auto;
          margin-top: 80px;
          position: relative;
          overflow: auto;
          .city-name {
            width: 100%;
            background: linear-gradient(to right, #a5c4fc, #7f70fd);
            border-radius: 25px;
            //margin-bottom: 10px;
            height: 35px;
            line-height: 35px;
            padding: 0px 16px;
            display: flex;
            justify-content: space-between;
            color: #fff;
            .v-text{
              font-size: 20px;
              color: #FFCC33;
              .unit{
                font-size: 14px;
                color: #fff;
              }
            }
          }
        }
        .text-detail-city {
          float: right;
          color: blue;
          border-bottom: 1px solid blue;
          cursor: pointer;
          margin: 8px 10px 0px 0px;
        }
      }
      .content-chart {
        width: 63%;
        height: 100%;
        border-radius: 8px;
        // border: #ccc 1px solid;
      }
    }
  }
}

</style>
<style >
.text-s-html {
    line-height: 30px;
    font-size: 18px;
    font-weight: 600;
    margin-top: 30px;
    font-family: SourceHanSansCN, SourceHanSansCN;
}
.text-f-html {
    font-size: 20px;
    font-weight: bold;
    color: #1f38c1;
}
</style>