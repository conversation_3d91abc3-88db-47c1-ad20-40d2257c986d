<template>
    <div class="pageList">
        <allDialogView
            ref="allDialogViewEl"
            :dialogTitle="'风险预警'"
            :dialogWidth="'95%'"
        >
            <template #content>
                <statisticaList
                    v-if="allDialogViewEl.showDialog"
                    @handleCloseDialog="handleMore"
                ></statisticaList>
            </template>
        </allDialogView>
        <div class="first-type">
            <div class="icon-box">
                <img src="/projectAi/ai-j.png" alt=""/>
            </div>
            <div class="content-box">
                <template v-if="parentProp.textData">
                    <div class="content-her" v-html="parentProp.textData.text"></div>
                </template>

                <div class="content-container">
                    <template v-if="modelCode === '6' || modelCode === '7' || modelCode === '8'">
                        <div class="content-item-column" v-for="(item, index) in parentProp.list" :key="index"
                             style="height: 141px;">
                            <div class="left">
                                <img src="../../../../assets/projectAi/card-left-icon.png" class="icon-img"/>
                            </div>
                            <div class="center">
                                <div class="text-lg">招标项目名称:</div>
                                <div class="text-lm">
                                    <template v-if="item.tenderProjectName.length > 30">
                                        <el-tooltip :content="item.tenderProjectName" placement="top">
                                            <span>{{ item.tenderProjectName.substring(0, 24) }}...</span>
                                        </el-tooltip>
                                    </template>
                                    <template v-else>
                                        <span>{{ item.tenderProjectName }}</span>
                                    </template>
                                    <!--                                    {{ item.tenderProjectName }}-->
                                </div>
                                <div class="center-border"></div>
                                <!--                                <template v-if="modelCode === '6'">-->
                                <!--                                    <div style="display: flex; justify-content: space-between">-->
                                <!--                                        <div>编号：{{ item.tenderProjectCode }}</div>-->
                                <!--                                        <div>当前阶段: XXXXXX</div>-->
                                <!--                                    </div>-->
                                <!--                                </template>-->
                                <template v-if="modelCode === '7' || modelCode === '6'">
                                    <div class="text-lg">招标项目编号:</div>
                                    <div class="text-lm" style="margin-bottom: 0">{{ item.tenderProjectCode }}</div>
                                </template>
                                <template v-else>
                                    <div class="text-lg">招标项目编号:</div>
                                    <div class="text-lm" style="margin-bottom: 0">{{ item.tenderProjectCode }}</div>
                                </template>

                            </div>
                            <div class="right">
                                <div class="right-btn" @click.stop="openUrlDetail(item.url)">查看详情</div>
                            </div>
                        </div>
                    </template>
                    <template v-else-if="modelCode === '19'">
                        <div class="content-item-column" v-for="(item, index) in parentProp.list" :key="index">
                            <div class="left">
                                <div class="left-line">标段名称：{{ item.bidSectionName }}</div>
                                <div class="left-line">标段编号:{{ item.bidSectionCode }}</div>
                            </div>
                            <div class="right">
                                <div class="" @click.stop="openUrlDetail(index)">查看详情</div>
                            </div>
                        </div>
                    </template>
                    <template v-else-if="modelCode === '21' || modelCode === '22'">
                        <div class="content-item-column" v-for="(item, index) in parentProp.list" :key="index">
                            <div class="left">
                                <img src="../../../../assets/projectAi/card-left-icon.png" class="icon-img"/>
                            </div>
                            <div class="center" style="width: 454px">
                                <div class="text-lg">标段名称:</div>
                                <div class="text-lm">
                                    <template v-if="item.bidSectionName.length > 30">
                                        <el-tooltip :content="item.bidSectionName" placement="top">
                                            <span>{{ item.bidSectionName.substring(0, 24) }}...</span>
                                        </el-tooltip>
                                    </template>
                                    <template v-else>
                                        <span>{{ item.bidSectionName }}</span>
                                    </template>
                                </div>
                                <div class="center-border"></div>
                                <div class="text-lg">标段编号:</div>
                                <div class="text-lm" style="margin-bottom: 0">{{ item.bidSectionCode }}</div>
                            </div>
                            <div class="right">
                                <div class="right-btn" @click.stop="openTableDetail(item.rowGuid)">查看详情</div>
                            </div>
                        </div>
                    </template>
                    <template v-else-if="modelCode === '25'">
                        <template v-if="parentProp.list.length === 0">
                            <el-empty :description="parentProp.msg"/>
                        </template>
                        <template v-else>
                            <div class="content-item-column" v-for="(item, index) in parentProp.list" :key="index"
                                 style="height: auto;padding: 10px 0">
                                <div class="left">
                                    <img src="../../../../assets/projectAi/card-left-icon.png" class="icon-img"/>
                                </div>
                                <div class="center">
                                    <div class="text-lg">投标人名称:</div>
                                    <div class="text-lm">{{ item.tbrname }}</div>
                                    <div class="center-border"></div>
                                    <div class="text-lg">表述非必要性雷同内容:</div>
                                    <div class="text-lm" style="margin-bottom: 5px"
                                         v-for="(m, index) in item.msg.split(',')">
                                        <span>内容{{ index + 1 }}：</span>
                                        <span>{{ m }}</span>
                                    </div>
                                </div>
                                <div class="right">
                                    <div class="right-btn" @click.stop="openDetail(item)">查看详情</div>
                                </div>
                            </div>
                        </template>
                    </template>
                    <template v-else-if=" modelCode === '26'">
                        <template v-if="parentProp.list.length === 0">
                            <el-empty :description="parentProp.msg"/>
                        </template>
                        <template v-else>
                            <div class="content-item-column" v-for="(item, index) in parentProp.list" :key="index">
                                <div class="left">
                                    <img src="../../../../assets/projectAi/card-left-icon.png" class="icon-img"/>
                                </div>
                                <div class="center">
                                    <div class="text-lg">标段名称:</div>
                                    <div class="text-lm">{{ item.bidname }}</div>
                                    <div class="center-border"></div>
                                    <div class="text-lg">表述非必要性雷同内容:</div>
                                    <div class="text-lm" style="margin-top: 5px"
                                         v-for="(m, index) in item.msg.split(',')">
                                        <span>内容{{ index + 1 }}：</span>
                                        <span>{{ m }}</span>
                                    </div>
                                </div>
                                <div class="right">
                                    <div class="right-btn" @click.stop="openDetail(item)">查看详情</div>
                                </div>
                            </div>
                        </template>
                    </template>
                    <template v-else-if="modelCode === '27' || modelCode === '28'">
                        <template v-if="parentProp.list.length === 0">
                            <el-empty :description="parentProp.msg"/>
                        </template>
                        <template v-else>
                            <div class="content-item-column" v-for="(item, index) in parentProp.list" :key="index"
                                 style="height: 200px">
                                <div class="left">
                                    <img src="../../../../assets/projectAi/card-left-icon.png" class="icon-img"/>
                                </div>
                                <div class="center">
                                    <div class="text-lg">标段名称:</div>
                                    <div class="text-lm">{{ item.bidSectionName }}</div>
                                    <div class="center-border"></div>
                                    <div class="text-lg">标段编号:</div>
                                    <div class="text-lm">{{ item.bidSectionCode }}</div>
                                    <div class="center-border"></div>
                                    <div class="text-lm" style="margin-bottom: 0">{{ item.content }}</div>
                                </div>
                                <div class="right">
                                    <div class="right-btn" @click.stop="openTableDetail(item.rowGuid)">查看详情</div>
                                </div>
                            </div>
                        </template>
                    </template>
                    <template v-else-if="modelCode === '29'">
                        <template v-if="parentProp.list.length === 0">
                            <el-empty :description="parentProp.msg"/>
                        </template>
                        <template v-else>
                            <div class="content-item-column" v-for="(item, index) in parentProp.list" :key="index"
                                 style="height: auto;padding: 10px 0">
                                <div class="left">
                                    <img src="../../../../assets/projectAi/card-left-icon.png" class="icon-img"/>
                                </div>
                                <div class="center">
                                    <div class="text-lg">标段名称: <span class="text-lm">{{
                                        item.bidSectionName
                                        }}</span></div>
                                    <div class="text-lg">招标人: <span class="text-lm">{{ item.tendererName }}</span>
                                    </div>
                                    <div class="text-lg">中标人: <span class="text-lm">{{ item.winBidderName }}</span>
                                    </div>
                                    <div class="text-lg">中标通知书备案时间: <span class="text-lm">{{
                                        item.approvalTime
                                        }}</span></div>
                                    <div class="text-lg">履约合同系统在线签订时间: <span
                                            class="text-lm">{{ item.onlinePerformanceTime }}</span></div>
                                    <div class="text-lg" style="margin-bottom: 0">
                                        <template v-if="item.diffTime === 0">
                                            <span class="yellow-font">已在法定时限内完成履约合同签订及备案</span>
                                        </template>
                                        <template v-else>
                                            <span class="yellow-font">超出法定时限<span
                                                    class="red-font">{{ item.diffTime }}</span>天</span>
                                        </template>
                                    </div>
                                </div>
                                <!--                                <div class="center">-->
                                <!--                                    <div class="text-lg">标段名称:</div>-->
                                <!--                                    <div class="text-lm">{{ item.bidSectionName }}</div>-->
                                <!--                                    <div class="text-lg">招标人:</div>-->
                                <!--                                    <div class="text-lm">{{ item.tendererName }}</div>-->
                                <!--                                    <div class="text-lg">中标人:</div>-->
                                <!--                                    <div class="text-lm">{{ item.winBidderName }}</div>-->
                                <!--                                    <div class="text-lg">中标通知书备案时间:</div>-->
                                <!--                                    <div class="text-lm">{{ item.approvalTime }}</div>-->
                                <!--                                    <div class="text-lg">履约合同系统在线签订时间:</div>-->
                                <!--                                    <div class="text-lm">{{ item.onlinePerformanceTime }}</div>-->
                                <!--                                    <div class="text-lm" style="margin-bottom: 0">超出法定时限{{ item.diffTime }}天</div>-->
                                <!--                                </div>-->
                            </div>
                        </template>
                    </template>
                    <template v-else-if="modelCode === '23' || modelCode === '24'">
                        <template v-if="parentProp.list.length === 0">
                            <el-empty :description="parentProp.msg"></el-empty>
                        </template>
                        <template v-else>
                            <div class="content-item-column" v-for="(item, index) in parentProp.list" :key="index"
                                 style="height: 92px">
                                <div class="left">
                                    <img src="../../../../assets/projectAi/card-left-icon.png" class="icon-img"/>
                                </div>
                                <el-tooltip :content="item.content" placement="top">
                                    <div>{{ item.content.substring(0, 80) }}...</div>
                                </el-tooltip>
                                <div class="right" style="margin-left: 10px">
                                    <div class="right-btn" @click.stop="openTableDetail(index,item)">查看详情</div>
                                </div>
                            </div>
                        </template>
                    </template>
                    <template v-else>
                        <div class="content-item-column" v-for="(item, index) in parentProp.list" :key="index">
                            <div class="left">
                                <img src="../../../../assets/projectAi/card-left-icon.png" class="icon-img"/>
                            </div>
                            <div class="center">
                                <div>{{ item.content }}</div>
                            </div>
                            <div class="right">
                                <div class="right-btn" @click.stop="openTableDetail(item)">查看详情</div>
                            </div>
                        </div>
                    </template>
                </div>
            </div>
        </div>
        <div class="second-type" v-if="modelCode === '23' || modelCode === '24'">
            <template v-if="modelCode === '23' || modelCode === '24'">
                <template v-if="parentProp.list.length !== 0">
                    <div class="content-chart">
                        <pieChart :pieData="parentProp.pieData"></pieChart>
                    </div>
                </template>
            </template>
            <template v-else>
                <div class="content-chart">
                    <pieChart :pieData="parentProp.pieData"></pieChart>
                </div>
            </template>

        </div>
        <cardDetail ref="cardDetailRef"/>
        <TableDetail ref="tableDetailRef"/>
    </div>
</template>

<script setup>
import CardDetail from "@/pages/smartRegulation/modelPage/components/cardDetail.vue";
import TableDetail from "@/pages/smartRegulation/modelPage/components/tableDetail.vue";
import pieChart from '../components/pieChart.vue'
import {ref} from 'vue'

const props = defineProps({
    parentProp: {
        type: Object,
        default: {},
    },
    modelCode: {
        type: String,
        default: ''
    },
    keyword: {
        type: String,
        default: ''
    },
});
const allDialogViewEl = ref();
const cardDetailRef = ref(null)
const tableDetailRef = ref(null)
const openDetail = (obj) => {
    cardDetailRef.value.openDialog(true)
    if (props.modelCode === '25') {
        console.log(obj)
        cardDetailRef.value.getDetail(obj, props.modelCode, obj.tbrname)
    } else if (props.modelCode === '26') {
        cardDetailRef.value.getDetail(obj, props.modelCode, props.keyword)
    }
}
const openUrlDetail = (url) => {
    // if (window.location.href.indexOf('localhost') !== -1) {
    //   window.open(url, '_blank')
    // } else {
    //   window.open(`/mg-admin${url}`, '_blank')
    // }
    window.open(url, '_blank')
}
const handleMore = () => {
    allDialogViewEl.value.showDialog = !allDialogViewEl.value.showDialog;
};
const openTableDetail = (index, item) => {
    tableDetailRef.value.openDialog(true)
    if(props.modelCode == '27' ||  props.modelCode == '28' || props.modelCode == '30' || props.modelCode == '31'){
        tableDetailRef.value.getTableDetail(index, props.modelCode, "", "",  "")
    }
    tableDetailRef.value.getTableDetail(index, props.modelCode, "", "", item.ids)

}
</script>

<style scoped>
.first-type {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;

    .icon-box {
        width: 10%;
        height: auto;
    }

    img {
        width: 100%;
        height: auto;
    }

    .content-box {
        width: 75%;
        height: 75%;
        margin-left: 2%;

        .content-her {
            height: 15%;
            width: 100%;
            border-radius: 8px;
            box-sizing: border-box;
            padding: 15px;

            .s-text {
                line-height: 30px;

                .text-f {
                    font-size: 20px;
                    font-weight: bold;
                }
            }
        }

        .content-container {
            margin-top: 16px;
            width: 100%;

            .content-item {
                background: linear-gradient(to right, #a5c4fc, #7f70fd);
                margin-bottom: 10px;
                border-radius: 8px;
                padding: 20px 8px;
                color: #fff;
                display: flex;
                align-items: center;
                justify-content: space-between;

                .same-text {

                }

                .to-detail {
                    cursor: pointer;
                }
            }

            .content-item-column {
                background: linear-gradient(to right, #7275FD, #A3B8FC);
                margin-bottom: 10px;
                border-radius: 8px;
                /*padding: 20px 8px;*/
                width: 668px;
                height: auto;
                color: #fff;
                display: flex;
                align-items: center;
                justify-content: space-between;

                .left {
                    border-right: 1px dashed rgba(255, 255, 255, .7);
                    margin-right: 10px;

                    .icon-img {
                        width: 88px;
                        height: 87px;
                    }

                }

                .center {
                    /*display: -webkit-box;*/
                    /*-webkit-box-orient: vertical;*/
                    /*-webkit-line-clamp: 3;*/
                    /*overflow: hidden;*/
                    /*text-overflow: ellipsis;*/
                    flex: 1;

                    .center-border {
                        height: 1px;
                        width: 98%;
                        border: 1px dashed rgba(5, 11, 135, .4);
                        margin-bottom: 8px;
                    }

                    .text-lg {
                        font-family: SourceHanSansCN, SourceHanSansCN;
                        font-weight: 600;
                        font-size: 18px;
                        color: #FFFFFF;
                        text-align: left;
                        font-style: normal;
                        text-transform: none;
                        margin-bottom: 8px;
                    }

                    .text-lm {
                        font-family: SourceHanSansCN, SourceHanSansCN;
                        font-weight: 400;
                        font-size: 16px;
                        color: #FFFFFF;
                        line-height: 20px;
                        text-align: left;
                        font-style: normal;
                        text-transform: none;
                        margin-bottom: 8px;
                    }

                    .red-font {
                        color: #f52b2b;
                        font-size: 20px;
                        margin: 0 10px;
                    }

                    .yellow-font {
                        color: #FFCC33;
                        font-size: 16px;
                    }
                }

                .right {
                    .right-btn {
                        background: url("../../../../assets/projectAi/card-left-btn.png") no-repeat center;
                        width: 89px;
                        height: 46px;
                        background-size: contain;
                        color: #FFFFFF;
                        font-size: 14px;
                        line-height: 46px;
                        font-weight: 600;
                        text-align: center;
                        cursor: pointer;
                    }
                }
            }

        }
    }
}

.second-type {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;

    .content-chart {
        width: 63%;
        height: 400px;
    }
}
</style>
<style>
.text-s-html {
    line-height: 30px;
    font-size: 18px;
    font-weight: 600;
    margin-top: 30px;
    font-family: SourceHanSansCN, SourceHanSansCN;
}

.text-f-html {
    font-size: 20px;
    font-weight: bold;
    color: #1f38c1;
}
</style>
