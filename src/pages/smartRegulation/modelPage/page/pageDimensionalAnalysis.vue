<template>
  <div id="pageDimensional">
    <div class="header">
      <div class="iconBox">
        <img src="/projectAi/ai-j.png" alt="" />
      </div>
      <div class="hCon">
        <span class="conName">投标情况聚合维度分析 </span>
        <span class="proName">我是标段名称</span>
      </div>
    </div>
    <div class="content">
      <div class="contBox">
        <div class="rowBox">
          <div class="rowBoxName unify-title unify-title7">
            潜在投标人竞争强度情况
          </div>
          <div class="rowPage">
            <compete></compete>
          </div>
        </div>
        <div class="rowBox">
          <div class="rowBoxName unify-title unify-title7">
            招标文件（含资格预审文件）领取时间分布
          </div>
          <div class="rowPage">
            <horizontalBarChart :infoData="timeData"></horizontalBarChart>
          </div>
        </div>
        <div class="rowBoxCen">
          <div class="rowBoxCenName unify-title unify-title7">
            潜在投标人综合分析
          </div>
          <div class="rowBoxCenPage">
            <div class="pageBox">
              <dimensionalPieChart :pieInfo="pieInfo"></dimensionalPieChart>
            </div>
            <div class="pageBox">
              <dimensionalPieChart :pieInfo="pieInfo1"></dimensionalPieChart>
            </div>
            <div class="pageBox">
              <dimensionalPieChart :pieInfo="pieInfo2"></dimensionalPieChart>
            </div>
            <div class="pageBox">
              <dimensionalPieChart :pieInfo="pieInfo3"></dimensionalPieChart>
            </div>
          </div>
        </div>
        <div class="rowBox">
          <div class="rowBoxName unify-title unify-title7">
            潜在投标人资质等级比例分布示图
          </div>
          <div class="rowPage">
            <horizontalBarChart :infoData="levelData"></horizontalBarChart>
          </div>
        </div>
        <div class="rowBox">
          <div class="rowBoxName unify-title unify-title7">
            潜在投标人资质等级比例分布示图
          </div>
          <div class="rowPage">
            <horizontalBarChart :infoData="levelEcData"></horizontalBarChart>
          </div>
        </div>
        <div class="rowBoxCen rowBoxCenSearch">
          <div class="rowBoxCenName rowBoxCenNameSec unify-title unify-title7">
            投标情况聚焦维度分析
          </div>
          <div class="searchBox">
            <div class="formBox">
              <el-form ref="ruleFormRef" :label-position="'top'" :model="formData" :rules="rules">
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="单位名称" prop="companyName" style="margin-right:20px;">
                      <el-input
                        v-model="formData.companyName"
                        placeholder="请输入单位名称！"
                        autocomplete="off"
                        clearable
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="统一社会信用代码" prop="lcode">
                      <el-input
                        v-model="formData.lcode"
                        placeholder="请输入单位统一社会信用代码！"
                        autocomplete="off"
                        clearable
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
              <div style="text-align:center;margin:10px 0;">
                <el-button style="width:150px;" round  @click="handleClose()">关 闭</el-button>
                <el-button style="width:150px;" round type="primary" @click="handleSearch(ruleFormRef)">搜 索</el-button>
              </div>
            </div>
            <div class="listConotent" v-if="tableData.length">
              <el-table
                :data="tableData"
                style="width: 100%"
                :height="350"
                :row-class-name="tableRowClassName"
                class="my-table"
              >
                <el-table-column
                  label="序号"
                  type="index"
                  :index="indexMethod"
                  align="center"
                  width="80px"
                />
                <el-table-column
                  v-for="item in tableColData"
                  :key="item.prop"
                  align="center"
                  :prop="item.prop"
                  :label="item.label"
                  :min-width="item.width"
                >
                  <template #default="{ row }">
                    <span>{{ row[item.prop] ? row[item.prop] : "--" }}</span>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <el-dialog v-model="centerDialogVisible" :show-close="false" width="800" center>
    <template #header="{ close, titleId, titleClass }">
      <div class="my-header">
        <h4 :id="titleId" :class="titleClass">查询结果</h4>
      </div>
    </template>
    <el-descriptions
    class="margin-top"
    :column="1"
    :size="size"
    border
  >
    <el-descriptions-item >
      <template #label>
        <div class="cell-item">
          <el-icon :style="iconStyle">
            <OfficeBuilding />
          </el-icon>
          投标人名称
        </div>
      </template>
      {{ formData.companyName  }}
    </el-descriptions-item>
    <el-descriptions-item >
      <template #label>
        <div class="cell-item">
          <el-icon :style="iconStyle">
            <Postcard />
          </el-icon>
          统一社会信用代码
        </div>
      </template>
    <div style="width:300px;"> {{ formData.lcode  }}</div>
    </el-descriptions-item>
  </el-descriptions>
    <div class="desCon">
      该单位未参与本项目(标段)
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button style="width:100px;" type="primary" @click="centerDialogVisible = false">
          关闭
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { httpPost } from "@wl-fe/http/dist";
import compete from "../components/compete.vue";
import horizontalBarChart from "../components/horizontalBarChart.vue";
import dimensionalPieChart from "../components/dimensionalPieChart.vue";
const timeData = {
  name: ["第7天", "第5-6天", "第3-4天", "第1-2天"],
  value: [60, 30, 10, 0],
};
const levelData = {
  name: ["特级", "一级", "二级"],
  value: [5, 33, 60],
};
const levelEcData = {
  name: ["甲级", "乙级", "丙级"],
  value: [10, 30, 150],
};
const pieInfo = {
  pieData: [
    {
      value: 45,
      name: "省内潜在投标人",
    },
    {
      value: 10,
      name: "省外潜在投标人",
    },
  ],
  infoData: [
    {
      value: 45,
      name: "省内潜在投标人",
    },
    {
      value: 10,
      name: "省外潜在投标人",
    },
  ],
};
const pieInfo1 = {
  pieData: [
    {
      value: 45,
      name: "沈阳市潜在投标人",
    },
    {
      value: 10,
      name: "大连市潜在投标人",
    },
    {
      value: 168,
      name: "南京市潜在投标人",
    },
    {
      value: 5,
      name: "北京市潜在投标人",
    },
  ],
  infoData: [
    {
      name: "沈阳市潜在投标人",
      value: "45",
    },
    {
      name: "大连市潜在投标人",
      value: "10",
    },
    {
      name: "南京市潜在投标人",
      value: "168",
    },
    {
      name: "北京市潜在投标人",
      value: "5",
    },
  ],
};
const pieInfo2 = {
  pieData: [
    {
      value: 20,
      name: "中央国企潜在投标人",
    },
    {
      value: 20,
      name: "地方国企潜在投标人",
    },
    {
      value: 20,
      name: "事业单位潜在投标人",
    },
    {
      value: 40,
      name: "民营企业潜在投标人",
    },
  ],
  infoData: [
    {
      name: "中央国企潜在投标人",
      value: "20",
    },
    {
      name: "地方国企潜在投标人",
      value: "20",
    },
    {
      name: "事业单位潜在投标人",
      value: "20",
    },
    {
      name: "民营企业潜在投标人",
      value: "40",
    },
  ],
};
const pieInfo3 = {
  pieData: [
    {
      value: 25,
      name: "潜在投标人联合招标",
    },
    {
      value: 75,
      name: "潜在投标人地独立投标",
    },
  ],
  infoData: [
    {
      value: 25,
      name: "潜在投标人联合招标",
    },
    {
      value: 75,
      name: "潜在投标人地独立投标",
    },
  ],
};
const tableColData = [
  {
    label: "查询时间",
    prop: "time",
    width: "200",
  },
  {
    label: "查询单位",
    prop: "name",
    width: "150",
  },
  {
    label: "查询单位统一社会信用代码",
    prop: "code",
    width: "200",
  },
];
const iconStyle = {
  color: "#409eff",
  fontSize: "16px",
  marginRight: "10px",
};
const centerDialogVisible = ref(false);
const ruleFormRef = ref();
const formData = ref({
  companyName: "",
  lcode: "",
});
const rules = reactive({
  companyName: [
    { required: true, message: "请输入单位名称！", trigger: "blur" },
  ],
  lcode: [
    {
      required: true,
      message: "请输入单位统一社会信用代码！",
      trigger: "blur",
    },
  ],
});
const tableRowClassName = ({ rowIndex }) => {
  if (rowIndex % 2 !== 0) {
    return "warning-row";
  }
  return "";
};
const tableData = ref([]);
const handleSearch = async (formEL) => {
  if (!formEL) return;
  await formEL.validate((valid, fields) => {
    if (valid) {
      let info = {
        companyName: formData.value.companyName,
        lcode: formData.value.lcode,
      };
      handleVerification(info);
    }
  });
};
const handleVerification = async (info) => {
  centerDialogVisible.value = true;
  await httpPost("/processNodeApproval/saveProcessNodeApproval", info);
};
const emit = defineEmits(["closeDimensional"]);
const handleClose = () => {
  emit('closeDimensional')
}
</script>

<style lang="scss" scoped>
#pageDimensional {
  width: 100%;
  height: 100%;
  .header {
    width: 100%;
    height: 10%;
    display: flex;
    .iconBox {
      width: 6%;
      height: 100%;
      img {
        height: 100%;
      }
    }
    .hCon {
      width: 90%;
      height: 100%;
      display: flex;
      align-items: center;
      .conName {
        font-size: 20px;
        font-weight: bold;
        margin-right: 30px;
      }
      .proName {
        font-size: 16px;
        color: #1367e4;
      }
    }
  }
  .content {
    width: 100%;
    height: 90%;

    margin-top: 10px;
    background: #eff8ff;
    border-radius: 10px;

    padding: 10px;
    box-sizing: border-box;
    .contBox {
      width: 100%;
      height: 100%;
      overflow: auto;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-around;
      .rowBox {
        width: 45%;
        height: 50%;
        margin: 10px 0;
        padding: 10px;
        box-sizing: border-box;
        .rowBoxName {
          width: 100%;
          height: 13%;
          display: flex;
          line-height: 40px;
          font-size: 22px;
          font-bold: bold;
          color: #1367e4;
        }
        .rowPage {
          width: 100%;
          height: 87%;
        }
      }
      .rowBoxCen {
        width: 95%;
        height: 147%;
        margin: 10px 0;
        padding: 10px;
        box-sizing: border-box;
        .rowBoxCenName {
          width: 100%;
          height: 4%;
          display: flex;
          line-height: 40px;
          font-size: 22px;
          font-bold: bold;
          color: #1367e4;
        }
        .rowBoxCenPage {
          width: 100%;
          height: 93%;
          display: flex;
          flex-wrap: wrap;
          justify-content: space-around;
          align-items: center;
          .pageBox {
            width: 45%;
            height: 45%;
          }
        }
      }
      .rowBoxCenSearch {
        height: auto;
        .rowBoxCenNameSec {
          height: 48px;
        }
        .searchBox {
          width: 100%;
          height: auto;
          padding: 0 42px;
          .formBox{
            margin: 10px 0;
          }
          .listConotent {
            margin-top: 20px;
          }
        }
      }
    }
  }
}
.cell-item{
  display: flex;
  align-items: center;
}
.my-table {
  :deep(.el-table__header-wrapper) {
    .el-table__cell {
      background: #f0f6ff;
      height: 54px;
      color: rgba(0, 0, 0, 0.88);
    }
  }

  :deep(.el-table__row) {
    height: 54px;
  }

  :deep(.warning-row) {
    background: #f9fbff;
  }
}
.desCon{
  text-align: center;
  font-size: 26px;
  color: #1367e4;
  margin: 40px;
}
</style>