<template>
  <div id="model-page">
    <component :is="pageArray[pageType]" :parentProp="dataObj" @selectYear="selectYear" :modelCode="modelCode" :keyword="keyword" :queryTime="queryTime"></component>
  </div>
</template>

<script setup>
const pagePie = defineAsyncComponent(() => import("./page/pagePie.vue"));
const pageBar = defineAsyncComponent(() => import("./page/pageBar.vue"));
const pageColBar = defineAsyncComponent(() => import("./page/pageColBar.vue"));
const pageFunnel = defineAsyncComponent(() => import("./page/pageFunnel.vue"));
const pageList = defineAsyncComponent(() => import("./page/pageList.vue"));
const pageCustomPie = defineAsyncComponent( () => import("./page/pageCustomPie.vue"))

const pageArray = {
  pagePie: pagePie,
  pageBar: pageBar,
  pageColBar: pageColBar,
  pageFunnel:pageFunnel,
  pageList: pageList,
  pageCustomPie: pageCustomPie,
};
defineProps({
  pageType: {
    type: String,
    default: "",
  },
  dataObj: {
    type: Object,
    default: {},
  },
  modelCode: {
    type: String,
    default: ''
  },
  keyword: {
    type: String,
    default: ''
  },
  queryTime: {
    type: String,
    default: ''
  }
});
const emit = defineEmits(["selectYear"]);
function  selectYear(year) {
  console.log(year)
  emit('selectYear',year)
}
</script>

<style scoped lang="less">
#model-page {
  width: 100%;
  height: 100%;
  font-size: 16px;
}
.render{
    display: flex;
    >div{
        flex:1;
    }
}
</style>