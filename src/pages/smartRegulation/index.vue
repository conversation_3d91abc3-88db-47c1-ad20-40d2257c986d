<template>
  <PageWrapper style="overflow-y: hidden">
    <div class="ai-box">
      <div class="container" v-loading="loading">
        <div class="container-header">
          <div class="left">
            <img
              src="../../assets/projectAi/ai-shadow.png"
              width="80"
              height="85"
              style="cursor: pointer"
            />
          </div>
          <!--              <div class="right" @click="handleListToSetGuide()" style="cursor: pointer">-->
          <!--                  <div class="right-text">流程引导</div>-->
          <!--              </div>-->
          <div class="list-header" id="ReportWirteDiv">
            <div
              class="list-name"
              :class="{ 'name-select': item.key == cardKey }"
              @click="handleSelectName(item)"
              v-for="(item, index) in cardNameList"
              :key="index"
            >
              {{ item.name }}
            </div>
          </div>
        </div>
        <div
          class="container-model"
          id="ReportWirteDiv2"
        >
          <el-tooltip
            v-for="(item, index) in selectModelList"
            placement="top"
            :key="index"
            popper-class="model-tooltip"
          >
            <template #content>
              <div class="font16 pd8">{{ item.modelContent }}</div>
            </template>
            <div
              class="item-model"
              :class="{ 'model-select': item.modelCode === modelCode }"
              @click="handleSelect(item)"
            >
              <div class="select-img" v-if="item.modelCode === modelCode"></div>
              <div class="img-bg">
                <img :src="item.modelLogo" alt="" width="100%" height="100%" />
              </div>
              <div class="content-text">
                <div class="text-title">{{ item.modelName }}</div>
                <div class="text-content">{{ item.modelContent }}</div>
              </div>
            </div>
          </el-tooltip>
        </div>
        <div class="container-search" id="ReportWirteDiv1">
          <div class="smart-regulation">
            <div class="search-border">
              <input
                type="text"
                :placeholder="placeholder"
                v-model="companyName"
                class="keyword"
                @keyup.enter="handleSearch"
              />
              <img
                src="../../assets/projectAi/search.png"
                width="25"
                height="25"
                class="search"
                @click.stop="handleSearch"
              />
            </div>
          </div>
        </div>
        <div
          class="action"
        >
          <span class="model-btn" id="ReportWirteDiv3">
            <div class="model-img"></div>
            <span class="model-btn-text" @click="handleSearch()"
              >点击开始智能分析</span
            >
            <div class="spinner">
              <div class="spinner1"></div>
            </div>
          </span>
        </div>
      </div>
      <el-drawer
        v-model="bigShowDrawer"
        v-if="bigShowDrawer"
        size="100%"
        class="my-drawer-all"
        :show-close="true"
      >
        <template #header>
          <h2></h2>
        </template>
        <ToolTree @closeToolTree="closeToolTree" :tree-data="treeData" />
      </el-drawer>
    </div>
    <el-drawer
      custom-class="my-drawer"
      v-model="isShowDrawer"
      v-if="isShowDrawer"
      size="75%"
      :show-close="false"
    >
      <template #header>
        <h2></h2>
      </template>
      <modelPage
        @selectYear="selectYear"
        :pageType="pageType"
        :dataObj="dataObj"
        :modelCode="modelCode"
        :keyword="companyName"
        :queryTime="queryTime"
      />
    </el-drawer>
    <el-drawer
      custom-class="my-drawer"
      v-model="groupShowDrawer"
      v-if="groupShowDrawer"
      size="100%"
      :show-close="true"
    >
      <template #header>
        <h2></h2>
      </template>
      <pageDimensionalAnalysis :dimensionalObj="treeData"  @closeDimensional="closeDimensional"/>
    </el-drawer>
    <el-drawer
      custom-class="my-drawer"
      v-model="analysisShowDrawer"
      v-if="analysisShowDrawer"
      size="100%"
      :show-close="true"
    >
      <template #header>
        <h2></h2>
      </template>
      <GroupAnalysis :analysisList="treeData"   @closeGroupAnalysis="closeGroupAnalysis"/>
    </el-drawer>
  </PageWrapper>
  <allDialogView
    ref="allDialogViewEl"
    :dialogTitle="'风险预警'"
    :dialogWidth="'95%'"
  >
    <template #content>
      <riskWarningList
        v-if="allDialogViewEl.showDialog "
        @handleCloseDialog="handleMore"
      ></riskWarningList>
    </template>
  </allDialogView>
  <allDialogView
          ref="statisticaTable"
          :dialogTitle="'统计表'"
          :dialogWidth="'95%'"
  >
      <template #content>
          <statisticaList
                  :modelCode="modelCode"
                  v-if="statisticaTable.showDialog"
                  @handleCloseDialog="handleStatisticaClose"
          ></statisticaList>
      </template>
  </allDialogView>
  <allDialogView
    ref="joinBidTable"
    :dialogTitle="'投标人共同投标分析'"
    :dialogWidth="'95%'"
  >
    <template #content>
      <jointBiddingTable
        v-if="joinBidTable.showDialog"
        @handleCloseDialog="handleClose"
      ></jointBiddingTable>
    </template>
  </allDialogView>
</template>

<script setup>
import modelPage from "./modelPage/index.vue";
import ToolTree from "./modelPage/components/toolTree.vue";
import GroupAnalysis from "./modelPage/components/groupAnalysis/groupAnalysis.vue";
import pageDimensionalAnalysis from "./modelPage/page/pageDimensionalAnalysis.vue";
import { httpPost } from "@wl-fe/http/dist";
import { ElMessage } from "element-plus";
import riskWarningList from "@/pages/home/<USER>/riskWarningList";
import statisticaList from "@/pages/smartRegulation/modelPage/components/statisticaList/index.vue";
import jointBiddingTable from "./modelPage/components/jointBiddingTable/index.vue";
import AdvancedSearchForm from "@/pages/smartRegulation/modelPage/components/statisticaList/AdvancedSearchForm.vue";

const router = useRouter();
// import   "https://cdn.bootcdn.net/ajax/libs/intro.js/7.2.0/intro.min.js"
// import TreeChart from "@/pages/smartRegulation/modelPage/components/treeChart.vue";
const allDialogViewEl = ref();
const joinBidTable = ref();
const statisticaTable = ref();
const companyName = ref("");
const pageType = ref("");
const modelCode = ref("");
const isShowDrawer = ref(false);
const bigShowDrawer = ref(false);
const groupShowDrawer = ref(false);
const analysisShowDrawer = ref(false);
const modelList = ref([]);
const selectModelList = ref([]);
const cardNameList = ref([]);
const cardKey = ref(1);
const selectItem = ref("");
const queryTime = ref("2024");

const placeholder = ref("请按照提示输入搜索信息");
const handleMore = () => {
  allDialogViewEl.value.showDialog = !allDialogViewEl.value.showDialog;
};
const handleClose = () => {
  joinBidTable.value.showDialog = !joinBidTable.value.showDialog;
};
const handleStatisticaClose = () => {
    statisticaTable.value.showDialog = !statisticaTable.value.showDialog;
};
const closeGroupAnalysis = () => {
  analysisShowDrawer.value = !analysisShowDrawer.value;
};
const closeDimensional = () => {
  groupShowDrawer.value =!groupShowDrawer.value;
}
const closeToolTree = () => {
  bigShowDrawer.value = !bigShowDrawer.value;
};
const handleSelect = (item) => {
  console.log(item);
  companyName.value = "";
  selectItem.value = item;
  pageType.value = item.modelPage;
  modelCode.value = item.modelCode;
  console.log("当前模型", modelCode.value);
  placeholder.value = item.modelKey;
  if (!item.modelKey) {
    placeholder.value = item.modelContent;
  }
};
const dataObj = ref({});

// treeData
// const treeData = {
//   name: '[网联测试][演示]2024年沈阳黄姑某高标准农田建设工程项目-施工',
//   pValue: "0",
//   value: '210101TP010100062001001',
//   children: [{
//     name: '农业交易平台',
//     pValue: "1",
//     value: 'ET01',
//     children: [{
//       name: '金融平台内部测试',
//       pValue: "2",
//       value: '20230403',
//       children: [{
//         name: '农业农村招标文件制作工具',
//         pValue: "3",
//         value: '',
//         children: [{
//           name: '112323',
//           pValue: "4",
//           value: '',
//           children: []
//         }]
//       }]
//     }]
//   }]
// }
const treeData = ref(null);
const handleSearch = async (year) => {
  // groupShowDrawer.value = true;
  // return
  if (!pageType.value) {
    ElMessage({
      message: "请先选择监管模型",
      type: "error",
    });
    return;
  }
  queryTime.value = year;

  let info = {
    modelCode: modelCode.value,
    keyword: companyName.value,
    queryTime: year ? year : queryTime.value,
  };

  if (info.modelCode === "46") {
    joinBidTable.value.showDialog = true;
    return;
  }

  if (["54", "55", "56", "57"].includes(info.modelCode)) {
    statisticaTable.value.showDialog = true;
    return;
  }

  loading.value = true;
  try {
    const data = await httpPost("/smartRegulationController/getModel", info);
    dataObj.value = data;

    if (dataObj.value) {
      console.log(dataObj.value);
      console.log("看看", data);
      // if (info.modelCode === '32' || info.modelCode === '30' || info.modelCode === '31') {
      if (info.modelCode === "32") {
        if (dataObj.value.tree) {
          treeData.value = dataObj.value.tree;
          console.log(" treeData.value", treeData.value);
        }
        bigShowDrawer.value = true;
        return;
      }
      if (info.modelCode === "45") {
        if (dataObj.value.stList) {
          treeData.value = dataObj.value.stList;
          console.log(" treeData.value", treeData.value);
        }
        analysisShowDrawer.value = true;
        // groupShowDrawer.value = true;
        return;
      }
      // bigShowDrawer.value = true;
      // treeRef.value.handleInit(dataObj.value.tree);
      // console.log(treeRef.value);
      // return;
      // }
      isShowDrawer.value = true;
    }
  } catch (error) {
    console.error(error);
  } finally {
    loading.value = false;
  }
};

function handleSelectName({ key }) {
  if (key == 4) {
    var urls =
      "https://www.lntb.gov.cn/zjbdfront/#/login?" +
      btoa(encodeURIComponent("username=" + "lnszjt"));
    window.open(urls, "_blank");
    return;
  }
  if (key == 5) {
    router.push({
      path: "/home",
      query: {
        type: "map",
      },
    });
    // ElMessage.error("暂未开放!")
    return;
  }
  if (key == 6) {
    handleMore();
    return;
  }
  cardKey.value = key;
  selectModelList.value = [];
  if (key === "all") {
    selectModelList.value = modelList.value;
  }

  modelList.value.forEach((val) => {
    if (val.modelPcode === key) {
      selectModelList.value.push(val);
    }
  });
}
const loading = ref(false);
onMounted(async () => {
  const data = await httpPost("/smartRegulationController/getSmartModel");
  cardNameList.value = data.cardmodel;
  cardNameList.value.unshift({
    name: "全部",
    key: "all",
  });
  modelList.value = data.smartmodel;
  // 为每个项添加随机颜色作为背景属性
  // modelList.value = data.smartmodel.map(item => {
  //   const randomColor = colors[Math.floor(Math.random() * colors.length)];
  //   return { ...item, background: randomColor };
  // });
  handleSelectName(data.cardmodel[0]);
  // setTimeout(() => {
  //   handleListToSetGuide()
  // }, 1000);
});
// function handleListToSetGuide() {
//   listToSetGuide([
//     {
//       title: "1/4",
//       element: "#ReportWirteDiv",
//       intro: "选择模型种类",
//       position: "right",
//     },
//     {
//       title: "2/4",
//       element: "#ReportWirteDiv1",
//       intro: "输入要分析的单位名称",
//       position: "right",
//     },
//     {
//       title: "3/4",
//       element: "#ReportWirteDiv2",
//       intro: "选择要分析的监管模型",
//       position: "right",
//     },
//     {
//       title: "4/4",
//       element: "#ReportWirteDiv3",
//       intro: "点击开始智能分析",
//       position: "right",
//     },
//   ]);
// }
// const emit = defineEmits(["selectYear"]);
function selectYear(year) {
  console.log(year);
  handleSearch(year);
}

// function listToSetGuide(data) {
//   introJs()
//     .setOptions({
//       prevLabel: "上一步",
//       nextLabel: "下一步",
//       skipLabel: "跳过",
//       doneLabel: "完成",
//       tooltipClass: "intro-tooltip" /* 引导说明文本框的样式 */,
//       exitOnEsc: true /* 是否使用键盘Esc退出 */,
//       exitOnOverlayClick: false /* 是否允许点击空白处退出 */,
//       keyboardNavigation: true /* 是否允许键盘来操作 */,
//       showBullets: true /* 是否显示小圆点 */,
//       showStepNumbers: false /* 是否显示数字 */,
//       // stepNumbersOfLabel: '/',
//       showProgress: false /* 是否显示进度条 */,
//       scrollToElement: true /* 是否滑动到高亮的区域 */,
//       overlayOpacity: 0.65, // 遮罩层的透明度 0-1之间
//       positionPrecedence: [
//         "bottom",
//         "top",
//         "right",
//         "left",
//       ] /* 当位置选择自动的时候，位置排列的优先级 */,
//       disableInteraction: true /* 是否禁止与元素的相互关联 */,
//       hidePrev: true /* 是否在第一步隐藏上一步 */,
//       hideNext: false /* 是否在最后一步隐藏下一步 */,
//       // tooltipClass: 'duplicate-intro',
//       steps: data,
//     })
//     .oncomplete(() => {
//       //点击结束按钮后执行的事件
//     })
//     .onexit(() => {
//       //点击跳过按钮后执行的事件
//     })
//     .start();
// }
</script>
<style scoped lang="less">
.ai-box {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  .container {
    width: 100%;
    height: 100%;
    background: url("../../assets/projectAi/model-bg.png") no-repeat center;
    background-size: 100% 100%;
    &-header {
      position: relative;
      display: flex;
        justify-content: space-between;
        width: 100%;
        align-items: center;
        justify-content: space-evenly;
      .right {
        position: absolute;
        top: 50px;
        right: 30px;
        background: url("../../assets/projectAi/l-bg.png");
        width: 128px;
        height: 36px;
        background-size: contain;
        &-text {
          color: #ffffff;
          text-align: center;
          font-family: MicrosoftYaHei;
          font-size: 16px;
          line-height: 36px;
          font-style: normal;
          text-transform: none;
        }
      }
      .left {
        // position: absolute;
        // top: 21px;
        // left: 25px;
        width:5%;
      }
      .list-header {
        color: #333333;
        display: flex;
        justify-content: center;
        padding-top: 1%;
        width:90%;
        justify-content: space-around;
        .list-name {
          width: 144px;
          height: 54px;
          line-height: 54px;
          text-align: center;
          background: #fff;
          border-radius: 8px;
          margin-right: 40px;
          font-size: 18px;
          cursor: pointer;
        }
        .name-select {
          font-weight: 600;
          color: #ffffff;
          background: linear-gradient(to right, #7582f5, #8f82f5);
          float: right;
        }
      }
    }
    &-model {
      width: 93%;
      height: 56%;
      border-radius: 16px;
      background: #ffffff;
      margin: 0 auto;
      margin-top: 30px;
      // display: flex;
      // flex-direction: row;
      // flex-wrap: wrap;
      // align-items: center;
      padding: 30px;
      overflow: auto;
      // justify-content: space-between;
      .item-model {
        width: 23%;
        height: 30%;
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        border-radius: 20px;
        cursor: pointer;
        transition: all 0.3s;
        //background: #f0f5f9;
        background: #f0f5f9;
        position: relative;
        box-sizing: border-box;
        position: relative;
        // margin-right: 28px;
        // margin-bottom: 30px;
        // padding-left: 30px;
        // padding-right: 63px;
        overflow: hidden;
        margin-bottom: 10px;
        float: left;
        margin: 0 1%;
        margin-bottom: 10px;
        .select-img {
          width: 28px;
          height: 28px;
          position: absolute;
          top: 12px;
          right: 22px;
          background: url("/projectAi/dh.png") no-repeat;
          background-size: 100% 100%;
        }
        .img-bg {
          width: 21%;
          // height: 72px;
          border-radius: 16px;
          //background: red;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 0 2%;
        }
        .content-text {
          width: 70%;
          display: flex;
          flex-direction: column;
          justify-content: center;
          margin-left: 10px;
          .text-title {
            font-size: 20px;
            font-weight: bold;
            color: #333333;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          .text-content {
            margin-top: 8px;
            color: #5f6162;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
        //img {
        //  width: 60%;
        //  margin: 0 6% 0 2%;
        //}
      }
      .item-model:nth-child(4n) {
        margin-right: 0;
      }
      .model-select {
        background: url("/projectAi/card-select.png") no-repeat;
        background-size: 100% 100%;
        background: linear-gradient(to right, #a9d7fa, #e2cafb);
        transform: scale(1.1);
      }
    }
    &-search {
      width: 100%;
      height: 6%;
      margin-top: 1%;
      margin-bottom: 1%;
      border-radius: 16px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      .smart-regulation {
        width: 80%;
        height: 100%;
        display: flex;
        flex-direction: row;
        align-items: center;
        .my-img {
          width: 7%;
        }
        .search-border {
          height: 100%;
          width: 88%;
          margin: 0 auto;
          position: relative;
          border-top: none;
          background: #fff; //  #3369e7;
          overflow: hidden;
          border-radius: 4px;
          display: inline-block;
          .search {
            position: absolute;
            top: 10px;
            right: 16px;
            cursor: pointer;
          }
        }
        .keyword {
          box-sizing: inherit;
          width: 100%;
          padding-left: 2%;
          outline: none;
          color: #666;
          border-radius: 27px;
          height: 100%;
          font-size: 18px;
          float: left;
          border: 1px solid #7582f5;
        }
        .search-btn {
          box-sizing: inherit;
          text-decoration: none;
          height: 100%;
          font-size: 18px;
          float: left;
          display: block;
          width: 16%;
          background-color: #6699ff;
          background: linear-gradient(to right, #7776ff, #ae2cf1);
          color: #fff;
          text-align: center;
          line-height: 55px;
          cursor: pointer;
          border-top-right-radius: 4px;
          border-bottom-right-radius: 4px;
        }
      }
    }
    .action {
      margin: 0 auto;
      width: 100%;
      height: 16%;
      display: flex;
      flex-direction: row;
      justify-content: center;
      // background: #ffffff;
      box-sizing: border-box;
      padding: 16px 0;
      .model-btn {
        display: inline-block;
        position: relative;
        .model-btn-text {
          position: absolute;
          top: 0;
          left: 0;
          height: 100%;
          width: 100%;
          z-index: 5;
          font-size: 20px;
          box-sizing: border-box;
          padding: 27% 9%;
          font-weight: 600;
          cursor: pointer;
          background: -webkit-linear-gradient(
            rgb(186, 66, 255) 35%,
            rgb(0, 225, 255)
          );
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          display: inline-block;
        }
      }
    }
  }
}

:deep(.el-drawer__header) {
  margin-bottom: 0px;
  display: none;
}

/* 设置滚动条的样式 */
.model-box::-webkit-scrollbar {
  width: 6px;
  box-sizing: border-box;
}

/* 滚动条滑块 */
.model-box::-webkit-scrollbar-thumb {
  height: 5px;
  border-radius: 5px;
  background-color: #c2cede;
}

/*滚动槽*/
.model-box::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0);
  border-radius: 10px;
  background: #ffffff;
}

/* 滚动条滑块 鼠标悬浮 */
.model-box::-webkit-scrollbar-thumb:hover {
  background-color: #c2cede;
  // border: 1px solid rgba(0, 0, 0, 0.21);
  cursor: pointer;
}
.model-select::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  border-radius: 8px;
  //border: 3px solid red;
  //animation: borderAround 1.5s infinite linear;
}

@keyframes borderAround {
  0%,
  100% {
    clip: rect(0 312px 2px 0);
  }
  25% {
    clip: rect(0 312px 100px 146px);
  }
  50% {
    clip: rect(114px 312px 116px 0);
  }
  75% {
    clip: rect(0 2px 116px 0);
  }
}
/* From Uiverse.io by xXJollyHAKERXx */
.spinner {
  background-image: linear-gradient(rgb(186, 66, 255) 35%, rgb(0, 225, 255));
  width: 100px;
  height: 100px;
  animation: spinning82341 1.7s linear infinite;
  text-align: center;
  border-radius: 100px;
  filter: blur(1px);
  box-shadow: 0px -5px 20px 0px rgb(186, 66, 255),
    0px 5px 20px 0px rgb(0, 225, 255);
}

.spinner1 {
  background-color: #fff;
  width: 100px;
  height: 100px;
  border-radius: 100px;
  filter: blur(10px);
}

@keyframes spinning82341 {
  to {
    transform: rotate(360deg);
  }
}
.ai-box {
  :deep(.el-drawer__body) {
    padding: 0px !important;
  }
  :deep(.el-drawer__header) {
    background: #e4f6eb;
  }
}
@keyframes spinning82341 {
  to {
    transform: rotate(360deg);
  }
}
</style>
<style lang="less">
.font16 {
  font-size: 16px;
}
.pd8 {
  padding: 8px;
}
.el-popper.is-dark {
  background-color: rgba(168, 195, 252, 0.7);
  border: 1px solid #a8c3fc;
  color: #ffffff;
}
.el-popper.is-dark .el-popper__arrow:before {
  background-color: #a8c3fc;
  border: 1px solid #a8c3fc;
}
</style>

