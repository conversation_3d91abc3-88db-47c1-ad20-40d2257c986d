<template>
  <div id="perject-detail" v-loading="isLoading">
    <div class="project-header">
      <div class="header-box" v-if="dataInfo">
        <div class="header-text-s unify-title5">
          <span class="text-t">项目编号</span>
          <span >{{ dataInfo.tenderProjectCode }}</span>
        </div>
        <div style="margin-left: 10%" class="header-text-s unify-title5">
          <span class="text-t">项目名称</span>
          <span>{{ dataInfo.tenderProjectName }}</span>
        </div>
      </div>
      <el-button @click="handleBack" class="back-btn" type="primary" plain round
        >返回</el-button
      >
    </div>
    <div class="project-content">
      <div class="project-center">
        <div class="bid-box">
          <div class="bid-box-title unify-title unify-title1">标段信息</div>
          <div class="bid-table-box">
            <el-table
              :data="tableData"
              style="width: 100%"
              :height="200"
              :row-class-name="tableRowClassName"
              class="my-table"
              @selection-change="handleSelectionChange"
            >
              <el-table-column type="selection" width="30" :selectable="selectable"/>
              <el-table-column
                label="序号"
                type="index"
                :index="indexMethod"
                align="center"
                width="100px"
              />
              <el-table-column
                v-for="item in tableColData"
                :key="item.prop"
                align="center"
                :prop="item.prop"
                :label="item.label"
                min-width="20%"
              >
                <template #default="{ row }">
                  <div v-if="['status'].includes(item.prop)">
                    <span>{{ row[item.prop] ? row[item.prop] : "--" }}</span>
                  </div>
                  <span
                    v-if="!['approvalStatus', 'status'].includes(item.prop)"
                    >{{ row[item.prop] ? row[item.prop] : "--" }}</span
                  >
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
        <div class="people-box">
          <div class="people-box-title unify-title unify-title1">
            招标工作小组名单
          </div>
          <div class="people-list">
            <el-card class="card-box" v-for="(item,index) in peopleList" :key="index">
              <div class="content-box">
                <div class="img-box">
                  <img v-if="item.photo" class="my-img-have" :src='`data:image/jpeg;base64,${item.photo}`' alt="">
                  <img v-else class="my-img" src="/projectManagement/wrz.jpg" alt="">
                  <el-button  class="my-rz-btn" round type="success" @click="handleRealName(item)">实名认证</el-button>
                </div>
                <div class="info-box">
                  <el-descriptions :column="2" :size="'Large'" border>
                    <el-descriptions-item
                      label-class-name="my-descriptions"
                      :span="1"
                    >
                      <template #label>
                        <div class="cell-item">姓名</div>
                      </template>
                      <span>{{ item.personName }}</span>
                    </el-descriptions-item>
                    <el-descriptions-item
                        label-class-name="my-descriptions"
                        :span="1"
                    >
                      <template #label>
                        <div class="cell-item">认证方式</div>
                      </template>
                      <span>{{ item.origionType ? item.origionType : '暂无' }}</span>
                    </el-descriptions-item>
                    <el-descriptions-item
                        label-class-name="my-descriptions"
                        :span="2"
                    >
                      <template #label>
                        <div class="cell-item">职业资格</div>
                      </template>
                      <span>{{ item.qualName }}</span>
                    </el-descriptions-item>
                    <el-descriptions-item
                      label-class-name="my-descriptions"
                      :span="2"
                    >
                      <template #label>
                        <div class="cell-item">身份证号</div>
                      </template>
                      <span>{{ item.idCard }}</span>
                    </el-descriptions-item>
                    <el-descriptions-item
                      label-class-name="my-descriptions"
                      :span="1"
                    >
                      <template #label>
                        <div class="cell-item">职务</div>
                      </template>
                      <el-tag style="width: 70px;" :type="item.projectLeader == '组长' ? 'primary' : 'info' " :effect="item.projectLeader == '组长' ? 'light' : 'light'  " > {{ item.projectLeader }} </el-tag>
                    </el-descriptions-item>
                    <el-descriptions-item
                      label-class-name="my-descriptions"
                      :span="1"
                    >
                      <template #label>
                        <div class="cell-item">实名状态</div>
                      </template>
                      <!-- <span :style="{color:item.origionStatus == '已认证' ? 'green' : 'red' }">{{ item.origionStatus }}</span> -->
                      <el-tag style="width: 70px;" :type="item.origionStatus == '已认证' ? 'success' : 'error' " effect="light"> {{ item.origionStatus }} </el-tag>
                    </el-descriptions-item>
                    <el-descriptions-item
                      label-class-name="my-descriptions"
                      :span="2"
                    >
                      <template #label>
                        <div class="cell-item">认证时间</div>
                      </template>
                      <span>{{ item.origionDate ? item.origionDate : '暂无' }}</span>
                    </el-descriptions-item>
                  </el-descriptions>
                </div>
              </div>
            </el-card>
          </div>
        </div>
      </div>
    </div>
    <div class="project-footer">
      <el-button
        @click="handleBack"
        class="btn-footer"
        type="primary"
        plain
        round
        >关 闭</el-button
      >
    </div>
  </div>
</template>

  <script setup>
import IDcardInfo  from "@/utils/IDread/index.js";
import { useRouter, useRoute } from "vue-router";
import { DATA_ARRAY } from "./content.js";
import { httpGet, httpPost } from "@wl-fe/http/dist";
const router = useRouter();
const route = useRoute();
let isLoading = ref(false);
let tableData = ref([]);
const tableColData = ref(DATA_ARRAY);
let { query } = toRefs(route);
const { nodeCode, parentNodeCode, tenderProjectGuid,isSee ,bidSectionGuid } = query.value;
let dataInfo = ref(null);
let peopleList  = ref([]);
let selectList = ref([])
const indexMethod = (index) => {
  return index + 1;
};
const tableRowClassName = ({ rowIndex }) => {
  if (rowIndex % 2 !== 0) {
    return "warning-row";
  }
  return "";
};
const handleBack = () => {
  router.push({
    path: "/projectStatisticaData/projectList",
    query: {
      nodeCode: nodeCode,
      parentNodeCode: parentNodeCode,
    },
  });
};


onMounted(async () => {
  handleGetDetail();
});
const handleSelectionChange = async (val) => {
  selectList.value = val
};
function isToday(dateTimeStr) {
    // 将输入的日期时间字符串转换为JavaScript能识别的格式
    // 注意：这个转换假设输入格式总是"YYYY年MM月DD日 HH时mm分"
    const [year, month, day, hour, minute] = dateTimeStr.match(/\d+/g).map(Number);
    const today = new Date();
    // 创建一个表示同一天但时间为00:00:00的Date对象
    const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    // 创建一个表示输入日期的Date对象（时间部分被忽略）
    const inputDate = new Date(year, month - 1, day); // 月份在Date构造函数中是0-based
    // 比较年份、月份和日期是否相同
    return inputDate.getTime() === todayStart.getTime();
}
const selectable = (row) => {
  return isToday(row.bidOpenTime)
}
const handleGetDetail = async () => {
  isLoading.value = true;
  let data = await httpPost("/inviteTender/tenderSitcardSwiping/details", {tenderProjectGuid,isSee,bidSectionGuid});
  dataInfo.value = data
  peopleList.value = data.publicDataInfo.personGroupInfo;
  tableData.value = data.publicDataInfo.sitcardSwipingInfo;
  isLoading.value = false;
};
const selectUserInfo = ref(null)
const handleRealName = (item) => {
  if(selectList.value.length == 0){
    ElMessage({
      message: "请先勾选标段信息！",
      type: "error",
    });
    return
  }
  selectUserInfo.value = item
  IDcardInfo.methods.toConnect(callBack)
};
const callBack = (data) => {
  if(data == '失败' || JSON.stringify(data) === "{}"){
    ElMessage({
      message: "读取失败！请检查读卡器是否连接或者翻动身份证",
      type: "error",
    });
    return
  }

  console.log("data", data);
  if(data.idNumber != selectUserInfo.value.idCard){
    ElMessage({
      message: "身份信息与当前人员不匹配！",
      type: "error",
    });
    return
  }
  const list = selectList.value.map(val => {
    selectUserInfo.value.photo = data.imgSrc
    return {...selectUserInfo.value, ...val}
  })
  promptRealName(list)
};
const promptRealName = async(list) => {
  await httpPost("/inviteTender/tenderSitcardSwiping/save", list);
  ElMessage({
    message: "实名认证成功！",
    type: "success",
  });
  handleGetDetail()
 }
</script>
  <style scoped lang="less">
#perject-detail {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  .project-header {
    width: 100%;
    height: 6%;
    background-color: #fff;
    border-radius: 5px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    font-weight: bold;
    font-size: 16px;
    .header-box {
      display: flex;
      flex-direction: row;
      align-items: center;
      width: 80%;
      .header-lan {
        height: 30px;
        width: 8px;
        background: #438bfa;
        border-radius: 5px;
        margin-left: 21px;
      }
      .header-text-s {
        margin-left: 12px;
        font-size: 20px;
        .text-t {
          margin-right: 10px;
        }
      }
    }
    .back-btn {
      width: 90px;
      margin-right: 20px;
    }
  }
  .project-content {
    width: 100%;
    height: 85%;
    border-radius: 5px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    .project-center {
      width: 0;
      height: 100%;
      background: #fff;
      border-radius: 5px;
      overflow: hidden;
      flex: 1;
      box-sizing: border-box;
      padding: 10px;
      .bid-box {
        width: 100%;
        height: 36%;
        margin-bottom: 1%;
        .bid-box-title {
          color: #000;
        }
        .bid-table-box {
          padding: 5px 1%;
        }
      }
      .people-box {
        width: 100%;
        height: 64%;
        .people-box-title {
          color: #000;
        }
        .people-list {
          width: 100%;
          padding: 0% 1%;
          height: 89%;
          box-sizing: border-box;
          display: flex;
          flex-direction: row;
          flex-wrap: wrap;
          align-items: center;
          overflow: auto;
          .card-box {
            width: 32%;
            height: 45%;
            margin: 7px 8px;
            padding: 5px;
            :deep(.el-card__body) {
              width: 100%;
              height: 100%;
              padding: 0px;
            }
            :deep(.el-descriptions__cell){
             width: 75px !important;
             line-height: 13px;
            }
            .content-box {
              width: 100%;
              height: 100%;
              display: flex;
              justify-content: space-between;
              .img-box {
                width: 25%;
                height: 100%;
                // border: 1px solid #ccc;
                background: #fff;
                position: relative;
                text-align: center;
                border: 1px solid #ccc;
                .my-rz-btn{
                  width: 94%;
                  height: 30px;
                  line-height: 30px;
                  position: absolute;
                  bottom: 4px;
                  left: 4px;
                  font-size: 16px;
                }
                .my-img {
                  width: 93%;
                  height: 78%;
                }
                .my-img-have{
                  width: 100%;
                  height: 100%;
                }
              }
              .info-box {
                width: 74%;
                height: 100%;
                // border: 1px solid #ccc;
              }
            }
          }
        }
      }
    }
  }
  .project-footer {
    width: 100%;
    height: 6%;
    background: #fff;
    border-radius: 5px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    .btn-footer {
      width: 120px;
    }
    .btn-tolerance {
      width: 150px;
    }
  }
}
/* 设置滚动条的样式 */
::-webkit-scrollbar {
  width: 6px;
  box-sizing: border-box;
}

/* 滚动条滑块 */
::-webkit-scrollbar-thumb {
  height: 5px;
  border-radius: 5px;
  background-color: #c2cede;
}

/*滚动槽*/
::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0);
  border-radius: 10px;
  background: #ffffff;
}

/* 滚动条滑块 鼠标悬浮 */
::-webkit-scrollbar-thumb:hover {
  background-color: #c2cede;
  // border: 1px solid rgba(0, 0, 0, 0.21);
  cursor: pointer;
}
.my-table {
  :deep(.el-table__header-wrapper) {
    .el-table__cell {
      background: #f0f6ff;
      height: 55px;
      color: rgba(0, 0, 0, 0.88);
    }
  }
  :deep(.el-table__row) {
    height: 55px;
  }
  :deep(.warning-row) {
    background: #f9fbff;
  }
}
</style>
