<template>
    <div id="projectInstruct" v-loading="isLoading">
        <div class="content-section">
            <el-form
                    ref="badPersonnelRef"
                    :model="form"
                    :rules="rules"
                    label-width="120px"
            >
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="公告标题" prop="noticeName">
                            <el-input v-model="form.noticeName" placeholder="请填写公告标题"
                                      :disabled="instructType==2"/>
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item label="发布到外网" prop="publishStatus">
                            <el-radio-group v-model="form.publishStatus" :disabled="instructType==2">
                                <el-radio :value="0">否</el-radio>
                                <el-radio :value="1">是</el-radio>
                            </el-radio-group>
                        </el-form-item>
                    </el-col>

                    <el-col :span="24">
                        <el-form-item label="公告内容" prop="noticeContent" v-if="instructType==2">
                            <Editor
                                    :readOnly="true"
                                    v-model="form.noticeContent"
                                    :height="460"/>
                        </el-form-item>
                        <el-form-item label="公告内容" prop="noticeContent" v-if="instructType==1">
                            <Editor
                                    v-model="form.noticeContent"
                                    :height="460"/>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </div>

        <div class="content-section">
            <div class="personnel-section-header">
                <div class="personnel-label"></div>
                <el-button type="primary" round @click="addPersonnel()" v-if="instructType==1">添加人员信息</el-button>
            </div>
            <el-table
                    :data="personData"
                    style="width: 100%"
                    max-height="250"
                    border>
                <el-table-column
                        label="序号"
                        type="index"
                        align="center"
                        width="80"/>
                <el-table-column
                        prop="personName"
                        label="人员名称"
                        align="center"
                        min-width="200"/>
                <el-table-column
                        prop="idCard"
                        label="身份证号"
                        align="center"
                        min-width="200"/>
                <el-table-column
                        label="操作"
                        align="center">
                    <template #default="scope">
                        <el-button type="text" @click="deleteRow(scope.index,personData)" v-if="instructType==1">移除
                        </el-button>
                        <el-button type="text" @click="lookRow(scope.row)" v-if="instructType==2">查看</el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>

        <div class="btn-box">
            <el-button
                    type="primary"
                    class="my-instruct-btn"
                    round
                    @click="handleProInstruct()"
                    v-if="instructType==1">
                提交
            </el-button>
            <el-button
                    class="my-instruct-btn"
                    round
                    type="primary"
                    plain
                    @click="handleClose">
                关 闭
            </el-button>
        </div>

        <allDialogView ref="addPersonnelDialogViewEl" :dialogTitle='dialogTitle' :dialog-width="1200">
            <template #content>
                <AddPersonnelDialog
                        v-model="addPersonnelVisible"
                        :badPersonnelInfo="badPersonnelInfo"
                        :rules2="rules2"
                        :instructType="instructType"
                        :isAuto="isAuto"
                        :isUpdatePerson="isUpdatePerson"
                        :fileList="fileList"
                        :uploadUrl="uploadUrl"
                        @choose-personnel="chossePersonnel"
                        @close-add-personnel="closeAddPersonnel"
                        @change-not-auto-input="changeNotAutoInput"
                        @upload-success="handleSuccess"
                        @delete-img="handleDeleteImg"
                        @submit="handleAddPersonnelSubmit"
                />
            </template>
        </allDialogView>

        <ChoosePersonnelDialog
                v-model="choosePersonnelDialogVisible"
                :personData="personData"
                @select="onSelectPersonnel"
        />
    </div>
</template>

<script setup>

import {httpGet, httpPost} from "@wl-fe/http/dist";
import Editor from "@/components/Editor"
import {el} from "element-plus/es/locales.mjs";
import {ref} from "vue";
import AddPersonnelDialog from './AddPersonnelDialog.vue';
import ChoosePersonnelDialog from './ChoosePersonnelDialog.vue';

const props = defineProps({
    type: {
        type: String,
        default: "1"
    },

    badPersionDetail: {
        type: Object,
        default: {},
    },
});

onMounted(() => {
    form.value = props.badPersionDetail || {}
    form.value.publishStatus = props.badPersionDetail.publishStatus !== undefined
      ? Number(props.badPersionDetail.publishStatus)
      : 0
    personData.value = props.badPersionDetail?.personnelRelList ?? []
    instructType.value = props.type
    console.log(props.type)
})

//表单数据
const form = ref({})
const instructType = ref(1)
const rules = {
    'noticeName': [{required: true, message: "请填写公告标题", trigger: "blur"}],
    'publishStatus': [{required: true, message: "请选择是否发布外网", trigger: "blur"}],
    'noticeContent': [{required: true, message: "请填写公告内容", trigger: "blur"}]
}

const idCardPattern = /(^\d{15}$)|(^\d{17}([0-9Xx])$)/;
const rules2 = {
    personName: [
        {required: true, message: "请填人员姓名", trigger: "blur"},
        {min: 2, max: 20, message: "姓名长度2-20位", trigger: "blur"}
    ],
    idCard: [
        {required: true, message: "请填写身份证号码", trigger: "blur"},
        {pattern: idCardPattern, message: "身份证格式不正确", trigger: "blur"}
    ],
    thisProvince: [{required: true, message: "请选择是否本省", trigger: "blur"}],
    suspendBid: [{required: true, message: "请选择是否暂停投标活动", trigger: "blur"}],
    startAndEndRage: [{
        type: 'array',
        required: true,
        message: "请选择开始/结束时间",
        trigger: "change",
        validator: (rule, value, callback) => {
            if (!value || value.length !== 2) {
                callback(new Error('请选择完整的日期范围'))
            } else {
                callback()
            }
        }
    }],
}
const isUpdate = ref(true);
const emit = defineEmits(["handleClose"]);
const badPersonnelRef = ref();
const addPersonnelRef = ref()
//上传文件
const fileList = ref([]);

let searchInfo = reactive({
    pageNum: 1,
    pageSize: 10,
    keyword: ''
});

let total = ref(1);

//待添加人员列表
let newPersionList = ref([])

//已添加人员列表
let personData = ref([])
//添加人员弹窗
const addPersonnelVisible = ref(false)
//人员详情
let badPersonnelInfo = ref({})
const lookRow = (row) => {
    // 深拷贝，避免弹窗内编辑影响原数据
    badPersonnelInfo.value = JSON.parse(JSON.stringify(row))
    badPersonnelInfo.value.startAndEndRage = []
    if (row.startDate && row.endDate) {
        badPersonnelInfo.value.startAndEndRage.push(row.startDate);
        badPersonnelInfo.value.startAndEndRage.push(row.endDate);
    }
    // 附件处理
    if (row.attach) {
        fileList.value = [row.attach]
    } else {
        fileList.value = []
    }
    isUpdatePerson.value = false;
    isAuto.value = true;
    addPersonnelVisible.value = true;
    dialogTitle.value = "查看人员信息";
    addPersonnelDialogViewEl.value.showDialog = !addPersonnelDialogViewEl.value.showDialog;
}

const addPersonnelDialogViewEl = ref()
const dialogTitle = ref('')
const addPersonnel = () => {
    if (addPersonnelRef.value && addPersonnelRef.value.resetFields) {
        addPersonnelRef.value.resetFields();
    }
    badPersonnelInfo.value = {};
    isUpdatePerson.value = false;
    isAuto.value = true;
    addPersonnelVisible.value = true;
    dialogTitle.value = "新增人员";
    addPersonnelDialogViewEl.value.showDialog = !addPersonnelDialogViewEl.value.showDialog;
}
const choosePersonnelLoading = ref(false)
const isAuto = ref(true)
const changeNotAutoInput = () => {
    badPersonnelInfo.value = {}
    isAuto.value = false;
}

//添加人员弹窗
const choosePersonnelDialogVisible = ref(false);

const chossePersonnel = async () => {
    badPersonnelInfo.value = {}
    choosePersonnelDialogVisible.value = true
    choosePersonnelLoading.value = true
    try {
        let params = {
            pageNum: searchInfo.pageNum,
            pageSize: searchInfo.pageSize,
            keyword: searchInfo.keyword
        };
        let data = await httpPost('/inviteTender/bad/personnel/listAddPerson', params);
        total.value = data.total;
        const existingIdCards = new Set(personData.value.map(p => p.idCard))
        newPersionList.value = data.rows
    } catch (error) {
        console.log(error)
    } finally {
        choosePersonnelLoading.value = false
    }
}
const closeAddPersonnel = async () => {
    addPersonnelVisible.value = false;
    if (addPersonnelDialogViewEl.value) {
        addPersonnelDialogViewEl.value.showDialog = false;
    }
}

const handleSizeChange = (val) => {
    console.log(val);
    searchInfo.pageSize = val
};
const handleChangePage = (val) => {
    console.log(val);
    searchInfo.pageNum = val;
};

const selectedPerson = ref(null);
//选择添加人员
const comfirPersonnel = () => {
    if (selectedPerson.value) {
        badPersonnelInfo.value.personName = selectedPerson.value.personName
        badPersonnelInfo.value.idCard = selectedPerson.value.idCard
        badPersonnelInfo.value.unitName = selectedPerson.value.legalName
        badPersonnelInfo.value.legalCode = selectedPerson.value.legalCode
        badPersonnelInfo.value.personId = selectedPerson.value.legalGuid
        choosePersonnelDialogVisible.value = false;
        searchInfo.keyword = '';
        searchInfo.pageNum = 1;
    } else {
        ElMessage.warning('请先选择人员');
    }
};

//提交添加人员
const sbmitPersonnel = async () => {
    if (!addPersonnelRef.value) {
        return;
    }
    await addPersonnelRef.value.validate(async (valid, fields) => {
        if (valid) {
            // 防止重复身份证号
            if (personData.value.some(p => p.idCard === badPersonnelInfo.value.idCard && (!isUpdatePerson.value || p !== badPersonnelInfo.value))) {
                ElMessage.error('该身份证号人员已存在！');
                return;
            }
            badPersonnelInfo.value.startDate = badPersonnelInfo.value.startAndEndRage[0]
            badPersonnelInfo.value.endDate = badPersonnelInfo.value.startAndEndRage[1]
            if (hasIdCard(badPersonnelInfo.value.idCard)) {
                removeIdCard(badPersonnelInfo.value.idCard)
            }
            let person = JSON.parse(JSON.stringify(badPersonnelInfo.value))
            person.personType = isAuto.value ? 1 : 2
            personData.value.push(person)
            badPersonnelInfo.value = {}
            addPersonnelVisible.value = false
        }
    })
}

const removeIdCard = (idToCheck) => {
    personData.value = personData.value.filter(
        person => person.idCard !== idToCheck
    )
}

const hasIdCard = (idToCheck) => {
    return personData.value.some(person => person.idCard === idToCheck)
}
const startAndEndRage = ref([])

//移除方法
const deleteRow = (index, rows) => {
    rows.splice(index, 1);
}
const isUpdatePerson = ref(false);

//选择人员
const handleUnitRowClick = (row) => {
    selectedPerson.value = row;
};

//上传文件成功后回调方法
const handleSuccess = (res, uploadFiles) => {
    console.log(res);
    fileList.value = [
        {
            id: res.data.id,
            attachmentFileName: res.data.attachmentFileName,
            url: res.data.url,
        },
    ];
    badPersonnelInfo.value.attachId = res.data.id;
};

//删除文件
const handleDeleteImg = () => {
    fileList.value = [];
    badPersonnelInfo.value.attachUrl = '';
};

const uploadUrl = `${import.meta.env.VITE_APP_RQUEST_API}sysUpload/uploadFile`
const isLoading = ref(false);

const handleProInstruct = async () => {
    if (!badPersonnelRef.value) return;
    await badPersonnelRef.value.validate(async (valid, fields) => {
        if (valid) {
            form.value.personnelRelList = personData.value
            console.log('form', form.value);
            isLoading.value = true;
            try {
                if (!form.value.id) {
                    await httpPost('/inviteTender/bad/personnel', form.value);
                } else {
                    form.value.createTime = null;
                    form.value.createBy = null;
                    form.value.publishBy = null;
                    await httpPost('/inviteTender/bad/personnel/update', form.value);
                }
                isLoading.value = false;
                ElMessage({
                    message: "提交成功！",
                    type: "success",
                });
                handleClose('refresh')
            } catch (error) {
                isLoading.value = false;
            }

        }
    });
};
const handleClose = (refresh) => {
    emit("handleClose", refresh);
};

const onSelectPersonnel = (person) => {
    badPersonnelInfo.value.personName = person.personName
    badPersonnelInfo.value.idCard = person.idCard
    badPersonnelInfo.value.unitName = person.legalName
    badPersonnelInfo.value.legalCode = person.legalCode
    badPersonnelInfo.value.personId = person.legalGuid
};

// 新增人员弹窗表单提交
const handleAddPersonnelSubmit = (person) => {
    // 防止重复身份证号
    if (personData.value.some(p => p.idCard === person.idCard)) {
        ElMessage.error('该身份证号人员已存在！');
        return;
    }
    // 处理日期
    if (person.startAndEndRage && person.startAndEndRage.length === 2) {
        person.startDate = person.startAndEndRage[0];
        person.endDate = person.startAndEndRage[1];
    }
    person.personType = isAuto.value ? 1 : 2;
    personData.value.push(person);
    addPersonnelVisible.value = false;
    if (addPersonnelDialogViewEl.value) {
        addPersonnelDialogViewEl.value.showDialog = false;
    }
};

</script>

<style scoped lang="less">
#projectInstruct {
  width: 80%;
  min-width: 1100px;
  margin: 0 auto;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 16px 0 rgba(64, 158, 255, 0.08);
  padding: 32px 40px 32px 40px;

  .unify-title {
    font-size: 18px;
    font-weight: 600;
    color: #222;
    margin-bottom: 24px;
    padding-left: 0;
    letter-spacing: 1px;
  }

  .content-section {
    padding: 0 0 24px 0;
    box-sizing: border-box;
    width: 100%;
  }

  .personnel-section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    .personnel-label {
      font-size: 15px;
      color: var(--el-text-color-regular);
    }
  }

  .btn-box {
    width: 100%;
    height: 56px;
    margin-top: 28px;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;

    .my-instruct-btn {
      width: 160px;
      font-size: 16px;
    }
  }

  :deep(.el-form) {
    padding: 0;
    background: none;

    .el-form-item {
      margin-bottom: 24px;
      width: 100%;

      .el-form-item__label {
        text-align: left;
        font-size: 15px;
        color: #333;
        font-weight: 500;
        padding-right: 12px;
        min-width: 0;
        width: auto;
      }

      .el-form-item__content {
        width: 100%;
        margin-left: 0 !important;
      }
    }
  }

  :deep(.el-table) {
    margin-top: 12px;
    width: 100%;
    border-radius: 6px;
    overflow: hidden;
    background: #fff;

    .el-table__header-wrapper {
      th {
        background-color: #f5f7fa;
        color: #222;
        font-weight: 600;
        font-size: 15px;
        border-bottom: 1.5px solid #e4e7ed;
      }
    }

    .el-table__row {
      font-size: 15px;

      td {
        color: #333;
        background: #fff;
      }
    }
  }

  :deep(.el-pagination) {
    margin-top: 16px;
    justify-content: flex-end;
  }
}

.personnel-form {
  padding: 0;
  width: 100%;
  box-sizing: border-box;

  .input-with-button {
    display: flex;
    gap: 10px;
    align-items: center;
    width: 100%;

    .el-input {
      flex: 1;
    }

    .button-group {
      display: flex;
      gap: 10px;
      flex-shrink: 0;
    }
  }

  .form-tip {
    color: #409EFF;
    font-size: 13px;
    margin-top: 6px;
    background: #f4f8ff;
    border-radius: 4px;
    padding: 6px 12px;
    display: flex;
    align-items: center;
    gap: 4px;
  }

  .file-list {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-top: 10px;

    .delete-btn {
      color: #f56c6c;

      &:hover {
        color: #f78989;
      }
    }
  }
}

.my-dialog {
  :deep(.el-dialog__header) {
    background-color: #409eff;
    margin: 0;
    padding: 15px 20px;

    .el-dialog__title {
      color: white;
      font-size: 16px;
      font-weight: 500;
    }

    .el-dialog__headerbtn .el-dialog__close {
      color: white;
    }
  }

  :deep(.el-dialog__body) {
    padding: 0;
    width: 100%;
  }

  :deep(.el-dialog__footer) {
    padding: 15px 20px;
    border-top: 1px solid #e4e7ed;
  }
}

.dialog-footer {
  display: flex;
  justify-content: center;
  gap: 20px;
}

.table-container {
  padding: 0;
  margin-bottom: 20px;
  width: 100%;
  box-sizing: border-box;

  :deep(.el-table) {
    width: 100%;

    .el-table__row {
      cursor: pointer;

      &.current-row {
        background-color: #ecf5ff !important;

        td {
          background-color: #ecf5ff !important;
        }
      }

      &:hover {
        background-color: #f5f7fa;
      }
    }
  }
}

.pagination-box {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

// 富文本内容美化
:deep(.ql-container), :deep(.el-editor__inner), :deep(.el-editor) {
  min-height: 180px;
  font-size: 15px;
  color: #222;
  background: #fafbfc;
  border-radius: 6px;
  padding: 12px 18px;
  box-sizing: border-box;
  text-align: left;
}

:deep(.ql-editor) {
  padding: 0 !important;
}
</style>
