<template>
    <div style="padding: 20px">
        <el-form
                :model="badPersonnelInfo"
                ref="addPersonnelRef"
                :rules="rules2"
                label-width="140px"
                class="personnel-form"
                status-icon
        >
            <el-row>
                <el-col :span="24">
                    <el-form-item label="人员姓名" prop="personName">
                        <div class="input-with-button" style="display: flex; align-items: center; gap: 10px;">
                            <el-input v-model="badPersonnelInfo.personName" :disabled="isAuto || instructType==2"
                                      style="flex: 1;"/>
                            <el-button type="primary" size="small" @click="$emit('choose-personnel')"
                                       v-if="instructType==1 && !isUpdatePerson">从人员库挑选
                            </el-button>
                            <el-button type="success" size="small" @click="changeNotAutoInput"
                                       v-if="instructType==1 && !isUpdatePerson">手动输入
                            </el-button>
                        </div>
                        <template v-if="instructType==1 && !isUpdatePerson">
                            <div class="form-tip beautify-tip">
                                <el-icon style="vertical-align: middle; color: #409EFF; margin-right: 4px;">
                                    <i-ep-info-filled/>
                                </el-icon>
                                <span>如需添加主体云库中未包含的人员，请点击<b>手动输入</b> ，如需从主体云库中选取人员，请点击<b>从人员库挑选</b> </span>
                            </div>
                        </template>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="身份证号码" prop="idCard">
                        <el-input :disabled="isAuto || instructType==2" v-model="badPersonnelInfo.idCard"/>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="是否本省" prop="thisProvince">
                        <el-select v-model="badPersonnelInfo.thisProvince" style="width: 100%"
                                   :disabled="instructType==2">
                            <el-option label="是" :value="true"/>
                            <el-option label="否" :value="false"/>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="暂停投标活动" prop="suspendBid">
                        <el-select v-model="badPersonnelInfo.suspendBid" style="width: 100%"
                                   :disabled="instructType==2">
                            <el-option label="是" :value="true"/>
                            <el-option label="否" :value="false"/>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="开始/结束日期" prop="startAndEndRage">
                        <el-date-picker
                                :disabled="instructType==2"
                                clearable="true"
                                v-model="badPersonnelInfo.startAndEndRage"
                                type="daterange"
                                value-format="YYYY-MM-DD"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                                style="width: 100%"
                        />
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item label="加入原因">
                        <el-input v-model="badPersonnelInfo.joinCause" type="textarea" :rows="3"
                                  :disabled="instructType==2"/>
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item label="备注">
                        <el-input v-model="badPersonnelInfo.remark" type="textarea" :rows="3"
                                  :disabled="instructType==2"/>
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item label="相关电子附件" v-if="instructType==1">
                        <el-upload
                                v-model:file-list="fileListLocal"
                                class="upload-demo"
                                :action="uploadUrl"
                                multiple
                                :on-success="handleSuccess"
                                :limit="1"
                        >
                            <el-button type="primary" v-if="fileListLocal.length==0">点击上传</el-button>
                        </el-upload>
                        <div class="file-list" v-if="fileListLocal.length > 0">
                            <el-link :href="fileListLocal[0].url" target="_blank">{{
                                fileListLocal[0].attachmentFileName
                                }}
                            </el-link>
                            <el-button type="text" class="delete-btn" @click="handleDeleteImg">
                                <el-icon>
                                    <CircleCloseFilled/>
                                </el-icon>
                            </el-button>
                        </div>
                    </el-form-item>
                    <el-form-item label="相关电子附件"
                                  v-if="instructType==2 && fileListLocal!=null && fileListLocal.length!=0">
                        <el-link :href="fileListLocal[0].url" target="_blank">{{
                            fileListLocal[0].attachmentFileName
                            }}
                        </el-link>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <div class="dialog-footer">
            <el-button
                v-if="instructType!=2"
                type="primary"
                class="my-instruct-btn"
                round
                @click="handleSubmit"
                >
                提交
            </el-button>
            <el-button
                class="my-instruct-btn"
                round
                type="primary"
                plain
                @click="$emit('close-add-personnel')"
                >
                关 闭
            </el-button>
        </div>
    </div>
</template>

<script setup>
import {ref, defineProps, defineEmits, watch} from 'vue';
import {ElMessage} from 'element-plus';

const props = defineProps({
    badPersonnelInfo: Object,
    rules2: Object,
    instructType: Number,
    isAuto: Boolean,
    isUpdatePerson: Boolean,
    fileList: Array,
    uploadUrl: String
});
const emit = defineEmits(['choose-personnel', 'submit', 'update:fileList', 'change-not-auto-input', 'upload-success', 'delete-img', 'update:modelValue']);
const addPersonnelRef = ref();

// 本地 fileList 代理
const fileListLocal = ref(props.fileList ? [...props.fileList] : []);

watch(() => props.fileList, (val) => {
    fileListLocal.value = val ? [...val] : [];
});

watch(fileListLocal, (val) => {
    emit('update:fileList', val);
}, {deep: true});

const changeNotAutoInput = () => {
    emit('change-not-auto-input');
};
const handleSuccess = (res, uploadFiles) => {
    emit('upload-success', res, uploadFiles);
};
const handleDeleteImg = () => {
    emit('delete-img');
};
const handleSubmit = () => {
    console.log('handleSubmit', addPersonnelRef.value);
    if (!addPersonnelRef.value) return;
    addPersonnelRef.value.validate((valid) => {
        if (valid) {
            emit('submit', {...props.badPersonnelInfo});
            emit('update:modelValue', false);
        }
        // 校验失败时，Element Plus 会自动高亮并显示错误信息
    });
};
</script>

<style scoped>
.dialog-footer {
    display: flex;
    justify-content: center;
    gap: 20px;
}

.beautify-tip {
    color: #409EFF;
    font-size: 13px;
    margin-top: 4px;
    background: #f4f8ff;
    border-radius: 4px;
    padding: 6px 12px;
    display: flex;
    align-items: center;
    gap: 4px;
}

.el-form-item.is-error .el-input__inner,
.el-form-item.is-error .el-textarea__inner {
    border-color: #f56c6c !important;
    box-shadow: 0 0 0 2px rgba(245, 108, 108, 0.08);
}

.el-form-item__error {
    color: #f56c6c;
    font-size: 13px;
    margin-top: 2px;
    line-height: 1.5;
    background: #fff0f0;
    border-radius: 3px;
    padding: 2px 8px;
    display: inline-block;
}

.upload-demo {
    display: flex;
}
.my-instruct-btn {
  width: 160px;
  font-size: 16px;
  border-radius: 24px;
}
</style> 