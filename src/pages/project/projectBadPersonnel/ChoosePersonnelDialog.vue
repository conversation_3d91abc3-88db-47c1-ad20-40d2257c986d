<template>
  <el-drawer
    :model-value="modelValue"
    @update:model-value="val => $emit('update:modelValue', val)"
    title="选择人员"
    direction="rtl"
    size="50%"
    :close-on-click-modal="false"
    class="my-drawer"
  >
    <div style="display: flex; gap: 10px; margin-bottom: 10px; margin-left: 20px; align-items: center;">
      <el-input
        v-model="searchInfo.keyword"
        placeholder="请输入姓名或身份证号"
        clearable
        style="width: 260px"
        @keyup.enter.native="handleSearchPersonnel"
      />
      <el-button type="primary" @click="handleSearchPersonnel">搜索</el-button>
      <el-button @click="handleResetSearch">重置</el-button>
    </div>
    <div class="table-container">
      <el-table
        v-loading="choosePersonnelLoading"
        :data="newPersionList"
        style="width: 100%"
        @row-click="handleUnitRowClick"
        highlight-current-row
        border
      >
        <el-table-column width="60" align="center">
          <template #default="{ row }">
            <el-radio
              :disabled="row.isDisabled"
              v-model="selectedPerson"
              :label="row"
              @change="handleUnitRowClick(row)"
            >
              <span></span>
            </el-radio>
          </template>
        </el-table-column>
        <el-table-column type="index" label="序号" width="60" align="center" />
        <el-table-column prop="personName" label="人员名称" align="center" />
        <el-table-column prop="idCard" label="身份证号" align="center" />
        <el-table-column prop="legalName" label="单位名称" align="center" />
        <el-table-column prop="legalCode" label="统一社会信用代码" align="center" />
      </el-table>
    </div>
    <div class="pagination-box pagination-right">
      <el-pagination
        v-model:current-page="searchInfo.pageNum"
        v-model:page-size="searchInfo.pageSize"
        :total="total"
        :page-sizes="[10, 20, 30, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleChangePage"
      />
    </div>
    <div class="dialog-footer">
      <el-button type="primary" @click="comfirPersonnel">确 定</el-button>
    </div>
  </el-drawer>
</template>

<script setup>
import { ref, reactive, watch, defineEmits, defineProps } from 'vue';
import { httpPost } from "@wl-fe/http/dist";

const props = defineProps({
  modelValue: Boolean,
  personData: Array,
});
const emit = defineEmits(["update:modelValue", "select"]);

const choosePersonnelLoading = ref(false);
const searchInfo = reactive({
  pageNum: 1,
  pageSize: 15,
  keyword: ''
});
const total = ref(1);
const newPersionList = ref([]);
const selectedPerson = ref(null);

watch(() => props.modelValue, (val) => {
  if (val) {
    handleSearchPersonnel();
  }
});

const handleSearchPersonnel = async () => {
  choosePersonnelLoading.value = true;
  try {
    let params = {
      keyword: searchInfo.keyword
    };
    let data = await httpPost('/inviteTender/bad/personnel/listAddPerson' + `?pageNum=${searchInfo.pageNum}&pageSize=${searchInfo.pageSize}`, params);
    total.value = data.total;
    newPersionList.value = data.rows;
  } catch (error) {
    console.log(error)
  } finally {
    choosePersonnelLoading.value = false;
  }
}
const handleResetSearch = async () => {
  searchInfo.keyword = '';
  searchInfo.pageNum = 1;
  await handleSearchPersonnel();
}
const handleSizeChange = (val) => {
  searchInfo.pageSize = val;
  handleSearchPersonnel();
};
const handleChangePage = (val) => {
  searchInfo.pageNum = val;
  handleSearchPersonnel();
};
const handleUnitRowClick = (row) => {
  selectedPerson.value = row;
};
const comfirPersonnel = () => {
  if (selectedPerson.value) {
    emit("select", selectedPerson.value);
    emit("update:modelValue", false);
    searchInfo.keyword = '';
    searchInfo.pageNum = 1;
  }
};
</script>

<style scoped>
.table-container {
  padding: 0 20px;
  margin-bottom: 20px;
  width: 100%;
  box-sizing: border-box;
}
.dialog-footer {
  display: flex;
  justify-content: center;
  gap: 20px;
}
.pagination-right {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding-right: 20px;
}
</style> 