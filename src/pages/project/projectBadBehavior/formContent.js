import { getOptionList , getSyscBadBehaviorTypeList} from './formOptionList'

export const FORM_BEHAVIOR_INFO = [
    {
        label: "单位名称",
        prop: "legalName",
        type: "INPUT",
        placeholder: "请选择单位名称",
        span: 12,
        disabled: false,
        optionList: []
    },
    {
        label: "统一社会信用代码",
        prop: "legalCode",
        type: "INPUT",
        placeholder: "请输入统一社会信用代码",
        span: 12,
        disabled: false
    },
    {
        label: "是否暂停招标活动",
        prop: "isPast",
        type: "RADIO",
        placeholder: "请选择是否暂停招标活动",
        span: 12,
        disabled: false,
        radioList: [{
            label: "是",
            value: 1
        }, {
            label: "否",
            value: 0
        }]
    },
    {
        label: "是否建设工程信息网注册",
        prop: "isRegister",
        type: "RADIO",
        placeholder: "请选择是否建设工程信息网注册",
        span: 12,
        disabled: false,
        radioList: [{
            label: "是",
            value: 1
        }, {
            label: "否",
            value: 0
        }]
    },
    {
        label: "处罚起始日期",
        prop: "punishStartTime",
        type: "DATE",
        placeholder: "请选择处罚起始日期",
        span: 12,
        disabled: false
    },
    {
        label: "处罚结束日期",
        prop: "punishEndTime",
        type: "DATE",
        placeholder: "请选择处罚结束日期",
        span: 12,
        disabled: false
    },
    
    {
        label: "是否建筑领域不良行为",
        prop: "isConstructionField",
        type: "RADIO",
        placeholder: "请选择是否建筑领域不良行为",
        span: 12,
        disabled: false,
        radioList: [{
            label: "是",
            value: 1
        }, {
            label: "否",
            value: 0
        }]
    },
    {
        label: "是否发布外网",
        prop: "isPublishNetword",
        type: "RADIO",
        placeholder: "请选择是否发布外网",
        span: 12,
        disabled: false,
        radioList: [{
            label: "是",
            value: 1
        }, {
            label: "否",
            value: 0
        }]
    },
    {
        label: "发布开始日期",
        prop: "sendStartTime",
        type: "DATE",
        placeholder: "请选择发布开始日期",
        span: 12,
        disabled: false
    },
    {
        label: "发布截止日期",
        prop: "sendEndTime",
        type: "DATE",
        placeholder: "请选择发布截止日期",
        span: 12,
        disabled: false
    },
    {
        label: "不良行为",
        prop: "badBehaviorContent",
        type: "TEXTAREA",
        placeholder: "请输入不良行为",
        span: 12,
        disabled: false
    },
    {
        label: "情节后果",
        prop: "consequencesPlot",
        type: "TEXTAREA",
        placeholder: "请输入情节后果",
        span: 12,
        disabled: false
    },
    {
        label: "处理部门",
        prop: "processingDepartment",
        type: "INPUT",
        placeholder: "请输入处理部门",
        span: 12,
        disabled: false
    },
    {
        label: "处理种类",
        prop: "processingType",
        type: "SELECT",
        placeholder: "请先选择处理种类",
        span: 12,
        disabled: false,
        optionList: []
    },
    {
        label: "不良行为类型",
        prop: "badBehaviorType",
        type: "CASCADER",
        placeholder: "请先选择不良行为类型",
        span: 12,
        disabled: false,
        optionList: [],
        defaultParams:{
            label: "name",
            value: "code",
            emitPath: false
          }
    },
    {
        label: "处罚内部编号",
        prop: "punishmentNumber",
        type: "INPUT",
        placeholder: "请输入处罚内部编号",
        span: 12,
        disabled: false
    },
    {
        label: "备注",
        prop: "remark",
        type: "TEXTAREA",
        placeholder: "请输入备注",
        span: 12,
        disabled: false
    },
    {
        label: "相关附件",
        prop: "sysAttachList",
        type: "UPLOAD",
        placeholder: "请上传",
        span: 24,
    }
]

// 然后在需要时更新optionList
async function updateOptionList() {
    const processingOptions = await getOptionList('clzl');
    const badBehaviorTypeList = await getSyscBadBehaviorTypeList();
    updateFormContentInfo(processingOptions, 'processingType');
    updateFormContentInfo(badBehaviorTypeList, 'badBehaviorType');
}

function updateFormContentInfo(options, prop) {
    const index = FORM_BEHAVIOR_INFO.findIndex(item => item.prop === prop);
    if (index !== -1) {
        FORM_BEHAVIOR_INFO[index].optionList = options;
    }
}
updateOptionList()
export const FORM_RULES = {
    legalName: [{ required: true, message: "请选择单位名称", trigger: "blur" }],
    legalCode: [{ required: true, message: "请输入统一社会信用代码", trigger: "blur" }],
    isRegister: [{ required: true, message: "请选择是否建设工程信息网注册", trigger: "blur" }],
    isPast: [{ required: true, message: "是否暂停招标活动", trigger: "blur" }],
    isConstructionField: [{ required: true, message: "请选择是否建筑领域不良行为", trigger: "blur" }],
    badBehaviorType: [{ required: true, message: "请先选择不良行为类型", trigger: "blur" }],
    sysAttachList: [{ required: true, message: "请上传相关附件", trigger: "blur" }],
}
