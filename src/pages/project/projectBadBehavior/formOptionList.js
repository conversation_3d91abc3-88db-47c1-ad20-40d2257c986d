import { httpGet, httpPost } from "@wl-fe/http/dist";
export async function getOptionList (type) {
    let data = await httpGet(`/system/dict/data/type/${type}`);
    let optionList = data.map((val) =>{
        return {
            label: val.dictLabel,
            value: val.dictValue
        }
    })
    return optionList
}

export async function getSyscBadBehaviorTypeList (type) {
    let data = await httpGet(`/api/syscBadBehavior/selectSyscBadBehaviorTypeList`);
    // let optionList = data.map((val) =>{
    //     return {
    //         label: val.name,
    //         value: val.code
    //     }
    // })
    return data
}