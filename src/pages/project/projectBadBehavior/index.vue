<template>
  <div id="projectInstruct" v-loading="isLoading">
    <el-form
      ref="badBehaviorRef"
      :model="badBehaviorForm"
      :rules="FORM_RULES"
      label-width="185px"
    >
      <div class="unify-title unify-title1">详情</div>
      <el-row>
        <el-col
          v-for="(item, index) in FORM_BEHAVIOR_INFO"
          :key="index"
          :span="item.span"
        >
          <el-form-item :label="item.label" :prop="item.prop">
            <div v-if="item.type === 'INPUT' && ['legalName'].includes(item.prop)" class="input-with-button">
              <el-input
                v-model="badBehaviorForm[item.prop]"
                :placeholder="item.placeholder"
              />
                <el-button type="primary" @click="openUnitSelectDialog">选择单位</el-button>
            </div>
            <el-input
              v-else-if="item.type === 'INPUT'"
              v-model="badBehaviorForm[item.prop]"
              :disabled="item.disabled || isShowFul"
              :placeholder="item.placeholder"
            />
            <el-input
              v-if="item.type === 'TEXTAREA'"
              :rows="2"
              v-model="badBehaviorForm[item.prop]"
              type="textarea"
              :placeholder="item.placeholder"
              :disabled="item.disabled || isShowFul"
            />
            <el-select
              v-if="item.type === 'SELECT'"
              v-model="badBehaviorForm[item.prop]"
              :placeholder="item.placeholder"
              :disabled="item.disabled || isShowFul"
            >
              <el-option
                v-for="dict in item.optionList"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              ></el-option>
            </el-select>
            <el-cascader :options="item.optionList" :props="item.defaultParams" :disabled="item.disabled || isShowFul"  v-model="badBehaviorForm[item.prop]" :show-all-levels="false" v-if="item.type === 'CASCADER'" style="width: 100%;">
              <template #default="{ node, data }">
                <span>{{ data.name }}</span>
                <span v-if="!node.isLeaf"> ({{ data.children.length }}) </span>
              </template>
            </el-cascader>
            <el-radio-group v-if="item.type === 'RADIO'"  v-model="badBehaviorForm[item.prop]" >
              <el-radio  :disabled="item.disabled || isShowFul" v-for="dict in item.radioList" :value="dict.value" :key="dict.value">{{ dict.label }}</el-radio>
            </el-radio-group>
            <el-date-picker
              style="width: 100%"
              v-if="item.type === 'DATE'"
              format="YYYY年MM月DD日 HH时mm分"
              value-format="YYYY-MM-DD HH:mm:ss"
              v-model="badBehaviorForm[item.prop]"
              type="datetime"
              :placeholder="item.placeholder"
              :disabled="item.disabled || isShowFul"
            />
            <el-upload
              v-if="item.type === 'UPLOAD' && !badBehaviorForm[item.prop].length"
              v-model:file-list="fileList"
              class="upload-demo"
              :action='uploadUrl'
              multiple
              :on-success="handleSuccess"
              :limit="1"
            >
              <el-button type="primary" v-if="isShowInitiate" >点击上传</el-button>
              <span v-else>暂无</span>
            </el-upload>
            <span
              v-show="item.type === 'UPLOAD' && badBehaviorForm[item.prop] != ''"
              v-for="(val, index) in fileList"
              :key="index"
            >
              <el-link :href="val.url" target="_blank">{{ val.attachmentFileName }}</el-link>
              <span v-if="isShowInitiate" class="my-span-delete" @click="handleDeleteImg"
                ><el-icon><CircleCloseFilled /></el-icon
              ></span>
            </span>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="btn-box">
      <el-button
        type="primary"
        class="my-instruct-btn"
        round
        @click="handleProInstruct()"
        v-if="!isShowFul"
      >
        提交</el-button
      >
      <el-button
        class="my-instruct-btn"
        round
        type="primary"
        plain
        @click="handleClose"
        >关 闭</el-button
      >
    </div>
    <!-- 选择单位弹窗 -->
    <el-dialog
      v-model="unitSelectDialogVisible"
      title="选择单位"
      width="60%"
      :close-on-click-modal="false"
      class="my-dialog"
    >
      <div class="unit-search-box">
        <el-input
          v-model="unitSearchKeyword"
          placeholder="请输入单位名称或统一社会信用代码"
          clearable
          @keyup.enter="handleUnitSearch"
        >
          <template #append>
            <el-button @click="handleUnitSearch">搜索</el-button>
          </template>
        </el-input>
      </div>
      <div class="table-container">
        <el-table
          :data="unitList"
          style="width: 100%"
          v-loading="unitLoading"
          @row-click="handleUnitRowClick"
          highlight-current-row
        >
            <el-table-column width="60" align="center">
                <template #default="{ row }">
                    <el-radio
                            v-model="selectedUnit"
                            :label="row"
                            @change="handleUnitRowClick(row)"
                    >
                        <span></span>
                    </el-radio>
                </template>
            </el-table-column>
          <el-table-column type="index" label="序号" width="60" align="center" />
          <el-table-column prop="tenderName" label="单位名称" />
          <el-table-column prop="tenderUscc" label="统一社会信用代码" />
        </el-table>
      </div>
      <div class="pagination-box">
        <el-pagination
          v-model:current-page="unitPageNum"
          v-model:page-size="unitPageSize"
          :total="unitTotal"
          :page-sizes="[10, 20, 30, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleUnitSizeChange"
          @current-change="handleUnitPageChange"
        />
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="unitSelectDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="handleUnitConfirm">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup >
import { FORM_BEHAVIOR_INFO,FORM_RULES,} from "./formContent";
import { httpGet, httpPost } from "@wl-fe/http/dist";
const emit = defineEmits(["handleClose"]);
const props = defineProps({
  type: {
    type: String,
    default: "",
  },
  tenderProjectGuid: {
    type: String,
    default: "",
  },
  detailRowGuid: {
    type: String,
    default: "",
  },
  behaviorDetail: {
    type: Object,
    default: "",
  },
});
const uploadUrl = `${import.meta.env.VITE_APP_RQUEST_API}sysUpload/uploadFile`
const isLoading = ref(false);
const badBehaviorRef = ref();
const fileList = ref([]);
const data = reactive({
  badBehaviorForm: {
    sysAttachList: [],
    feedAttachList:[]
  }
});
let { badBehaviorForm } = toRefs(data);
const isShowInitiate = computed(() => {
  return props.type == "1";
});
const isShowFul = computed(() => {
  return props.type == "2";
});
const handleSuccess = (res, uploadFiles) => {
  console.log(res);
  fileList.value = [
    {
      id: res.data.id,
      attachmentFileName: res.data.attachmentFileName,
      url: res.data.url,
    },
  ];
  badBehaviorForm.value.sysAttachList = [res.data];
};
const handleDeleteImg = () => {
  fileList.value = [];
  badBehaviorForm.value.sysAttachList = [];
};
const handleProInstruct = async () => {
  if (!badBehaviorRef.value) return;
  await badBehaviorRef.value.validate(async (valid, fields) => {
    if (valid) {
      console.log('badBehaviorForm',badBehaviorForm.value);
      badBehaviorForm.value.attachGuid = badBehaviorForm.value.sysAttachList[0].rowGuid
      isLoading.value = true;
      try {
        await httpPost('/inviteTender/badBehavior/saveBadBehavior', badBehaviorForm.value);
        isLoading.value = false;
        ElMessage({
          message: "提交成功！",
          type: "success",
        });
        handleClose('refresh')
      } catch (error) {
        isLoading.value = false;
      }

    }
  });
};
const handleClose = (refresh) => {
  emit("handleClose",refresh);
};
onMounted(() => {
  // handleGetBidsectionList(props.tenderProjectGuid)
  if(props.type == "2"){
     handleGetDetail()
  }
 }
)
const handleGetDetail = async () => {
  // isLoading.value = true;
  // let data = await httpPost('/inviteTender/asProjectPauseRestoration/details',{rowGuid:props.detailRowGuid});
  // isLoading.value = false
  console.log('data',props.behaviorDetail);
  badBehaviorForm.value = props.behaviorDetail;
  for (let key in badBehaviorForm.value) {
      // 如果属性的值为null，则删除该属性
      if (badBehaviorForm.value[key] === null) {
          delete badBehaviorForm.value[key];
      }
  }
  // badBehaviorForm.value = Object.assign({}, bidInfo.value , badBehaviorForm.value);
  if(badBehaviorForm.value.sysAttachList.length > 0){
    fileList.value = badBehaviorForm.value.sysAttachList
  }
}
const  bidList = ref([])
const handleGetBidsectionList = async (val) =>{
  let info ={
    keyword: val ? val :''
  }
   let data = await httpPost('/inviteTender/bidSection/getBidsectionList', info);
   console.log('data',data);
   bidList.value = data
}
const selectBidGuidChange = (val) => {
  console.log(val);
};
const unitSelectDialogVisible = ref(false);
const unitSearchKeyword = ref('');
const unitList = ref([]);
const unitLoading = ref(false);
const unitPageNum = ref(1);
const unitPageSize = ref(10);
const unitTotal = ref(0);
const selectedUnit = ref(null);

const handleUnitRowClick = (row) => {
  selectedUnit.value = row;
};

const handleUnitConfirm = () => {
  if (selectedUnit.value) {
    badBehaviorForm.value.legalName = selectedUnit.value.tenderName;
    badBehaviorForm.value.legalCode = selectedUnit.value.tenderUscc;
    unitSelectDialogVisible.value = false;
  } else {
    ElMessage.warning('请先选择单位');
  }
};

const openUnitSelectDialog = () => {
  unitSelectDialogVisible.value = true;
  selectedUnit.value = null;
  handleUnitSearch();
};

const handleUnitSearch = async () => {
  unitLoading.value = true;
  try {
    const res = await httpPost('/inviteTender/badBehavior/getCompanyList', {
      keyword: unitSearchKeyword.value,
      pageNum: unitPageNum.value,
      pageSize: unitPageSize.value
    });
    unitList.value = res.rows;
    unitTotal.value = res.total;
  } catch (error) {
    console.error('获取单位列表失败:', error);
  } finally {
    unitLoading.value = false;
  }
};

const handleUnitSizeChange = (val) => {
  unitPageSize.value = val;
  handleUnitSearch();
};

const handleUnitPageChange = (val) => {
  unitPageNum.value = val;
  handleUnitSearch();
};
</script>

<style scoped lang="less">
#projectInstruct {
  width: 100%;
  height: auto;
  .btn-box {
    width: 100%;
    height: 50px;
    margin-top: 20px;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    .my-instruct-btn {
      width: 150px;
    }
  }
  .my-span-delete {
    cursor: pointer;
    cursor: pointer;
    display: inline-block;
    vertical-align: -webkit-baseline-middle;
    margin-left: 20px;
    &:hover {
      color: red;
    }
  }
}
.input-with-button {
  display: flex;
  gap: 10px;
  align-items: center;
  width: 100%;
  .el-input {
    flex: 1;
  }

  .el-button {
    flex-shrink: 0;
  }
}

.my-dialog {
  :deep(.el-dialog__header) {
    background-color: #409eff;
    margin: 0;
    padding: 15px 20px;
    
    .el-dialog__title {
      color: white;
      font-size: 16px;
      font-weight: 500;
    }
    
    .el-dialog__headerbtn .el-dialog__close {
      color: white;
    }
  }
  
  :deep(.el-dialog__body) {
    padding: 20px;
  }
  
  :deep(.el-dialog__footer) {
    padding: 15px 20px;
    border-top: 1px solid #e4e7ed;
  }
}

.dialog-footer {
  display: flex;
  justify-content: center;
  gap: 20px;
}

.unit-search-box {
  margin-bottom: 20px;
  
  :deep(.el-input-group__append) {
    background-color: #409eff;
    border-color: #409eff;
    color: white;
    padding: 0 15px;
    
    &:hover {
      background-color: #66b1ff;
      border-color: #66b1ff;
    }
  }
}

.table-container {
  padding: 0 10px;
  margin-bottom: 20px;

  :deep(.el-table) {
    .el-table__row {
      cursor: pointer;
      
      &.current-row {
        background-color: #ecf5ff !important;
        td {
          background-color: #ecf5ff !important;
        }
      }
      
      &:hover {
        background-color: #f5f7fa;
      }
    }
  }
}

.pagination-box {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>
