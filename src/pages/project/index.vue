<template>
  <PageWrapper>
    <div class="project-home" v-loading="isLoading">
      <div
        class="project-card"
        :class="item.className"
        v-for="(item, index) in cardList"
        :key="index"
      >
        <div class="card-header"></div>
        <div class="card-text">{{ item.name }}</div>
        <div class="card-status">{{ item.status }}</div>
        <div class="card-list-box">
          <div v-if="item.type == 'inProcessSupervisionData' || item.type == 'electronicMonitoringData'">
            <div v-for="(val, index1) in item.dataList" :key="index1" >
              <div class="cart-list"  @click="handleViewList(val)">
                <span class="cart-list-name">
                  {{ val.name }}
                </span>
                <span class="cart-list-num">{{ val.value }} </span>
                <span class="cart-list-nav">
                  <el-icon><ArrowRightBold /></el-icon>
                </span>
              </div>
            </div>
          </div>
          <div v-if="item.type == 'afterProcessSupervisionData'" style="margin-top: 10px;" >
            <div class="sh-box" v-if="typeObj['MODULE_TYPE_DECISION_MAKING']">
              <div class="sen-title sen-title-first">
                    事前
              </div>
              <div>
                <div  class="cart-list-ts  cart-list-ts-top cart-width" @click="getJumpUrl(webRequestJc)">
                    <span class="cart-list-name-ts">
                      招标人自主决策系统
                    </span>
                    <span class="cart-list-nav-ts">
                      <el-icon><ArrowRightBold /></el-icon>
                    </span>
                </div>
              </div>
            </div>
            <div class="dashed-border"></div>
            <div class="sh-box">
              <div class="sen-title">
                    事后
              </div>
              <div>
                <div class="cart-list-ts  cart-list-ts-top cart-width"  @click="router.push({ path:'/projectStatisticaData/projectList',query:{ nodeCode:'TENDER_RECORD', parentNodeCode:'afterProcessSupervisionData',}});">
                  <span class="cart-list-name-ts">
                    <span class="name-color">招标</span>业务管理
                  </span>
                  <span class="cart-list-num">{{ projectNum }} </span>
                  <span class="cart-list-nav-ts">
                    <el-icon><ArrowRightBold /></el-icon>
                  </span>
                </div>
                <div class="cart-list-ts  cart-list-ts-top cart-width"  @click="router.push({ path:'/bidOpeningProjects'});">
                  <span class="cart-list-name-ts">
                    <span class="name-color">开标、评标</span>预览
                  </span>
                      <span class="cart-list-nav-ts">
                    <el-icon><ArrowRightBold /></el-icon>
                  </span>
                </div>

              <div >
                <div v-if="typeObj['MODULE_TYPE_TENDER']" class="cart-list-ts cart-list-ts-top cart-width" @click="router.push({ path:'/blindboxStatisticalData' });" >
                      <span class="cart-list-name-ts">
                        <span class="name-color">投标</span>盲盒系统
                      </span>
                      <span class="cart-list-nav-ts">
                        <el-icon><ArrowRightBold /></el-icon>
                      </span>
                  </div>
                  <div v-if="typeObj['MODULE_TYPE_BID']" class="cart-list-ts cart-list-ts-top cart-width" @click="getJumpUrl(webRequestKb)" >
                      <span class="cart-list-name-ts">
                        <span class="name-color">开标、评标</span>系统
                      </span>
                      <span class="cart-list-nav-ts">
                        <el-icon><ArrowRightBold /></el-icon>
                      </span>
                  </div>
                  <div v-if="typeObj['MODULE_TYPE_BID']" class="cart-list-ts cart-list-ts-top cart-width" @click="getJumpUrl(webRequestDb)" >
                      <span class="cart-list-name-ts">
                        <span class="name-color">定标</span>系统
                      </span>
                      <span class="cart-list-nav-ts">
                        <el-icon><ArrowRightBold /></el-icon>
                      </span>
                  </div>
                </div>
              </div>
            </div>
            <div class="dashed-border"></div>
            <div class="sh-box" >
              <div class="sen-title sen-title-last">
                    事后
              </div>
              <div>
                <div v-if="typeObj['MODULE_TYPE_OBJECTION_COMPLAINT']" class="cart-list-ts  cart-list-ts-top cart-width" @click="router.push({ path:'/tousuYIyiStatisticalData' });">
                    <span class="cart-list-name-ts">
                      <span class="name-color">异议投诉</span>处理系统
                    </span>
                    <span class="cart-list-nav-ts">
                      <el-icon><ArrowRightBold /></el-icon>
                    </span>
                </div>
                <div v-if="typeObj['MODULE_TYPE_CONTRACT_PERFORMANCE']" class="cart-list-ts cart-list-ts-top cart-width" @click="router.push({ path:'/contractPerformanceStatisticalData' });">
                    <span class="cart-list-name-ts">
                      <span class="name-color">合同履约</span>系统
                    </span>
                    <span class="cart-list-nav-ts">
                      <el-icon><ArrowRightBold /></el-icon>
                    </span>
                </div>
              </div>
            </div>
          </div>   
        </div>
        <div class="card-tip">{{ item.tip }}</div>
      </div>
    </div>
  </PageWrapper>
</template>

<script setup>
import { reactive } from "vue";
import { useRouter } from "vue-router";
import { httpGet } from "@wl-fe/http/dist";
import { openUrl } from '@/utils';
import useGloBalStore from "@/store/useGlobalStore";
const { user } = useGloBalStore();
const router = useRouter();
const isLoading = ref(false);
let cardListInitial = [
  {
    type: "afterProcessSupervisionData",
    name: "全流程监管",
    status: "（事后监管）",
    className: "card-list-sh",
    dataList: []
  },
  {
    type: "inProcessSupervisionData",
    name: "待核验",
    status: "（事中监管）",
    className: "card-list-sj",
    dataList: [],
  },
  {
    type: "electronicMonitoringData",
    name: "监察监管",
    status: "",
    className: "card-list-dz",
    dataList: []
  },
];
const projectNum = ref('');
const webRequestJc = "/singleLoginController/getDecisionMakingUrl"
const webRequestKb = "/singleLoginController/getOpenBidEvaluateUrl"
const webRequestDb = "/singleLoginController/getCalibrationUrl"
let cardList = reactive(cardListInitial);


const handleViewList = ({nodeCode,stage,name,jump}) => {
  if(name == '开标、评标' || name == '定标' || name == '招标'){
    getJumpUrl(jump)
    return
  }
  if(name == '电子监察'){
      ElMessage({
        message: "尚未开启！",
        type: "warning",
      });
     return
  }
  if(jump){
    router.push({
      path:jump
    });
    return
  }
  router.push({
    path:"/projectStatisticaData/projectList",
    query:{
      nodeCode:nodeCode,
      parentNodeCode:stage,
    }
  });
};
const getJumpUrl = async (url) => {
      const result = await httpGet(url, {rowGuid:""}, {
        transferResult: (result) => result,
      });
      if (!result) return;
      openUrl(result);
    };
const typeObj = ref({})
onMounted(async () => {
  isLoading.value = true;
  const dataInfo = await httpGet(
    "/api/projectManageHone/getStatisticsInProcessData",
    {}
  );
  isLoading.value = false;
    console.log(user)
    if(user.userType == '09'){
        cardList = cardList.filter(item => item.type ==  'electronicMonitoringData')
    }

  cardList.forEach((item, index) => {
      item.dataList = dataInfo[item.type]
  });

  typeObj.value = {
    'MODULE_TYPE_DECISION_MAKING': cardList[0].dataList.some(item => item.nodeCode === 'MODULE_TYPE_DECISION_MAKING'), // 自主决策
    'MODULE_TYPE_TENDER': cardList[0].dataList.some(item => item.nodeCode === 'MODULE_TYPE_TENDER'), // 盲盒
    'MODULE_TYPE_BID': cardList[0].dataList.some(item => item.nodeCode === 'MODULE_TYPE_BID'), // 开标评标 定标
    'MODULE_TYPE_OBJECTION_COMPLAINT': cardList[0].dataList.some(item => item.nodeCode === 'MODULE_TYPE_OBJECTION_COMPLAINT'), // 异议投诉
    'MODULE_TYPE_CONTRACT_PERFORMANCE': cardList[0].dataList.some(item => item.nodeCode === 'MODULE_TYPE_CONTRACT_PERFORMANCE') // 合同履约
  }
  projectNum.value = cardList[0].dataList[0].value
});
</script>
<style scoped lang="less">
.project-home {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  font-size: 16px;
  .project-card {
    width: 520px;
    height: 800px;
    background: url("/projectManagement/sj-bg.png") no-repeat;
    background-size: 100% 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #3281ff;
    .card-header {
      width: 100px;
      height: 100px;
      margin: 0 auto;
      color: #3281ff;
      margin-top: 25%;
      background: url("/projectManagement/sj-supervise.png") no-repeat;
      background-size: 100% 100%;
    }
    .card-text {
      font-size: 24px;
      font-weight: bold;
    }
    .card-status {
      font-size: 18px;
      font-weight: bold;
      height: 30px;
    }
    .card-list-box {
      margin-top: 10px;
      color: #000;
      .cart-list {
        width: 328px;
        height: 42px;
        line-height: 42px;
        background: url("/projectManagement/text-bg.png") no-repeat;
        background-size: 100% 100%;
        margin-top: 10px;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        cursor: pointer;
        transition: all 0.5s;
        &:hover {
          .cart-list-nav {
            transform: scale(1.1);
          }
        }
        .cart-list-name {
          margin-left: 10px;
          width: 130px;
          text-align-last: justify;
        }
        .cart-list-num {
          font-weight: bold;
        }
        .cart-list-nav {
          display: inline-block;
          width: 30px;
          height: 30px;
          background: #3281ff;
          margin-right: 4px;
          border-radius: 5px;
          text-align: center;
          color: #fff;
          line-height: 34px;
          transition: all 0.1s;
        }
      }
      .cart-list-ts {
        width: 328px;
        height: 42px;
        line-height: 42px;
        background: url("/projectManagement/text-bg.png") no-repeat;
        background-size: 100% 100%;
        margin-top: 10px;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        cursor: pointer;
        transition: all 0.5s;
        &:hover {
            .cart-list-nav-ts {
              transform: scale(1.1);
            }
          }
        .cart-list-title-ts{
          width: 44px;
          height: 100%;
          padding: 4px 15px;
          background: #6ccef7;
          line-height: 17px;
          border-radius: 5px 0px 0px 5px;
          color: #fff;
        }
        .cart-list-num {
          font-weight: bold;
        }
        .cart-list-name-ts {
          margin-left: 10px;
          width: 146px;
          text-align-last: justify;
          .name-color{
            color: #3482fe;
          }
        }
        .cart-list-nav-ts {
          display: inline-block;
          width: 30px;
          height: 30px;
          background: #6ccef7;
          margin-right: 4px;
          border-radius: 5px;
          text-align: center;
          color: #fff;
          line-height: 34px;
          transition: all 0.1s;
        }
      }
      .sh-box{
        border: 1px solid #6ccef7;
        border-radius: 5px;
        display: flex;
        overflow: hidden;
        .sen-title{
          width: 44px;
          height: 210px;
          padding: 48px 15px;
          background: #6ccef7;
          line-height: 62px;
          border-radius: 5px 0px 0px 5px;
          color: #fff;
        }
        .sen-title-first{
          height: 42px;
          padding: 3px 15px;
          background: #6ccef7;
          line-height: 17px;
        }
        .sen-title-last{
          height: 84px;
          padding: 7px 15px;
          background: #6ccef7;
          line-height: 36px;
        }
      }
      .cart-list-ts-top{
         margin-top: 0px;
         background:#fff;
      }
      .cart-width{
        width: 282px;
      }
      .dashed-border {
        background: url("/projectManagement/box-xx.png") no-repeat;
        background-size: 100% 100%;
        margin: 10px 0;
        width: 328px;
        height: 10px;
      }
    }
    .card-tip {
      color: #676767;
      margin-top: 18px;
    }
  }
  .card-list-sj {
    background: url("/projectManagement/sj-bg.png") no-repeat;
    background-size: 100% 100%;
    .card-header {
      background: url("/projectManagement/sj-supervise.png") no-repeat;
      background-size: 100% 100%;
    }
    .card-list-box {
      .cart-list {
        .cart-list-nav {
          background: #3281ff;
        }
      }
    }
  }
  .card-list-sh {
    background: url("/projectManagement/sh-bg.png") no-repeat;
    background-size: 100% 100%;
    color: #6ccef7;
    .card-header {
      background: url("/projectManagement/sh-supervise.png") no-repeat;
      background-size: 100% 100%;
    }
    .card-list-box {
      .cart-list {
        .cart-list-nav {
          background: #6ccef7;
        }
      }
    }
  }
  .card-list-dz {
    background: url("/projectManagement/dz-bg.png") no-repeat;
    background-size: 100% 100%;
    color: #ff6f6f;
    .card-header {
      background: url("/projectManagement/dz-supervise.png") no-repeat;
      background-size: 100% 100%;
    }
    .card-list-box {
      .cart-list {
        .cart-list-nav {
          background: #ff6f6f;
        }
      }
    }
  }
}
</style>
