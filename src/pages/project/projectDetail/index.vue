<template>
    <div id="perject-detail" v-loading="isLoading">
        <div class="project-header">
            <div class="header-box">
                <div class="header-column">
                    <div class="header-text-s  unify-title5" v-if="dataInfo">
                        <div class="header-w-box">
                            <span class="text-t">{{ dataInfo.tenderProjectName ? '招标项目名称' : '标段名称' }}</span>
                            <el-popover placement="bottom"  trigger="hover">
                                <template #reference>
                                    <span class="text-content">{{  dataInfo.tenderProjectName ? dataInfo.tenderProjectName : dataInfo.bidSectionName }}</span>
                                </template>
                                <div style="text-align:center;">
                                    {{ dataInfo.tenderProjectName ? dataInfo.tenderProjectName : dataInfo.bidSectionName }}
                                </div>
                            </el-popover>
                        </div>
                    </div>
                    <div class="header-text-s  unify-title5" v-if="dataInfo">
                        <div class="header-w-box">
                            <span class="text-t">{{ dataInfo.tenderProjectCode ? '招标项目编号' : '标段编号' }}</span>
                            <span class="text-content" style="width: 316px;">{{
                                    dataInfo.tenderProjectCode ? dataInfo.tenderProjectCode : dataInfo.bidSectionCode
                                }}</span>
                        </div>
                    </div>
                </div>
                <div class="header-column">
                    <div class="header-text-s  unify-title5" v-if="dataInfo">
                        <div class="header-w-box">
                            <span class="text-t">招标人</span>
                            <span class="text-content" style="width: 316px;">{{
                                dataInfo.tendererName ? dataInfo.tendererName : dataInfo.tendererName
                                }}</span>
                        </div>
                    </div>
                    <div class="header-text-s  unify-title5" v-if="dataInfo">
                        <div class="header-w-box">
                            <span class="text-t" >招标代理机构</span>
                            <span class="text-content" style="width: 316px;">{{
                                    dataInfo.tenderAgentName ? dataInfo.tenderAgentName : dataInfo.tenderAgentName
                                }}</span>
                        </div>
                    </div>
                </div>
            </div>
            <el-button @click="handleBack" class="back-btn" type="primary" plain round
            >返回
            </el-button
            >
        </div>
        <div class="project-press" v-if="parentNodeCode == 'PRESS'">
            <el-steps
                    class="mb-4"
                    align-center

            >
                <el-step @click="handleSelectStep(item)" v-for="(item,index) in flattenedData" :key="index"
                         :status="item.nodeStepType" :icon="Memo">
                    <template #title>
                        <el-popover placement="bottom" trigger="hover">
                            <template #reference>
                                <span class="step-span" style="cursor: pointer;">{{ item.name }}</span>
                            </template>
                            <div style="text-align: center;">{{ item.name }}</div>
                        </el-popover>
                    </template>
                </el-step>
            </el-steps>
            <!-- <div
              class="simple"
            >
            <div class="step" @click="handleSelectStep(item)" v-for="(item,index) in flattenedData" :key="index"  :class="item.nodeStepType">
              <span class="step-title" >
                <el-popover placement="bottom" trigger="hover">
                    <template #reference>
                    <span class="step-span" style="cursor: pointer;"  >  <img v-if="(item.nodeStepType).indexOf('success') != -1" style="width: 22px; height: 22px; vertical-align: sub;  margin-right: 10px;" src="/projectManagement/success.png" alt="">{{ item.name }}</span>
                    </template>
                    <div style="text-align: center;">{{ item.name }} </div>
                  </el-popover>
              </span>
            </div>
            </div> -->
        </div>
        <div  :class="{projectContentH: parentNodeCode != 'PRESS'}" class="project-content">
            <div class="project-left" v-if="isShowLeftBox && activities.length != 0">
                <div class="left-top" v-if="parentNodeCode != 'PRESS'">
                    <div class="left-header header-title  unify-title7">流 程</div>
                    <div class="project-stop">
                        <div
                                class="stop-item"
                                v-for="(item, index) in processList"
                                :key="index"
                        >
                            <div
                                    class="stop-text"
                                    @click="handleSelectModule(item.key)"
                                    :class="{ 'stop-text-avtive': item.key == selectKey }"
                            >
                                <span class="h-top-bg"> {{ index + 1 }} </span>
                                {{ item.name }}
                            </div>
                            <div
                                    class="step-img"
                                    v-if="processList.length - 1 != index"
                            ></div>
                        </div>
                    </div>
                </div>
                <div class="left-bottom" :class="parentNodeCode == 'PRESS' ? 'left-bottom-height' : '' ">
                    <div class="left-bottom-header header-title unify-title7">核验记录</div>
                    <div class="bottom-list-audit">
                        <el-timeline>
                            <el-timeline-item
                                    v-for="(activity, index) in activities"
                                    :key="index"
                                    :color="activity.color"
                                    :size="activity.size"
                            >
                                <div class="no-status">
                                    <div
                                            class="audit-status"
                                            :style="{ color: getAuditStatusColor(activity.content) }"
                                    >
                                        {{ activity.content }}
                                    </div>
                                    <div class="audit-companyName" style="margin-left: 7px">
                                        {{ activity.companyName }}
                                    </div>
                                    <div class="audit-time" style="margin-left: 7px">
                                        {{ activity.timestamp }}
                                    </div>
                                    <div class="audit-time" style="margin-left: 7px;color:red;"
                                         v-if="activity.content == '【 核验不通过 】'">
                                        原因：{{ activity.handingSuggestion }}
                                    </div>
                                </div>
                            </el-timeline-item>
                        </el-timeline>
                    </div>
                </div>
            </div>
            <div class="project-center">
                <div class="project-audit" ref="mianscroll">
                    <div class="center-text" v-if="isShowPdf">
                        <div class="iframe-box">
                            <div class="company-list" v-if="isShowBtn" :class="!isShowList ? 'company-list-sh' : '' ">
                                <div class="com-btn" @click="isShowList = !isShowList">{{
                                    isShowList ? '收起' : '展开'
                                    }}
                                </div>
                                <div class="company-name" :class="{ companyNameS:item.rowGuid == fileRowGuid }"
                                     @click="hansleModelSign(item)" v-for="(item,index) in moduleList" :key="index">
                                    <div class="qz-sign" v-if="item.signatureStatus === 1"></div>
                                    {{ item.attachmentFileName }}
                                </div>
                            </div>
                            <iframe
                                    id="iframe"
                                    ref="iframe"
                                    width="100%"
                                    height="100%"
                                    frameborder="0"
                                    :src="iframeSrc"
                            ></iframe>
                        </div>
                    </div>
                    <div class="project-detail-info" v-else>
                        <div class="monitor" @click="handleSupervisionDetail"
                             v-if="isShowSupervisionCount !== 0 && isShowSupervisionCount != null">
                            <img class="ionCountImg" :src="isShowSupervisionStatus == 9? ionCount.greenImg:isShowSupervisionStatus == 1?ionCount.redImg: '' " alt=""> <span
                                class="text">电子监察预警 <span>{{ dataInfo?.supervisionCount }}</span> 条</span>
                        </div>
                        <detailModule
                                v-for="(val, key, index) in selectModule"
                                :key="index"
                                :headerTitle="key"
                                :moduleInfo="val"
                        ></detailModule>
                    </div>
                </div>
            </div>
            <div class="project-right" v-if="fileDateList.length != 0">
                <div class="right-header header-title unify-title7">附 件</div>
                <div class="file-content-box">
                    <div
                            class="file-list"
                            v-for="(item, key, index) in fileDateList"
                            :key="index"
                    >
                        <div class="file-header">{{ key }}</div>
                        <div
                                class="file-content"
                                v-for="(val, index1) in fileDateList[key]"
                                :key="index1"

                        >
                            <div class="content-header" :title="val.attachmentName">{{ val.attachmentName }}</div>
                            <div class="file-src"  v-for="(obj, index2) in val.attachmentFileName?.split(',')" :key="index2" @click="handleOpenPdf(val,index2)">
                                <span class="pdf-img"></span> {{ obj }}
                            </div>
                            <div class="file-time"
                                 v-if="detailInfo.nodeCode == 'TENDER_FILE' && val.attachmentName == '招标文件'">
                                <el-button type="primary" class="btn-tolerance" round
                                           @click.prevent="handleViewFileZb(val.rowGuid)">点击查看招标文件
                                </el-button>
                            </div>
                            <div class="file-time"
                                 v-if="detailInfo.nodeCode == 'CLARIFY_UPDATE' && val.attachmentName == '澄清文件'">
                                <el-button type="primary" class="btn-tolerance" round
                                           @click="handleViewFileZb(val.rowGuid)">点击查看澄清文件
                                </el-button>
                            </div>
                            <div class="file-time"
                                 v-if="detailInfo.nodeCode == 'PREQUALIFICATION_FILE' && val.attachmentName == '资格预审文件'">
                                <el-button type="primary" class="btn-tolerance" round
                                           @click="handleViewFileZb(val.rowGuid)">点击查看资格预审文件
                                </el-button>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
        <div class="project-footer">
            <el-button
                    v-if="isShowTolerance && isShowAudit"
                    type="primary"
                    class="btn-tolerance"
                    round
                    @click="handleGetTolerance()"
            >生成容缺受理通知书
            </el-button
            >
            <el-button
                    v-if="isShowSign"
                    :disabled="pdfQzUrl"
                    :type="pdfQzUrl ? 'info' : 'primary'"
                    class="btn-footer"
                    round
                    @click="handleSign()"
            >{{ pdfQzUrl ? "已签章" : "签 章" }}
            </el-button
            >
            <!--      <el-button-->
            <!--          v-if="true"-->
            <!--          :type="'primary'"-->
            <!--          class="btn-footer"-->
            <!--          round-->
            <!--          @click="handleSign()"-->
            <!--      >{{ "签 章" }}</el-button-->
            <!--      >-->
            <el-button
                    v-if="isShowAudit"
                    type="primary"
                    class="btn-footer"
                    color="#8bc34a"
                    style="color: #fff"
                    round
                    @click="handlePass"
            >核验通过
            </el-button
            >
            <el-button
                    v-if="isShowAudit"
                    type="primary"
                    class="btn-footer"
                    color="red"
                    style="color: #fff"
                    round
                    @click="handleNoPass"
            >核验不通过
            </el-button
            >
            <el-button
                    @click="handleBack"
                    class="btn-footer"
                    type="primary"
                    plain
                    round
            >关 闭
            </el-button
            >
        </div>
    </div>
    <el-dialog v-model="dialogFormVisible" width="700px" title="核验意见">
        <el-form ref="ruleFormRef" :model="formData" :rules="rules">
            <el-form-item label="意见" prop="reason">
                <el-input
                        v-model="formData.reason"
                        :rows="6"
                        placeholder="请输入意见！"
                        type="textarea"
                        autocomplete="off"
                />
            </el-form-item>
        </el-form>
        <template #footer>
      <span class="dialog-footer">
        <el-button round @click="dialogFormVisible = false">取消</el-button>
        <el-button round type="primary" @click="handleConNoPass(ruleFormRef)"
        >确 认</el-button
        >
      </span>
        </template>
    </el-dialog>
    <qianzhang ref="qzel" @getPdfInfo="getPdfInfo"></qianzhang>
    <allDialogView ref="allDialogViewEl" :dialogTitle='dialogTitle'>
        <template #content>
            <div style="width: 100%;height:700px;">
                <pdfSign @handleCloseQz="handleCloseQz" :pdfRowGuid="pdfRowGuid" :pathUrl="pdfUrl"></pdfSign>
            </div>
        </template>
    </allDialogView>
    <allDialogView ref="allDialogViewElPdf" :dialogTitle='dialogTitle'>
        <template #content>
            <div class="viewPdfbox" style="">
                <div class="navBox" v-if="zbFileList.length">
                    <el-menu :default-active="defaultActive" class="el-menu-vertical-demo">
                        <el-menu-item v-for="item in zbFileList" :key="item.rowGuid || item.id" :index="item.rowGuid || item.id">
                            <el-icon>
                                <document/>
                            </el-icon>
                            <span class="menuName" @click="handleViewPdf(item)">{{ item.fileName }}</span>
                        </el-menu-item>
                    </el-menu>
                </div>
                <div class="pdfBox">
                    <iframe id="iframe" ref="iframe" width="100%" height="100%" frameborder="0"
                            :src="pdfViewerSrc"></iframe>
                </div>
            </div>
            <span class="dialog-footer">
                <el-button
                        type="primary"
                        round
                        style="width: 100px"
                        @click="handleClosePdfZb()"
                >关闭</el-button
                >
            </span>
        </template>
    </allDialogView>
    <el-drawer custom-class="my-drawer" modal-class="my-drawer-detail" :append-to-body="false" v-model="isShowDrawer"
               v-if="isShowDrawer" size="50%" :show-close="false">
        <template #header>
            <h2></h2>
        </template>
        <detailModule
                v-for="(item, index) in dataInfo?.supervisionList"
                :key="index"
                :headerTitle="item['监察点名称']"
                :moduleInfo="item"
        ></detailModule>
    </el-drawer>
    <el-drawer custom-class="my-drawer" modal-class="my-drawer-detail" :append-to-body="false" v-model="isShowFileDrawer"
               v-if="isShowFileDrawer" size="50%" :show-close="false" v-loading="isLoading">
        <template #header>
            <h2></h2>
        </template>
        <el-descriptions :column="2" :size="'Large'" border v-if="allFileList.length">
            <template #title>
                <div class="descriptions-header unify-title unify-title1">
                   {{ fileHeader }}
                </div>
            </template>
            <el-descriptions-item
                label-class-name="my-descriptions"
                v-for="(val, index) in allFileList"
                :span="2"
                :key="index"
            >
                <template #label>
                <div class="cell-item">
                    {{ val.attachmentName ||  val.name}}
                </div>
                </template>
                <el-text class="my-el-text" type="primary" @click="handleViewFileDetail(val)">查看</el-text>
            </el-descriptions-item>
            
            </el-descriptions>
            <el-empty v-else description="暂无数据" />
    </el-drawer>
    <newQz @signedSuccess="signedSuccess" ref="newQzEl"></newQz>
</template>

<script setup>
import AccountManagementFilter from "@/filters/index.js";
import {useRouter, useRoute} from "vue-router";
import {httpGet, httpPost} from "@wl-fe/http/dist";
import {ionCount} from "@/pages/project/projectList/content.js";
import {ref, computed} from "vue";
import {Memo} from '@element-plus/icons-vue'

const {getAuditStatusColor} = AccountManagementFilter();
const router = useRouter();
const route = useRoute();
let isLoading = ref(false);
const handleBack = () => {
    if (parentNodeCode != 'PRESS') {
        router.push({
            path: "/projectStatisticaData/projectList",
            query: {
                nodeCode: detailInfo.nodeCode,
                parentNodeCode: parentNodeCode,
            },
        });
    } else {
        router.go(-1)
    }

};
const isShowDrawer = ref(false);
const isShowFileDrawer = ref(false);
const fileHeader = ref("");
const allFileList = ref([]);

const isShowList = ref(false);
const isShowBtn = ref(false);
const dialogFormVisible = ref(false);
const ruleFormRef = ref();
const formData = reactive({
    reason: "",
});
const rules = reactive({
    reason: [{required: true, message: "请先输入意见！", trigger: "blur"}],
});
const handleSupervisionDetail = () => {
    isShowDrawer.value = true;
};
const handlePass = () => {
    console.log("核验通过",detailInfo)
    ElMessageBox.alert('确认核验通过？', '', {
        confirmButtonText: '确认',
        callback: (action) => {
            if (action == 'confirm') {
                let info = {
                    status: 2,
                    cruxGuid: detailInfo.rowGuid,
                    handingSuggestion: "核验通过",
                    node: detailInfo.nodeCode,
                    platformCode: detailInfo.platformCode,
                    bidSectionGuid: detailInfo.bidSectionGuid
                };
                if (nodeCode == 'TENDER_RECORD') {
                    info.tolerance = tolerance
                }
                handleVerification(info);
            }
        },
    })
};
const handleNoPass = () => {
    dialogFormVisible.value = true;
};
const handleConNoPass = async (formEL) => {
    if (!formEL) return;
    await formEL.validate((valid, fields) => {
        if (valid) {
            dialogFormVisible.value = false;
            let info = {
                status: 1,
                cruxGuid: detailInfo.rowGuid,
                handingSuggestion: formData.reason,
                node: detailInfo.nodeCode,
                bidSectionGuid: detailInfo.bidSectionGuid,
                platformCode: detailInfo.platformCode
            };
            handleVerification(info)
        }
    });
};
const handleVerification = async (info) => {
    await httpPost("/processNodeApproval/saveProcessNodeApproval", info);
    ElMessage({
        message: "核验完成！",
        type: "success",
    });
    //  handleBack()
    handleGetDetail();
    handleGetAuditInfo();
};

let {query} = toRefs(route);
const {
    tolerance, //招标备案核验用的
    nodeCode,
    bidSectionGuid,
    rowGuid,
    parentNodeCode,
    projectGuid,
    isShowLeft,
    tenderProjectGuid,
    isPress,
    platformCode
} = query.value;

const detailInfo = reactive({
    // 统一详情用的
    nodeCode,
    rowGuid,
    projectGuid,
    bidSectionGuid,
    isPress,
    platformCode
});
let dataInfo = ref(null);
let fileDateList = ref([]);
const processObj = {
    TENDER_RECORD: [
        {
            name: "项目登记",
            key: "constructionProjectInfo",
        },
        {
            name: "招标项目",
            key: "tenderProjectInfo",
        },
        {
            name: "招标委托",
            key: "tenderTrustAgreementInfo",
        },
        {
            name: "评标组织形式",
            key: "expertNeedInfo",
        },
        {
            name: "招标备案",
            key: "tenderRecordInfo",
        },
    ], // 招标备案
    WINING_ADVICE: [

        {
            name: "中标候选人公示",
            key: "winCandidatePublicityInfo",
        },
        {
            name: "中标结果公示",
            key: "winBidderPublicityInfo",
        },
        {
            name: "中标通知书",
            key: "bidWinningNoticeInfo",
        },
        {
            name: "中标通知书（附件）",
            key: "bidWinningNoticeFileInfo",
        },
    ], // 中标通知书
    WRITTEN_RECORD: [
        {
            name: "书面报告备案",
            key: "publicDataInfo",
        },
    ], // 书面报告备案
    C_BID_EXCEPTION_REPORT: [
        {
            name: "特殊事项",
            key: "publicDataInfo",
        },
    ], // 特殊事项
    C_TENDER_ABNORMITY_REPORT: [
        {
            name: "招标容缺补件",
            key: "publicDataInfo",
        },
    ], // 招标容缺补件
    DC_BID_SECTION: [

        {
            name: "直接发包项目登记",
            key: "dcConstructionProjectInfo",
        },
        {
            name: "直接发包备案",
            key: "dcbidSectionInfo",
        },
        {
            name: "直接发包告知书（附件）",
            key: "dcWinningNoticeFileInfo",
        },
    ], // 直接发包
     DC_ILLEG_FILING_SECTION: [

        {
            name: "未依法发包项目登记",
            key: "dcConstructionProjectInfo",
        },
        {
            name: "未依法发包备案",
            key: "dcbidSectionInfo",
        },
        {
            name: "未依法发包告知书（附件）",
            key: "dcWinningNoticeFileInfo",
        },
    ], // 未依法发包
    ES_EXPERT_PROJECT: [

        {
            name: "评标组织形式",
            key: "publicDataInfo",
        }
    ], // 专家抽取需求
};
let processList = ref(processObj[detailInfo.nodeCode]);
// https://oocr.lnwlzb.com/ocr/api/ocr/getFile/2f686f6d652f696d672f4176617461722f66696c6575706c6f61642f7a746b2f3231303230344130313031303031323430373039313433323331302f7a746b2f7a746b3132343739313433323337363336332e706466247a746b3132343739313433323337363336332e7064662431373330353036373233373631
const fileUrl = ref("");
const iframeSrc = computed(() => {
    console.log('所有环境变量:', import.meta.env);
    console.log('PDF_WEB_VIEW值:', `${import.meta.env.VITE_PDF_WEB_VIEW}${fileUrl.value}`);
    return `${import.meta.env.VITE_PDF_WEB_VIEW}${fileUrl.value}`;
});
let selectKey = ref("");
1
let selectModule = ref({});
const mianscroll = ref(null);
onMounted(async () => {
    handleGetDetail();
    if (isShowLeftBox.value) {
        handleGetAuditInfo();
    }
    if (parentNodeCode == 'PRESS' && nodeCode != 'PROJECT_REGISTE') {
        handleGetProjectFlowchart()
    }
});
const flattenedData = ref([]);
const handleGetProjectFlowchart = async () => {
    let info = {tenderProjectGuid, bidSectionGuid};
    const data = await httpPost("/api/projectManageHone/projectFlowchart", info);
    flattenedData.value = flattenTree(data);
    flattenedData.value.forEach((item, index) => {
        if (item.currentNodeCode == nodeCode) {
            item.nodeStepType = "success"
            // if(index == 0){
            //     item.nodeStepType = 'success-first'
            //   }
            //   if(index == flattenedData.value.length - 1){
            //     item.nodeStepType = 'success-last'
            //   }
        } else {
            item.nodeStepType = "finish"
            // if(index == 0){
            //     item.nodeStepType = 'finish-first'
            //   }
            //   if(index == flattenedData.value.length - 1){
            //     item.nodeStepType = 'finish-last'
            //   }
        }
    })
};
const flattenTree = (tree) => {
    let flatData = [];

    function traverse(node) {
        // 复制当前节点，避免修改原始数据
        const newNode = {...node};
        // 删除 children 属性
        delete newNode.children;
        flatData.push(newNode);
        if (node.children && node.children.length > 0) {
            node.children.forEach(child => traverse(child));
        }
    }

    tree.forEach(node => traverse(node));
    return flatData;
}
const handleSelectStep = (val) => {
    detailInfo.nodeCode = val.currentNodeCode;
    detailInfo.rowGuid = val.currentNodeGuid;
    query.value.nodeCode = val.currentNodeCode;
    query.value.rowGuid = val.currentNodeGuid;
    console.log(' query.value query.value', query.value);

    flattenedData.value.forEach((item, index) => {
        if (item.currentNodeCode == val.currentNodeCode) {
            item.nodeStepType = "success"
            // if(index == 0){
            //     item.nodeStepType = 'success-first'
            //   }
            //   if(index == flattenedData.value.length - 1){
            //     item.nodeStepType = 'success-last'
            //   }
        } else {
            item.nodeStepType = "finish"
            // if(index == 0){
            //     item.nodeStepType = 'finish-first'
            //   }
            //   if(index == flattenedData.value.length - 1){
            //     item.nodeStepType = 'finish-last'
            //   }
        }
    })
    handleGetDetail()
    handleGetAuditInfo()
}
const handleGetDetail = async () => {
    isShowPdf.value = false
    isLoading.value = true;
    let data = await httpPost(handleApi(), detailInfo);
    isLoading.value = false;
    fileDateList.value = data.fileData;
    dataInfo.value = data;
    if (nodeCode == 'AS_AGENCY_LOCKOUT') {
        selectModule.value = dataInfo.value['publicDataInfo'];
        if (selectModule.value.hasOwnProperty("核验附件")) {
            isShowPdf.value = true;
            fileUrl.value = selectModule.value['核验附件'][0].url;
        }
        return
    }
    if (parentNodeCode != 'PRESS') {
        handleSelectModule(processList.value[processList.value.length - 1].key);
    } else {
        selectModule.value = dataInfo.value['publicDataInfo'];
        if (selectModule.value.hasOwnProperty("核验附件")) {
            isShowPdf.value = true;
            fileUrl.value = selectModule.value['核验附件'][0].url;
        }
    }
    if(detailInfo.nodeCode == 'WRITTEN_RECORD'){
        // 书面报告
      dataInfo.value.publicDataInfo['档案信息'] = {
        '招标投标过程电子资料': getViewLink('processData'),
        '开标评标报告附件': getViewLink('reportAttachment'),
        '招标文件': getViewLink('tenderFile'),
        '投标文件': getViewLink('bidFile'),
        // '履约合同（待定）': getViewLink('contract')
      }
    }
};
// 添加这个方法
const getViewLink = (type) => {
    return `<span class="view-link el-text el-text--primary" style="cursor: pointer;" data-type="${type}">查看</span>`;
};
const clickHandler = (e) => {
    if(e.target.classList.contains('view-link')) {
        const type = e.target.dataset.type;
        const fileObj = {
           processData:1, 
           reportAttachment:2, 
           tenderFile:3, 
           bidFile:4, 
           contract:5, 
        }
        const fileHeaderObj = {
           processData:'招标投标过程电子资料', 
           reportAttachment:'开标评标报告附件', 
           tenderFile:'招标文件', 
           bidFile:'投标文件', 
           contract:'履约合同（待定）', 
        }
        // 根据type处理不同的查看逻辑
        console.log('查看:', type);
        fileHeader.value = fileHeaderObj[type];
        handleGetElectronicArchivesList(fileObj[type]);
    }
};

document.addEventListener('click', clickHandler);

onBeforeUnmount(() => {
    document.removeEventListener('click', clickHandler);
});
const handleGetElectronicArchivesList = async (type) => {
    let info = {
        rowGuid: detailInfo.rowGuid,
        bidSectionGuid: detailInfo.bidSectionGuid,
        fileType: type,
    }
    isLoading.value = true;
    allFileList.value = [];
    const data = await httpPost('/api/projectManageHone/getElectronicArchivesList', info)
        .catch(err => {
            isLoading.value = false
            return
        });
    isLoading.value = false    
    if((type == 1 || type == 2) && data.length > 0){
       handleViewFileDetail(data[0])
       return
    }
    allFileList.value = data
   
    isShowFileDrawer.value = true;
    console.log(data);
}
const handleViewFileDetail = (val) => {
    if(fileHeader.value == '投标文件' || fileHeader.value == '招标投标过程电子资料' ||fileHeader.value == '开标评标报告附件'){
        zbFileList.value = val.fileList ? val.fileList : []
        zbPdfFileUrl.value = val.fileList ? val.fileList[0].fileUrl :  val.url
        defaultActive.value = val.fileList ? val.fileList[0].id : ''
        allDialogViewElPdf.value.showDialog = true
        dialogTitle.value = val.name ? `投标文件（${val.name}）` : fileHeader.value
        return
    }
    if( val.attachmentName  == '招标文件' || val.attachmentName.includes( '澄清文件' )  ){
        handleViewFileZb(val.rowGuid)
    } else {
        window.open(val.url, '_blank')
    }
}
const nodeCodeList = ['TENDER_RECORD', 'WINING_ADVICE', 'WRITTEN_RECORD', 'C_BID_EXCEPTION_REPORT', 'DC_BID_SECTION', 'ES_EXPERT_PROJECT','DC_BID_SECTION','DC_ILLEG_FILING_SECTION']
const detailApiList = {
    ES_EXPERT_PROJECT: "/inviteTender/esExpertProject/details",
    TENDER_RECORD: "/inviteTender/tenderRecord/details",
    WINING_ADVICE: "/inviteTender/bidWinningNotice/details",
    DC_BID_SECTION: "/inviteTender/dcBidSection/details",
    DC_ILLEG_FILING_SECTION: '/illegal/project/details',
    WRITTEN_RECORD: "/inviteTender/tenderProcessNode/details",
    C_BID_EXCEPTION_REPORT: "/inviteTender/tenderProcessNode/details",
}
const handleApi = () => {
    let apiUrl = ''
    if (nodeCodeList.includes(detailInfo.nodeCode) && parentNodeCode != 'PRESS') {
        apiUrl = detailApiList[detailInfo.nodeCode]
    } else if(detailInfo.nodeCode == "C_BID_EXCEPTION_REPORT") {
        apiUrl = '/inviteTender/bidExceptionReport/getBidExceptionReportDetail'
    }else {
        apiUrl = '/inviteTender/tenderProcessNode/details'
    }
    return apiUrl
}
const isShowLeftBox = computed(() => {
    return isShowLeft == 'true'
});
const isShowTolerance = computed(() => {
    return nodeCode == 'TENDER_RECORD' && tolerance == 1
});
const isShowSign = computed(() => {
    return (
        dataInfo.value?.buttonDataInfo?.signButton == 1 &&
        dataInfo.value?.buttonDataInfo?.approvalButton == 1
    );
});
const isShowAudit = computed(() => {
    return dataInfo.value?.buttonDataInfo?.approvalButton == 1;
});
const isShowSupervisionCount = computed(() => {
    return dataInfo.value?.supervisionCount
});
const isShowSupervisionStatus = computed(() => {
    return dataInfo.value?.supervisionStatus
});
const activities = ref([]);
const handleGetAuditInfo = async () => {
    let info = {
        cruxGuid: detailInfo.rowGuid,
        node: detailInfo.nodeCode,
    };
    let data = await httpPost(
        "/processNodeApproval/getProcessNodeApprovalByGuid",
        info
    );
    let list = data.map((item) => {
        return {
            content: `【 ${item.approvalName} 】`,
            timestamp: item.operateTime,
            size: "large",
            color: item.approvalName == "待核验" ? "#FF9933" : item.approvalName == "核验不通过" ? "#e40d0d" :item.approvalName == "流程退回" ?  "#e40d0d" :"#0bbd87",
            companyName: item.operateUser,
            handingSuggestion: item.handingSuggestion
        };
    });
    activities.value = list;
    console.log("data", data);
};
const isShowPdf = ref(false);
const moduleList = ref([]);
const handleSelectModule = (key) => {
    selectKey.value = key;
    console.log(dataInfo.value);
    selectModule.value = dataInfo.value[key];
    console.log("selectModule", selectModule.value);
    isShowBtn.value = false;
    if (key == "tenderRecordInfo") {
        isShowPdf.value = true;
        fileUrl.value =
            selectModule.value['核验附件'].url;
        fileRowGuid.value = selectModule.value['核验附件'].rowGuid;
    } else if (key == "bidWinningNoticeFileInfo" || key == "dcWinningNoticeFileInfo") {
        isShowPdf.value = true;
        if (Array.isArray(selectModule.value['核验附件'])) {
            fileUrl.value =
                selectModule.value['核验附件'][0].url;
            fileRowGuid.value = selectModule.value['核验附件'][0].rowGuid;
            console.log("selectModule.value['核验附件']", selectModule.value['核验附件']);
            moduleList.value = selectModule.value['核验附件'];
            isShowList.value = true;
            isShowBtn.value = true;
        } else {
            fileUrl.value =
                selectModule.value['核验附件'].url;
        }

    } else {
        isShowPdf.value = false;
    }
    bottomScrollClick();
};
const fileRowGuid = ref('');
const hansleModelSign = (item) => {
    fileUrl.value = item.url
    fileRowGuid.value = item.rowGuid
}
const bottomScrollClick = async () => {
    await nextTick();
    mianscroll.value.scrollTop = 0;
};
const handleOpenPdf = (val,index) => {
    window.open(val.url.split(',')[index]);
};
const qzel = ref(null);
const newQzEl = ref(null);
const handleSign = () => {
    document.body.style.zoom = 1;
    if (!((selectKey.value == "bidWinningNoticeFileInfo" || selectKey.value == "dcWinningNoticeFileInfo") && Array.isArray(selectModule.value['核验附件']))) {
        handleSelectModule(processList.value[processList.value.length - 1].key);
    }
    console.log('import.meta.env.VITE_PROXY_QZ', import.meta.env.VITE_PROXY_QZ);
    if (import.meta.env.VITE_PROXY_QZ == 'QZ') {
        let info = {
            id: new Date().getTime(), //fileRowGuid.value,
            fileUrl: fileUrl.value
        }
        newQzEl.value.open(info);
    } else {
        let urlEncode = window.btoa(`${window.location.origin}/`);
        let encode = window.btoa(fileUrl.value);
        let pdfUrl = `https://qzpublic.lnwlzb.com/signature/H5QZ.htm?fileURL=${encode}&back=${urlEncode}`;
        qzel.value.handleGetPdfInfo(pdfUrl);
    }

};
const dialogPdfTitle = ref('');
const allDialogViewElPdf = ref()
let zbPdfFileUrl = ref('')
let zbFileList = ref([])
let defaultActive = ref('')

const handleViewFileZb = async (rowGuid) => {
    isLoading.value = true;
    let data = await httpPost("/inviteTender/businessBidFiles/selectTenderFiles", {...detailInfo, rowGuid});
    if (data.length === 0) {
        await httpPost("/inviteTender/businessBidFiles/analysisFiles", {...detailInfo, rowGuid});
        setTimeout(() => {
            handleViewFileZb(rowGuid)
        }, 5000);
    } else {
        isLoading.value = false;
        console.log('处理数据');
        zbFileList.value = data
        zbPdfFileUrl.value = data[0].fileUrl
        defaultActive.value = data[0].rowGuid
        allDialogViewElPdf.value.showDialog = true
        dialogTitle.value = '招标文件'
    }
    console.log(data);
}
const handleViewPdf = (item) => {
    zbPdfFileUrl.value = item.fileUrl
}
const handleClosePdfZb = () => {
    allDialogViewElPdf.value.showDialog = false
}
const selectTenderFiles = async () => {
    let data = await httpPost("/inviteTender/businessBidFiles/selectTenderFiles", detailInfo);
    console.log(data);
}
const signedSuccess = async (signInfo) => {
    console.log("signInfo", signInfo, JSON.parse(signInfo.outResp));
    newQzEl.value.handleClose();
    try {
        let res = JSON.parse(signInfo.outResp)
        console.log('resresres111111111', res);
        fileUrl.value = res.msg;
        pdfQzUrl.value = res.msg;
        console.log("selectModule", selectModule);
        let info = {
            rowGuid: '', // selectModule.value["核验附件"].rowGuid,
            signatureUrl: pdfQzUrl.value,
        };
        if (selectKey.value == "tenderRecordInfo") {
            info.rowGuid = selectModule.value["核验附件"].rowGuid;
        } else if (selectKey.value == "bidWinningNoticeFileInfo") {
            if (Array.isArray(selectModule.value['核验附件'])) {
                info.rowGuid = fileRowGuid.value
                selectModule.value['核验附件'].forEach(ele => {
                    if (fileRowGuid.value == ele.rowGuid) {
                        ele.url = pdfQzUrl.value
                        ele.signatureStatus = 1
                    }
                });
                moduleList.value = selectModule.value['核验附件'];
                // logsole.log(moduleList.value);

            } else {
                info.rowGuid = selectModule.value["核验附件"].rowGuid;
            }
        } else if (selectKey.value == "dcWinningNoticeFileInfo") {
            if (Array.isArray(selectModule.value['核验附件'])) {
                info.rowGuid = fileRowGuid.value
                selectModule.value['核验附件'].forEach(ele => {
                    if (fileRowGuid.value == ele.rowGuid) {
                        ele.url = pdfQzUrl.value
                        ele.signatureStatus = 1
                    }
                });
                moduleList.value = selectModule.value['核验附件'];
                // logsole.log(moduleList.value);

            } else {
                info.rowGuid = selectModule.value["核验附件"].rowGuid;
            }
        }
        await httpPost("api/sysAttach/updateSignatureFile", info);
        ElMessage({
            message: "签章成功！",
            type: "success",
        });
    } catch (error) {
        console.log('errorerror', error);

        ElMessage({
            message: "签章失败！",
            type: "error",
        });
    }
}
const pdfQzUrl = ref(false);
const getPdfInfo = async (pdfInfo) => {
    fileUrl.value = pdfInfo.fj_url;
    pdfQzUrl.value = pdfInfo.fj_url;
    console.log("selectModule", selectModule);
    let info = {
        rowGuid: '', // selectModule.value["核验附件"].rowGuid,
        signatureUrl: pdfQzUrl.value,
    };
    if (selectKey.value == "tenderRecordInfo") {
        info.rowGuid = selectModule.value["核验附件"].rowGuid;
    } else if (selectKey.value == "bidWinningNoticeFileInfo") {
        if (Array.isArray(selectModule.value['核验附件'])) {
            info.rowGuid = fileRowGuid.value
            selectModule.value['核验附件'].forEach(ele => {
                if (fileRowGuid.value == ele.rowGuid) {
                    ele.url = pdfQzUrl.value
                    ele.signatureStatus = 1
                }
            });
            moduleList.value = selectModule.value['核验附件'];
            // logsole.log(moduleList.value);

        } else {
            info.rowGuid = selectModule.value["核验附件"].rowGuid;
        }
    } else if (selectKey.value == "dcWinningNoticeFileInfo") {
        if (Array.isArray(selectModule.value['核验附件'])) {
            info.rowGuid = fileRowGuid.value
            selectModule.value['核验附件'].forEach(ele => {
                if (fileRowGuid.value == ele.rowGuid) {
                    ele.url = pdfQzUrl.value
                    ele.signatureStatus = 1
                }
            });
            moduleList.value = selectModule.value['核验附件'];
            // logsole.log(moduleList.value);

        } else {
            info.rowGuid = selectModule.value["核验附件"].rowGuid;
        }
    }
    await httpPost("api/sysAttach/updateSignatureFile", info);
    ElMessage({
        message: "签章成功！",
        type: "success",
    });
};

const allDialogViewEl = ref()
const dialogTitle = ref('');
let pdfUrl = ref('');
let pdfRowGuid = ref('');
const handleGetTolerance = async () => {
    dialogTitle.value = "容缺受理通知书"
    let info = {
        rowGuid: rowGuid,
        templateType: 'TENDER_ABNORMITY_REPORT',
    }
    let data = await httpPost('/inviteTender/tenderProcessNode/createTemplate', info)
    pdfUrl.value = data.data.url
    pdfRowGuid.value = data.data.rowGuid
    console.log('pdfUrlpdfUrl', pdfUrl.value);

    allDialogViewEl.value.showDialog = !allDialogViewEl.value.showDialog
}
const handleCloseQz = () => {
    allDialogViewEl.value.showDialog = !allDialogViewEl.value.showDialog
}
const pdfViewerSrc = computed(() => {
    console.log('所有环境变量:', import.meta.env);
    console.log('PDF_WEB_VIEW值:', import.meta.env.VITE_PDF_WEB_VIEW);
    return `${import.meta.env.VITE_PDF_WEB_VIEW}${zbPdfFileUrl.value}`;
});
</script>
<style scoped lang="less">
#perject-detail {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  font-size: 16px;
  .project-header {
    width: 100%;
    height: 10%;
    background-color: #fff;
    border-radius: 5px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    font-weight: bold;
    font-size: 16px;

    .header-box {
      display: flex;
      flex-direction: row;
      align-items: center;
      width: 85%;
      justify-content: space-between;
      .unify-title5:after {
        top: 50%;
        transform: translateY(-50%);
      }
      .unify-title5:before {
        top: 46%;
        transform: translateY(-50%);
      }
      .header-column {
          display: flex;
          flex-direction: column;
          margin-left: 12px;
      }
      .header-lan {
        height: 30px;
        width: 8px;
        background: #438bfa;
        border-radius: 5px;
        margin-left: 21px;
      }

      .header-text-s {
        display: flex;
        flex-direction: row;
        align-items: center;
        position: relative;
        height: 40px;
        font-size: 18px;
        .header-w-box{
            display: flex;
            align-items: center;
        }
        .text-t {
          margin-right: 10px;
          display: inline-block;
          width: 128px;
        }
        .text-content {
          display: inline-block;
          width: 750px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }

    .back-btn {
      width: 90px;
      margin-right: 20px;
    }
  }

  .project-press {
    width: 100%;
    height: 65px;
    background: #fff;
    border-radius: 5px;
    margin: 5px 0px;

    .simple {
      display: flex;
      height: 45px;
      align-items: center;
      width: 100%;

      .step {
        display: inline-block;
        background: red;
        margin: 0 5px;
        height: 35px;
        line-height: 35px;
        text-align: center;
        width: 100%;
        cursor: pointer;

        .step-title {
          // cursor: pointer;
          font-size: 18px;

          display: block;
        }
      }

      .success {
        background: url("/projectManagement/step2.png") no-repeat center;
        background-size: 100% 100%;
        color: #fff;
        font-weight: bold;
      }

      .success-first {
        background: url("/projectManagement/step1.png") no-repeat center;
        background-size: 100% 100%;
        color: #fff;
        font-weight: bold;
      }

      .success-last {
        background: url("/projectManagement/step3.png") no-repeat center;
        background-size: 100% 100%;
        color: #fff;
        font-weight: bold;
      }

      .finish {
        background: url("/projectManagement/step8.png") no-repeat center;
        background-size: 100% 100%;
      }

      .finish-first {
        background: url("/projectManagement/step7.png") no-repeat center;
        background-size: 100% 100%;
      }

      .finish-last {
        background: url("/projectManagement/step9.png") no-repeat center;
        background-size: 100% 100%;
      }
    }

    .step-span {
      cursor: pointer;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      width: 100%;
      display: inline-block;
      vertical-align: bottom;
    }

    :deep(.el-steps--simple) {
      background: var(--el-color-primary-light-9);
      border-radius: 4px;
      padding: 13px 2%;
    }

    :deep(.el-step__title) {
      line-height: 20px;
    }

    :deep(.el-steps) {
      padding-top: 14px;
    }

    :deep(.el-step.is-simple .el-step__arrow:after, .el-step.is-simple .el-step__arrow:before) {
      background: var(--el-color-primary-light-3);
      content: "";
      display: inline-block;
      height: 15px;
      position: absolute;
      width: 3px;
    }

    :deep(.el-step.is-simple .el-step__arrow:before) {
      background: var(--el-color-primary-light-3);
      content: "";
      display: inline-block;
      height: 15px;
      position: absolute;
      width: 3px;
    }

    :deep(.is-success .el-icon:before) {
      // background: var(--el-color-primary-light-3);
      content: "";
      // display: inline-block;
      // height: 15px;
      // position: absolute;
      // width: 3px;
      position: absolute;
      top: -11px;
      left: 7px;
      border-left: 5px solid transparent;
      border-right: 5px solid transparent;
      border-top: 10px solid #67c23a;
    }
  }

  .project-content {
    width: 100%;
    height: 70%;
    border-radius: 5px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;

    .project-left {
      width: 15%;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      margin-right: 12px;

      .left-top,
      .left-bottom {
        width: 100%;
        height: 49%;
        background: #fff;
        border-radius: 5px;
        box-sizing: border-box;
        padding: 13px 0px;

        .left-bottom-header {
          font-size: 16px;
          font-weight: bold;
          margin-bottom: 10px;
        }

        .bottom-list-audit {
          width: 100%;
          height: 90%;
          padding-top: 5px;
          overflow: auto;

          :deep(.el-timeline-item__tail) {
            border-left: 2px solid #ccc;
          }

          .no-status {
            cursor: pointer;
          }

          .audit-status {
            color: #3981f8;
          }

          .audit-companyName {
          }

          .audit-time {
          }

          :deep(.el-timeline) {
            margin-left: -19px;
          }
        }

        .left-header {
          font-size: 16px;
          font-weight: bold;
        }

        .project-stop {
          width: 100%;
          margin-top: 15px;

          .stop-item {
            display: flex;
            flex-direction: column;
            align-items: center;

            .stop-text {
              width: 200px;
              height: 30px;
              text-align: center;
              line-height: 28px;
              border: 1px solid #3981f8;
              color: #3981f8;
              border-radius: 5px;
              cursor: pointer;
              background: #ebf2fe;
              border-radius: 20px;
              position: relative;

              .h-top-bg {
                width: 30px;
                height: 20px;
                position: absolute;
                top: -9px;
                left: 20px;
                background: url("/projectManagement/h-tip.png") no-repeat;
                background-size: 100% 100%;
                line-height: 20px;
                text-align: left;
                padding-left: 6px;
                color: #fff;
                font-weight: bold;
              }
            }

            .stop-text-avtive {
              background: #3981f8;
              color: #fff;
            }

            .step-img {
              width: 30px;
              height: 30px;
              background: url("/projectManagement/step-down.png") no-repeat;
              background-size: 100% 100%;
            }
          }
        }
      }

      .left-bottom-height {
        height: 100%;
      }
    }

    .project-center {
      // width: 61%;
      width: 0;
      height: 100%;
      background: #fff;
      border-radius: 5px;
      overflow: hidden;
      flex: 1;

      .project-audit {
        width: 100%;
        height: 99%;
        overflow: auto;
        // .center-header {
        //   width: 100%;
        //   height: 6%;
        //   display: flex;
        //   flex-direction: row;
        //   justify-content: flex-end;
        //   align-items: center;
        // }
        .center-text {
          width: auto;
          height: 100%;
          // border: 1px solid #ccc;
          .iframe-box {
            width: 100%;
            height: 99%;
            position: relative;

            .company-list {
              position: absolute;
              top: 39px;
              left: 0;
              width: 300px;
              height: 95%;
              background: #00000038;
              box-sizing: border-box;
              padding: 10px;
              border-radius: 0px 5px 5px 0px;
              display: flex;
              flex-direction: column;
              justify-content: center;
              transition: all 0.5s;

              .company-name {
                width: 100%;
                height: 30px;
                line-height: 30px;
                text-align: center;
                color: #fff;
                border: 1px solid #fff;
                border-radius: 5px;
                padding: 0 25px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                margin: 5px;
                cursor: pointer;
                position: relative;

                .qz-sign {
                  position: absolute;
                  top: 4px;
                  left: 4px;
                  width: 20px;
                  height: 20px;
                  background: url("/projectManagement/yqz.png") no-repeat;
                  background-size: 100% 100%;
                }
              }

              .companyNameS {
                color: #3366ff;
                border: 1px solid #3366ff;
              }

              .com-btn {
                position: absolute;
                right: -24px;
                top: 43%;
                height: 84px;
                width: 22px;
                background: #00000038;
                color: #fff;
                text-align: center;
                line-height: 41px;
                border-radius: 0 5px 5px 0px;
                cursor: pointer;
              }
            }

            .company-list-sh {
              left: -300px;
            }
          }
        }

        .project-detail-info {
          width: 100%;
          height: 100%;
          box-sizing: border-box;
          padding: 10px 5px;
          position: relative;

          :deep(.my-descriptions) {
            // background: #dceaff;
            font-size: 16px;
            width: 22%;
            color: #000;
            text-align: center;
            background: #f6faff;
          }

          .monitor {
            position: absolute;
            right: 20px;
            top: 21px;
            font-size: 18px;
            cursor: pointer;
            z-index: 10;

            .ionCountImg {
              width: 30px;
              margin-right: 5px;
              vertical-align: bottom;
            }

            .text {
              color: red;
              cursor: pointer;

              span {
                font-size: 25px;
                font-weight: bold;
              }
            }
          }
        }
      }
    }

    .project-right {
      width: 15%;
      height: 100%;
      background: #fff;
      border-radius: 5px;
      box-sizing: border-box;
      padding: 13px 0px;

      margin-left: 12px;

      .right-header {
        font-size: 16px;
        font-weight: bold;
        height: 5%;
      }

      .file-content-box {
        height: 95%;
        overflow: auto;
        padding-bottom: 5px;
        box-sizing: border-box;

        .file-list {
          width: 90%;
          margin: 0 auto;
          overflow: hidden;
          margin-top: 22px;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);

          .file-header {
            width: 100%;
            height: 38%;
            line-height: 38px;
            text-align: center;
            background: #dceaff;
          }

          .file-content {
            width: 100%;
            box-sizing: border-box;
            margin-bottom: 20px;

            .content-header {
              white-space: nowrap;
              text-overflow: ellipsis;
              overflow: hidden;
              height: 28px;
              line-height: 28px;
              padding: 0 10px;
              cursor: pointer;
              background: #f6faff;
              color: #3981f8;

              &:hover {
                .file-src {
                  color: #438bfa;
                }
              }
            }

            .file-src {
              // height: 28px;
              line-height: 36px;
              // background: #f6faff;
              padding: 0 10px;
              cursor: pointer;
              font-size: 14px;
              width: 100%;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;

              &:hover {
                color: #438bfa;
              }

              .pdf-img {
                width: 28px;
                height: 32px;
                display: inline-block;
                vertical-align: sub;
                background: url("/projectManagement/PDF.png") no-repeat;
                background-size: 100% 100%;
                margin-right: 4px;
                vertical-align: middle;
              }
            }

            .file-time {
              text-align: center;
              margin-top: 10px;
            }
          }
        }
      }

    }
  }
  .projectContentH{
    height: 79%;
  }

  .project-footer {
    width: 100%;
    height: 6%;
    background: #fff;
    border-radius: 5px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    margin: 5px 0px;

    .btn-footer {
      width: 120px;
    }

    .btn-tolerance {
      width: 150px;
    }
  }

  .header-title-bg {
    display: inline-block;
    height: 30px;
    width: 8px;
    background: #2196f3;
    border-radius: 5px;
    vertical-align: middle;
    margin: 0 5px 0 3px;
  }
}

/* 设置滚动条的样式 */
::-webkit-scrollbar {
  width: 6px;
  box-sizing: border-box;
}

/* 滚动条滑块 */
::-webkit-scrollbar-thumb {
  height: 5px;
  border-radius: 5px;
  background-color: #c2cede;
}

/*滚动槽*/
::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0);
  border-radius: 10px;
  background: #ffffff;
}

/* 滚动条滑块 鼠标悬浮 */
::-webkit-scrollbar-thumb:hover {
  background-color: #c2cede;
  // border: 1px solid rgba(0, 0, 0, 0.21);
  cursor: pointer;
}

.header-title {
  height: 32px;
  font-weight: 600;
  line-height: 32px;
  margin-left: 5px;
  padding-left: 5px;
  position: relative;
  color: #000;
  z-index: 0;
}

.unify-title7 {
  &:after {
    content: "";
    position: absolute;
    bottom: 0;
    top: 0;
    left: 0;
    width: 180px;
    height: 100%;
    opacity: 0.3;
    // background: linear-gradient(to right, #2d83fa, #4bf15900);
  }

  &:before {
    content: " ";
    position: absolute;
    bottom: 0;
    left: 0;
    position: absolute;
    height: 2px;
    width: 100%;
    background: linear-gradient(to right, #2d83fa, rgba(255, 255, 255, 0));
  }
}

:deep(.el-drawer__header) {
  display: none;
}

.dialog-footer {
  display: flex;
  justify-content: center;
  margin-top: 13px;
}

.viewPdfbox {
  width: 100%;
  height: 700px;
  display: flex;

  .navBox {
    width: 15%;
    height: 100%;
    border-right: 1px solid #d1eedb;
    margin-right: 5px;
    overflow: auto;
    .menuName{
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        width: 200px; /* 设置元素宽度 */
    }
  }

  .pdfBox {
    width: 85%;
    height: 100%;
    flex: 1;
  }
}
.my-el-text{
    cursor: pointer;
}
</style>
<style scoped>
#toolbarViewer {
    background: red !important;
}
</style>
