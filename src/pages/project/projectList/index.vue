<template>
    <PageWrapper>
        <div class="project-list" v-loading="isLoading">
            <div class="project-search-box">
                <searchButton style="width: auto" @search="handleSearch" :placeholder="searchPlaceholder">
                    <el-select class="select" clearable v-model="searchInfo.platformCode" @change="handleGetList"
                               v-if="isShowAddInsRECORD && isShowAfterProcess"
                               placeholder="交易平台筛选" size="large">
                        <el-option v-for="item in platformOption" :key="item.value" :label="item.label"
                                   :value="item.value"/>
                    </el-select>
                    <el-select class="select" clearable v-model="searchInfo.currentNodeCode"
                               v-if="isShowAddInsRECORD && isShowAfterProcess" @change="handleGetList"
                               placeholder="交易环节筛选" size="large">
                        <el-option v-for="item in pressOption" :key="item.value" :label="item.label"
                                   :value="item.value"/>
                    </el-select>
                    <el-date-picker
                            v-if="isShowWiningAdvice"
                            class="date-picker"
                            v-model="searchInfo.docGetStartTime"
                            type="datetime"
                            placeholder="选择开始时间"
                            value-format="YYYY-MM-DD HH:mm:ss"
                            :style="{ 
                                '--el-border-radius-base': '50px',
                                'height': '40px',
                                'width': '200px',
                            }"
                            :picker-options="startTimePickerOptions"
                    />
                    <span style="margin: 0 8px" v-if="isShowWiningAdvice">至</span>
                    <el-date-picker
                            v-if="isShowWiningAdvice"
                            v-model="searchInfo.docGetEndTime"
                            type="datetime"
                            placeholder="选择结束时间"
                            value-format="YYYY-MM-DD HH:mm:ss"
                            class="date-picker"
                            :style="{ 
                                '--el-border-radius-base': '50px',
                                'height': '40px',
                                'width': '200px',
                                'margin-right':'15px'
                            }"
                            :picker-options="endTimePickerOptions"
                    />
                </searchButton>
                <div>
                    <el-button
                            @click="handleInitiate()"
                            class="success-btn"
                            type="success"
                            round
                            v-if="isShowAddIns"
                    >新增指令
                    </el-button
                    >
                    <el-button
                            @click="handleAddBadbehavior()"
                            class="success-btn"
                            type="success"
                            round
                            v-if="isShowBadBehavior"
                    >新增不良行为
                    </el-button
                    >
                    <el-button
                            @click="handleAddBadPersonnel()"
                            class="success-btn"
                            type="success"
                            round
                            v-if="isShowBadPersonnel"
                    >新增不良人员
                    </el-button
                    >
                    <el-button
                            @click="handleBack"
                            class="back-btn"
                            type="primary"
                            plain
                            round
                    >返回
                    </el-button
                    >
                </div>
            </div>
            <div class="project-content">
                <div class="project-city" v-if="isShowList">
                    <City
                            :selectCityId="searchInfo.regionCode"
                            @selectCity="handleSelectCity"
                            :city-list="cityLists"
                    ></City>
                </div>
                <div class="project-list-box">
                    <HeaderCard v-if="isShowAuditHeader " :statsData="statsData" @changeType="changeType"></HeaderCard>
                    <div class="radio-box">
                        <el-radio-group v-model="searchInfo.isToday" @change="handleChangeRadio" size="large"
                                        v-if="isShowSitcardSwiping">
                            <el-radio-button label="今日开标" value="1"/>
                            <el-radio-button label="全部开标" value="2"/>
                        </el-radio-group>
                    </div>
                    <div class="project-list-conotent" :class="{conotentTopNone: !isShowAuditHeader}">
                        <el-table
                                :data="tableData"
                                style="width: 100%"
                                :height="isShowAuditHeader ? 600 : 650"
                                :row-class-name="tableRowClassName"
                                class="my-table"
                        >
                            <el-table-column
                                    label="序号"
                                    type="index"
                                    :index="indexMethod"
                                    align="center"
                                    width="80px"
                            />
                            <el-table-column
                                    v-for="item in tableColData"
                                    :key="item.prop"
                                    align="center"
                                    :prop="item.prop"
                                    :label="item.label"
                                    :min-width="item.width"
                                    :show-overflow-tooltip=" ['tenderProjectName','bidSectionName'].includes(item.prop) ? false: tooltipOptions"
                            >
                                <template #default="{ row }">
                                    <div v-if="['tenderProjectName','bidSectionName'].includes(item.prop)">
                                        <el-popover placement="right-start" trigger="hover" width="260">
                                            <template #reference>
                                                <span class="step-span">
                                                    <template
                                                            v-if="row[item.prop] && row[item.prop].includes('[已作废]')">
                                                        <span style="color: red;">[已作废]</span>{{
                                                        row[item.prop].replace('[已作废]', '')
                                                        }}
                                                    </template>
                                                    <template v-else>
                                                        {{ row[item.prop] }}
                                                    </template>
                                                </span>
                                            </template>
                                            <div class="project-city-box" v-if="row['bidSectionNames']">
                                                <div v-for="(val,index) in row['bidSectionNames'].split(',')"
                                                     :key="val">{{ val }}
                                                </div>
                                            </div>
                                        </el-popover>
                                    </div>
                                    <div v-else-if="['approvalStatus'].includes(item.prop)">
                                        <img
                                                class="my-img"
                                                :src="htStatusRender(row.approvalStatus).icon"
                                                alt=""
                                        />
                                        <span
                                                :class="htStatusRender(row.approvalStatus).className"
                                        >{{ htStatusRender(row.approvalStatus).label }}</span
                                        >
                                    </div>
                                    <div v-else-if="['status'].includes(item.prop)">
                                        <span :style="{ color: getInsStatusColor(row[item.prop]) }">{{
                                            row[item.prop] ? row[item.prop] : "--"
                                            }}</span>
                                    </div>
                                    <div v-else-if="['hasSupervision'].includes(item.prop)">
                                        <img
                                                v-if="row[item.prop] == 9"
                                                class="my-img my-img-yj"
                                                :src="item.greenImg"
                                                alt=""
                                        />
                                        <img
                                                v-if="row[item.prop] == 2"
                                                class="my-img my-img-yj"
                                                :src="item.redImg"
                                                alt=""
                                        />
                                        <span v-if="row[item.prop] == 0">--</span>
                                    </div>
                                    <span v-else>{{ row[item.prop] ? row[item.prop] : "--" }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column align="center" label="操作" width="180px">
                                <template #default="{ row }">
                                    <div v-if="!isShowBadPersonnel">
                                        <el-button v-if="!isShowEvaluate" type="primary" class="my-detail-btn" plain
                                                   size="small" round @click="handleViewDetail(row)">查看详情
                                        </el-button>
                                        <el-button v-if="isShowAddIns && row.status !='办结' && row.status !='已反馈'"
                                                   type="warning" plain size="small" round
                                                   @click="handleWithdraw(row.rowGuid)">撤回
                                        </el-button>
                                        <el-button v-if="isShowAddIns && row.status !='办结'" type="warning" plain
                                                   size="small" round
                                                   @click="handleCompleted(row.rowGuid)">办结
                                        </el-button>
                                        <el-button
                                                v-if="!isShowAddIns && !isShowLock && !isShowSitcardSwiping && !isShowEvaluate && isShowAfterProcess"
                                                type="warning" plain size="small" round
                                                @click="handleInitiate(row.tenderProjectGuid?row.tenderProjectGuid:row.bidSectionGuid)">
                                            发起指令
                                        </el-button>
                                        <el-button v-if="isShowSitcardSwiping && searchInfo.isToday == 1" type="warning"
                                                   plain size="small" round @click="handleRealName(row,'1')">实名认证
                                        </el-button>
                                        <!--                                    <el-button v-if="isShowEvaluate" type="warning" plain size="small" round-->
                                        <!--                                               @click="handleEvaluate(row)">发起评价-->
                                        <!--                                    </el-button>-->
                                        <el-button v-if="isShowAddIns" type="success" plain size="small" round
                                                   @click="handleDLPJEvaluate(row)">发起评价
                                        </el-button>
                                        <el-button v-if="isShowLock" type="warning" plain size="small" round
                                                   @click="handleUnlock(row.rowGuid)">解锁
                                        </el-button>
                                        <el-button v-if="isShowTenderRecord && row.approvalStatus == 8" type="warning"
                                                   size="small" round @click="handleReturnProcess(row)">流程退回
                                        </el-button>
                                      <el-button v-if="isShowWrittenRecord && row.approvalStatus == 8" type="warning"
                                                 size="small" round @click="handleReturnProcess(row)">流程退回
                                      </el-button>
                                        <el-button v-if="isShowDcBidSection && row.approvalStatus == 8" type="warning"
                                                   size="small" round @click="handleReturnProcess(row)">流程退回
                                        </el-button>
                                        <el-button v-if="isShowDcIllegFilingSection && row.approvalStatus == 8"
                                                   type="warning"
                                                   size="small" round @click="handleReturnProcess(row)">流程退回
                                        </el-button>
                                        <el-button
                                                v-if="isShowBadBehavior  && row.publishStatus == 1 && row.isPublishNetword == 1"
                                                type="warning" plain size="small" round
                                                @click="handleRevoke(row.rowGuid)">撤回发布
                                        </el-button>
                                        <el-button
                                                v-if="isShowBadBehavior && row.publishStatus == 0 && row.isPublishNetword == 1"
                                                type="success" plain size="small" round
                                                @click="handlePublishNetword(row.rowGuid)">确认发布
                                        </el-button>
                                        <!-- <div class="table-view" @click="handleViewDetail(row)">
                                          <el-icon><ArrowRightBold /></el-icon>
                                        </div> -->
                                    </div>
                                    <div v-if="isShowBadPersonnel">
                                        <el-button  type="success" class="my-detail-btn" plain
                                                   size="small" round @click="handleViewDetail(row)">查看详情
                                        </el-button>
<!--                                        <el-button  v-if="row.publishStatus == 0" type="primary" class="my-detail-btn" plain-->
<!--                                                   size="small" round @click="updateBadPerson(row)">修改内容-->
<!--                                        </el-button>-->
                                        <el-button
                                            v-if="row.publishStatus == 1 && row.isPublish == 1"
                                            type="warning" plain size="small" round
                                            @click="handlePersonRevoke(row.id, '撤回',9)">撤回发布
                                        </el-button>
                                        <el-button
                                            v-if="row.publishStatus == 1 && row.isPublish == 0"
                                            type="warning" plain size="small" round
                                            @click="handlePersonRevoke(row.id, '发布',1)">确认发布
                                        </el-button>
                                    </div>
                                </template>
                            </el-table-column>
                        </el-table>
                        <el-dialog v-model="dialogReturnProcessVisible" width="700px" title="流程退回">
                            <el-form ref="ruleFormRef" :model="formData" :rules="rules">
                                <el-form-item label="退回原因" prop="reason">
                                    <el-input
                                            v-model="formData.reason"
                                            :rows="6"
                                            placeholder="请退回原因！"
                                            type="textarea"
                                            autocomplete="off"
                                    />
                                </el-form-item>
                            </el-form>
                            <template #footer>
        <span class="dialog-footer">
          <el-button round @click="dialogReturnProcessVisible = false">取消</el-button>
          <el-button round type="primary" @click="handleOperateProcess(ruleFormRef)"
          >确 认</el-button
          >
      </span>
                            </template>
                        </el-dialog>
                        <div class="pagination-box">
                            <el-pagination
                                    class="pagination"
                                    background
                                    v-model:current-page="searchInfo.pageNum"
                                    v-model:page-size="searchInfo.pageSize"
                                    :total="total"
                                    :size="searchInfo.pageSize"
                                    :page-sizes="[100, 200, 300, 400]"
                                    layout="total, sizes, prev, pager, next, jumper"
                                    @size-change="handleSizeChange"
                                    @current-change="handleChangePage"
                            />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </PageWrapper>
    <allDialogView ref="allDialogViewEl" :dialogTitle='dialogTitle'>
        <template #content>
            <projectInstruct v-if="allDialogViewEl.showDialog && (isShowAddIns || isShowAddInsRECORD)"
                             @handleClose="handleInitiate"
                             :type="instructType" :tenderProjectGuid="tenderProjectGuid"
                             :detailRowGuid='detailRowGuid'></projectInstruct>
            <projectBadBehavior v-if="allDialogViewEl.showDialog && isShowBadBehavior" @handleClose="handleInitiate"
                                :type="instructType" :tenderProjectGuid="tenderProjectGuid"
                                :detailRowGuid='detailRowGuid' :behaviorDetail="behaviorDetail"></projectBadBehavior>
        </template>
    </allDialogView>
    <allDialogView ref="personDialogViewEl" :dialogTitle='dialogTitle' :dialog-width="1300">
        <template #content>
            <projectBadPersionnel v-if="personDialogViewEl.showDialog && isShowBadPersonnel" @handleClose="handlePersonInitiate"
                                  :badPersionDetail="badPersionDetail" :type="instructType"></projectBadPersionnel>
        </template>
    </allDialogView>
</template>

<script setup>
import City from "./cityList/index.vue";
import HeaderCard from "./headerCard/index.vue";
import projectInstruct from "../projectInstruct/index.vue";
import projectBadBehavior from "../projectBadBehavior/index.vue";
import projectBadPersionnel from '../projectBadPersonnel/index.vue';
import {httpGet, httpPost} from "@wl-fe/http/dist";
import {DATA_ARRAY, htStatusRender} from "./content.js";
import {ref, computed} from "vue";
import {ElMessage} from "element-plus";
import AccountManagementFilter from "@/filters/index.js";
import {getOptionList} from '@/pages/project/projectBadBehavior/formOptionList'
import useGloBalStore from "@/store/useGlobalStore"

const {user} = useGloBalStore();
// 定义 tooltipOptions 对象
const tooltipOptions = ref({
    effect: 'light', // 提示框主题为亮色
    placement: 'top', // 提示框显示在单元格上方
    showArrow: true, // 显示提示框的箭头
    hideAfter: 200, // 提示框隐藏的延迟时间为 200 毫秒
    popperOptions: {
        strategy: 'fixed' // 使用 fixed 定位策略
    }
});
const {getInsStatusColor} = AccountManagementFilter();
console.log('getInsStatusColor', getInsStatusColor, AccountManagementFilter);

const router = useRouter();
const route = useRoute();
const indexMethod = (index) => {
    return index + 1;
};
const changeType = (val) => {
    searchInfo.approvalStatus = val;
    handleGetList();
};
const handleSearch = (val) => {
    searchInfo.keyword = val;
    handleGetList();
};
const handleBack = () => {
    router.push("/projectStatisticaData");
};
const isLoading = ref(false);
let total = ref(1);
let statsData = ref(null);
let {query} = toRefs(route);
const {nodeCode, parentNodeCode} = query.value;
const tableColData = ref(DATA_ARRAY[parentNodeCode][nodeCode]);
let searchInfo = reactive({
    keyword: "",
    regionCode: "",
    pageNum: 1,
    pageSize: 10,
    nodeCode,
    isToday: "1",
    approvalStatus: 1,
    platformCode: undefined,
    currentNodeCode: undefined
});
const dialogReturnProcessVisible = ref(false);
const ruleFormRef = ref();
const rules = reactive({
    reason: [{required: true, message: "请先退回原因！", trigger: "blur"}],
});
let returnProcessInfo = reactive({
    reason: "",
    platformCode: "",
    rowGuid: "",
    tenderProcessNode: "",
    tenderProjectGuid: ""
});
const formData = reactive({
    reason: "",
});
const dialogTitle = ref('');
const behaviorDetail = ref({})
const badPersionDetail = ref({})
const isShowTenderRecord = computed(() => {
    return nodeCode == "TENDER_RECORD" && parentNodeCode == "inProcessSupervisionData";
});
const isShowDcBidSection = computed(() => {
    return nodeCode == "DC_BID_SECTION" && parentNodeCode == "inProcessSupervisionData";
});
const isShowDcIllegFilingSection = computed(() => {
    return nodeCode == "DC_ILLEG_FILING_SECTION" && parentNodeCode == "inProcessSupervisionData";
});
const isShowWrittenRecord = computed(() => {
  return nodeCode == "WRITTEN_RECORD" && parentNodeCode == "inProcessSupervisionData";
});


const isShowEvaluate = computed(() => {
    return nodeCode == "C_TENDER_AGENT_EVALUATE";
});
const isShowAddIns = computed(() => {
    return nodeCode == "AS_INSTRUCTION_MATTERS";
});
const isShowAddInsRECORD = computed(() => {
    return nodeCode == "TENDER_RECORD";
});
const isShowWiningAdvice = computed(() => {
    return nodeCode == "WINING_ADVICE";
});

const isShowBadBehavior = computed(() => {
    return nodeCode == "BAD_BEHAVIOR";
});
const isShowBadPersonnel = computed(() => {
    return nodeCode == "C_BAD_PERSONNEL";
});
const isShowSitcardSwiping = computed(() => {
    return nodeCode == "C_TENDER_SITCARD_SWIPING";
});
const isShowLock = computed(() => {
    return nodeCode == "AS_AGENCY_LOCKOUT";
});
const isShowAuditHeader = computed(() => {
    return parentNodeCode != "afterProcessSupervisionData" && statsData.value != null;
});
const isShowAfterProcess = computed(() => {
    return parentNodeCode == "afterProcessSupervisionData";
});
const handleSelectCity = (item) => {
    searchInfo.regionCode = item.code;
    handleGetList();
};
const handleChangeRadio = (val) => {
    handleGetList();
};
const tableRowClassName = ({rowIndex}) => {
    if (rowIndex % 2 !== 0) {
        return "warning-row";
    }
    return "";
};
const handleReturnProcess = (row) => {
    returnProcessInfo.rowGuid = row.rowGuid;
    returnProcessInfo.platformCode = row.platformCode;
    returnProcessInfo.tenderProcessNode = row.nodeCode;
    returnProcessInfo.tenderProjectGuid = row.tenderProjectGuid;
    dialogReturnProcessVisible.value = true;
};
const handleOperateProcess = async (formEL) => {
    if (!formEL) return;
    formEL.validate(async (valid, fields) => {
        if (valid) {
            returnProcessInfo.reason = formData.reason;
            const res = await httpPost("/tradingApi/pushProcessReturnToTrading", returnProcessInfo);
            if (res.code == 200) {
                ElMessage.success("退回成功");
            }
            dialogReturnProcessVisible.value = false;
            handleGetList();
        }
    });

};

const updateBadPerson = async (row) => {
    const data = await httpGet("inviteTender/bad/personnel/" + row.id);
    badPersionDetail.value = data;
    dialogTitle.value = '不良人员行为';
    instructType.value = '1'
    personDialogViewEl.value.showDialog = !personDialogViewEl.value.showDialog
    return

}

const handleViewDetail = async (row) => {
    if (nodeCode == 'AS_AGENCY_LOCKOUT' && row.lockType == "投诉异议") {
        let params = {
            complaintNo: row.nodeGuid,
        };
        const res = await httpPost("/subSysComplaint/skipUrl", params);
        window.open(res.msg, "_blank");
        return
    }
    if (nodeCode == 'C_TENDER_SITCARD_SWIPING') {
        handleRealName(row, '2')
        return
    }
    if (nodeCode == 'AS_INSTRUCTION_MATTERS') {
        dialogTitle.value = '查看行政监管事项';
        tenderProjectGuid.value = ''
        instructType.value = '2'
        detailRowGuid.value = row.rowGuid
        allDialogViewEl.value.showDialog = !allDialogViewEl.value.showDialog
        return
    }
    if (nodeCode == 'BAD_BEHAVIOR') {
        dialogTitle.value = '不良行为';
        instructType.value = '2'
        behaviorDetail.value = row
        allDialogViewEl.value.showDialog = !allDialogViewEl.value.showDialog
        return
    }

    if (nodeCode == 'C_BAD_PERSONNEL') {
        const data = await httpGet("inviteTender/bad/personnel/" + row.id);
        badPersionDetail.value = data;
        dialogTitle.value = '不良人员行为';
        instructType.value = '2'
        personDialogViewEl.value.showDialog = !personDialogViewEl.value.showDialog
        return
    }
    //新增
    // if (nodeCode == 'BAD_PERSONNEL') {
    //     dialogTitle.value = '人员不良行为';
    //     instructType.value = '2'
    //     behaviorDetail.value = row
    //     allDialogViewEl.value.showDialog = !allDialogViewEl.value.showDialog
    //     return
    // }
    if (parentNodeCode == 'afterProcessSupervisionData') {
        router.push({
            path: "/projectStatisticaData/projectPress",
            query: {
                tenderProjectGuid: row.tenderProjectGuid,
                projectGuid: row.projectGuid,
                nodeCode: nodeCode,
                parentNodeCode: parentNodeCode,
                name: row.tenderProjectName,
                code: row.tenderProjectCode
            },
        });
    } else {
        router.push({
            path: "/projectStatisticaData/projectDetail",
            query: {
                nodeCode: row.nodeCode,
                bidSectionGuid: row.bidSectionGuid,
                rowGuid: row.rowGuid,
                projectGuid: row.projectGuid,
                parentNodeCode: parentNodeCode,
                platformCode: row.platformCode,
                tolerance: row.tolerance,
                isShowLeft: nodeCode == "AS_AGENCY_LOCKOUT" ? false : true
            },
        });
    }

};
let cityLists = ref([]);
const isShowList = computed(() => {
    return cityLists.value.length > 0;
});
const searchPlaceholder = computed(() => {
    // 招标备案、评标组织形式变更、招标容缺补件
    if (['TENDER_RECORD', 'EVALUATION_FORM_CHANGE', 'TENDER_TOLERANCE'].includes(nodeCode)) {
        return '请输入招标项目名称或编号';
    }
    if (nodeCode == 'BAD_BEHAVIOR') {
        return '请输入单位名称或统一社会信用代码';
    }
    if (nodeCode == 'C_BAD_PERSONNEL') {
        return '请输入标题名称';
    }
    return '请输入标段（包）名称或编号';
});
const handleChangePage = (val) => {
    console.log(val);
    searchInfo.pageNum = val
    handleGetList();
};
const handleSizeChange = (val) => {
    console.log(val);
    searchInfo.pageNum = val
    handleGetList();
};
const platformOption = ref([]);
const pressOption = ref([]);
onMounted(async () => {
    if (nodeCode != 'BAD_BEHAVIOR' && nodeCode != 'C_BAD_PERSONNEL') {
        const list = await httpGet("/api/sysRegion/list", {});
        cityLists.value = list;
        searchInfo.regionCode = user.regionCode ? user.regionCode : "";
    }
    handleGetList();
    platformOption.value = await getOptionList('jyptbm');
    pressOption.value = await getOptionList('tenderProcessNode')
    console.log('processingOptions', platformOption.value, pressOption.value);

});
let tableData = reactive([]);
const handleApiList = (key) => {
    let api = ''
    switch (key) {
        case 'AS_AGENCY_LOCKOUT':
            api = `/inviteTender/asAgencyLockout/list`
            break;
        case 'C_TENDER_SITCARD_SWIPING':
            api = `/inviteTender/tenderSitcardSwiping/list`
            break;
        case 'AS_INSTRUCTION_MATTERS':
            api = `/inviteTender/asProjectPauseRestoration/list`
            break;
        case 'C_TENDER_AGENT_EVALUATE':
            api = `/inviteTender/tenderAgentEvaluete/list`
            break;
        case 'BAD_BEHAVIOR':
            api = `/inviteTender/badBehavior/list`
            break;
        case 'C_BAD_PERSONNEL':
            api = `/inviteTender/bad/personnel/list`
            break;
        default:
            api = `/inviteTender/tenderProcessNode/list`
            break;
    }
    return api
}
const handleStatsApiList = (key) => {
    let api = ''
    switch (key) {
        case 'AS_AGENCY_LOCKOUT':
            break;
        case 'C_TENDER_SITCARD_SWIPING':
            break;
        case 'AS_INSTRUCTION_MATTERS':
            break;
        case 'C_TENDER_AGENT_EVALUATE':
            break;
        case 'BAD_BEHAVIOR':
            break;
        case 'C_BAD_PERSONNEL':
            break;
        default:
            api = `/inviteTender/tenderProcessNode/getStatsData`
            break;
    }
    return api
}
const handleGetList = async () => {
    isLoading.value = true;
    let data = await httpPost(handleApiList(nodeCode) + `?pageNum=${searchInfo.pageNum}&pageSize=${searchInfo.pageSize}`, searchInfo);
    if (handleStatsApiList(nodeCode) != '') {
        statsData.value = await httpPost("/inviteTender/tenderProcessNode/getStatsData", searchInfo);
    }
    console.log(data);
    isLoading.value = false;
    total.value = data.total;
    tableData = data.rows;
};
const allDialogViewEl = ref()
const personDialogViewEl = ref()
const instructType = ref('1')
const tenderProjectGuid = ref('')
const detailRowGuid = ref('')
const handleInitiate = (guid) => {
    tenderProjectGuid.value = ''
    instructType.value = '1'
    dialogTitle.value = "新增行政监管事项"
    if (guid) {
        tenderProjectGuid.value = guid
    }
    if (guid == 'refresh') {
        handleGetList()
    }
    allDialogViewEl.value.showDialog = !allDialogViewEl.value.showDialog
}
const handlePersonInitiate =  (guid) => {
    if (guid == 'refresh') {
        handleGetList()
    }
    personDialogViewEl.value.showDialog = false
}
const handleAddBadbehavior = () => {
    dialogTitle.value = "新增不良行为"
    allDialogViewEl.value.showDialog = !allDialogViewEl.value.showDialog
}

const handleAddBadPersonnel = () => {
    dialogTitle.value = "新增不良人员"
    badPersionDetail.value = {}
    instructType.value = '1';
    personDialogViewEl.value.showDialog = !personDialogViewEl.value.showDialog
}

const handleEvaluate = async (row) => {
    const res = await httpPost("/inviteTender/tenderAgentEvaluete/skipUrl", row);
    window.open(res.msg, "_blank");
    return
};

const handleDLPJEvaluate = async (row) => {
    console.log(row.bidSectionUnifiedCode + "11111111111111111111111111111111111111111111111111")
    sessionStorage.setItem("evUrl", JSON.stringify('/singleLoginController/getAgentEvaluationBehaviorUrl?bidSectionUnifiedCode=' + row.bidSectionUnifiedCode));
    await router.push(`/expertEvaluateStatisticalData/expertMainStore`);
};

const handleUnlock = async (guid) => {
    isLoading.value = true;
    const result = await httpPost("/inviteTender/asAgencyLockout/unlockAgency", {"rowGuid": guid});
    if (result.code == 200) {
        ElMessage({
            message: result.msg,
            type: "success",
        });
        handleGetList()
    } else {
        ElMessage({
            message: result.msg,
            type: "error",
        });
    }
    isLoading.value = false;
}
const handleRevoke = (guid) => {
    ElMessageBox.alert('确认撤回？', '', {
        confirmButtonText: '确认',
        callback: async (action) => {
            if (action == 'confirm') {
                const result = await httpPost(`/inviteTender/badBehavior/removeBadBehavior/${guid}`);
                if (result.code == 200) {
                    ElMessage({
                        message: result.msg,
                        type: "success",
                    });
                    handleGetList()
                } else {
                    ElMessage({
                        message: result.msg,
                        type: "error",
                    });
                }
            }
        }
    })
}

const handlePersonRevoke = (id,msg,isPublish) => {
    ElMessageBox.alert('确认'+ msg +'？', '', {
        confirmButtonText: '确认',
        callback: async (action) => {
            if (action == 'confirm') {
                const  params = {
                    id: id,
                    isPublish: isPublish
                };
                const result = await httpPost(`/inviteTender/bad/personnel/updateIsPublish`,params);
                console.log( "123123123",result)
                if (result.code == 200) {
                    ElMessage({
                        message: result.msg,
                        type: "success",
                    });
                    handleGetList()
                } else {
                    ElMessage({
                        message: result.msg,
                        type: "error",
                    });
                }
            }
        }
    })
}

const handlePublishNetword = (guid, msg) => {
    ElMessageBox.alert('确认发布？', '', {
        confirmButtonText: '确认',
        callback: async (action) => {
            if (action == 'confirm') {
                const result = await httpPost(`/inviteTender/badBehavior/publishNetword/${guid}`);
                if (result.code == 200) {
                    ElMessage({
                        message: result.msg,
                        type: "success",
                    });
                    handleGetList()
                } else {
                    ElMessage({
                        message: result.msg,
                        type: "error",
                    });
                }
            }
        }
    })
}
const handleWithdraw = async (guid) => {
    const result = await httpPost("/inviteTender/asProjectPauseRestoration/withdraw", {"rowGuid": guid});
    if (result.code == 200) {
        ElMessage({
            message: result.msg,
            type: "success",
        });
        handleGetList()
    } else {
        ElMessage({
            message: result.msg,
            type: "error",
        });
    }
}
const handleCompleted = async (guid) => {
    const result = await httpPost("/inviteTender/asProjectPauseRestoration/completed", {"rowGuid": guid});
    if (result.code == 200) {
        ElMessage({
            message: result.msg,
            type: "success",
        });
        handleGetList()
    } else {
        ElMessage({
            message: result.msg,
            type: "error",
        });
    }
}
const handleRealName = async (row, isSee) => {
    router.push({
        path: "/projectStatisticaData/projectRealName",
        query: {
            bidSectionGuid: row.bidSectionGuid,
            tenderProjectGuid: row.tenderProjectGuid,
            nodeCode: nodeCode,
            parentNodeCode: parentNodeCode,
            isSee: isSee
        },
    });
}
</script>
<style scoped lang="less">

.project-list {
  width: 100%;
  height: 100%;

  .project-search-box {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;

    .select {
      width: 200px;
      margin-right: 16px;
      border-radius: 50px;

      :deep(.el-select__wrapper) {
        border-radius: 50px;
      }
    }


    .back-btn {
      width: 90px;
    }

    .success-btn {
      width: 150px;
    }
  }

  .project-content {
    width: 100%;
    height: 93%;
    margin-top: 1%;
    display: flex;
    justify-content: space-between;

    .project-city {
      width: 8%;
      height: 100%;
      margin-right: 10px;
      overflow: auto;
      flex-shrink: 0;
    }

    .project-list-box {
      height: 100%;
      flex: 1;
      width: 0;
      display: flex;
      flex-direction: column;

      .radio-box {
        text-align: right;
      }

      .project-list-conotent {
        width: 100%;
        // height: 82%;
        margin-top: 1%;
        flex: 1;
        height: 0;

        .pagination-box {
          margin-top: 20px;
          display: flex;
          flex-direction: row;
          justify-content: flex-end;
        }
      }

      .conotentTopNone {
        margin-top: 0;
      }
    }
  }
}

.date-picker {
  width: 200px;
  margin-right: 16px;

  .el-input__wrapper {
    border-radius: 50px; /* 设置你想要的圆角值 */
  }
}

.table-view {
  width: 20px;
  height: 20px;
  border-radius: 20px;
  border: 1px solid #3366ff;
  text-align: center;
  line-height: 22px;
  cursor: pointer;
  margin: 0 auto;
  color: #3366ff;
}

.my-img {
  width: 20px;
  margin-right: 5px;
}

.my-img-yj {
  width: 30px;
}

.status-red {
  color: red;
}

.status-green {
  color: green;
}

.status-orign {
  color: #f4871c;
}

/* 设置滚动条的样式 */
.project-city-box::-webkit-scrollbar {
  width: 6px;
  box-sizing: border-box;
}

/* 滚动条滑块 */
.project-city-box::-webkit-scrollbar-thumb {
  height: 5px;
  border-radius: 5px;
  background-color: #c2cede;
}

/*滚动槽*/
.project-city-box::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0);
  border-radius: 10px;
  background: #ffffff;
}

/* 滚动条滑块 鼠标悬浮 */
.project-city-box::-webkit-scrollbar-thumb:hover {
  background-color: #c2cede;
  // border: 1px solid rgba(0, 0, 0, 0.21);
  cursor: pointer;
}

.my-table {
  :deep(.el-table__header-wrapper) {
    .el-table__cell {
      background: #f0f6ff;
      height: 54px;
      color: rgba(0, 0, 0, 0.88);
    }
  }

  :deep(.el-table__row) {
    height: 54px;
  }

  :deep(.warning-row) {
    background: #f9fbff;
  }
}
</style>
