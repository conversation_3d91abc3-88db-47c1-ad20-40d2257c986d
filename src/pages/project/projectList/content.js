import {STATUS_ICONS_MAP} from "@/constant/icon";
import redImg from '/projectManagement/red.gif'
import yellowImg from '/projectManagement/yellow.gif'
import greenImg from '/projectManagement/green.gif'

export const DATA_ARRAY = {
    inProcessSupervisionData: {
        TENDER_RECORD: [
            {
                label: "招标项目编号",
                prop: "tenderProjectCode",
                width: "12%",
            },
            {
                label: "招标项目名称",
                prop: "tenderProjectName",
                width: "15%",
            },
            {
                label: "招标人",
                prop: "tendererName",
                width: "10%",
            },
            {
                label: "代理机构",
                prop: "tenderAgentName",
                width: "10%",
            },
            {
                label: "监管部门",
                prop: "approveDeptName",
                width: "10%",
            },
            {
                label: "所属区域",
                prop: "regionName",
                width: "8%",
            },
            {
                label: "招标方式",
                prop: "tenderMode",
                width: "6%",
            },
            {
                label: "组织形式",
                prop: "tenderOrganizeForm",
                width: "6%",
            },
            {
                label: "状态",
                prop: "approvalStatus",
                width: "10%",
            }
        ], // 招标备案
        WINING_ADVICE: [
            {
                label: "招标项目名称",
                prop: "tenderProjectName",
                width: "200",
            },
            {
                label: "标段（包）编号",
                prop: "bidSectionCode",
                width: "150",
            },
            {
                label: "标段（包）名称",
                prop: "bidSectionName",
                width: "200",
            },
            {
                label: "监管部门",
                prop: "approveDeptName",
            },
            {
                label: "中标人",
                prop: "winBidderName",
                width: "150",
            },
            {
                label: "项目负责人",
                prop: "bidManager",
            },
            {
                label: "状态",
                prop: "approvalStatus",
            }
        ], // 中标通知书
        WRITTEN_RECORD: [
            {
                label: "招标项目名称",
                prop: "tenderProjectName",
                width: "200",
            },
            {
                label: "标段（包）编号",
                prop: "bidSectionCode",
                width: "120",
            },
            {
                label: "标段（包）名称",
                prop: "bidSectionName",
                width: "200",
            },
            {
                label: "招标人",
                prop: "tendererName",
                width: "120",
            },
            {
                label: "代理机构",
                prop: "tenderAgentName",
                width: "120",
            },
            {
                label: "监管部门",
                prop: "approveDeptName",
            },
            {
                label: "开标时间",
                prop: "bidOpeningTime",
            },
            {
                label: "中标时间",
                prop: "approvalTime",
            },
            {
                label: "状态",
                prop: "approvalStatus",
            }
        ], // 书面报告备案
        C_BID_EXCEPTION_REPORT: [
            {
                label: "招标项目名称",
                prop: "tenderProjectName",
                width: "200",
            },
            {
                label: "标段（包）编号",
                prop: "bidSectionCode",
                width: "170",
            },
            {
                label: "标段（包）名称",
                prop: "bidSectionName",
                width: "200",
            },
            {
                label: "招标人",
                prop: "tendererName",
                width: "100",
            },
            {
                label: "代理机构",
                prop: "tenderAgentName",
                width: "100",
            },
            {
                label: "监管部门",
                prop: "approveDeptName",
                width: "100",
            },
            {
                label: "所属区域",
                prop: "regionName",
            },
            {
                label: "招标方式",
                prop: "tenderMode",
            },
            {
                label: "状态",
                prop: "approvalStatus",
            }
        ], // 特殊事项
        C_TENDER_ABNORMITY_REPORT: [
            {
                label: "项目名称",
                prop: "projectName",
                width: "200",
            },
            {
                label: "招标项目编号",
                prop: "projectCode",
                width: "150",
            },
            {
                label: "招标人",
                prop: "tendererName",
                width: "120",
            },
            {
                label: "代理机构",
                prop: "tenderAgentName",
                width: "120",
            },
            {
                label: "监管部门",
                prop: "approveDeptName",
                width: "120",
            },
            {
                label: "所属区域",
                prop: "regionName",
                width: "120",
            },
            {
                label: "容缺补件状态",
                prop: "waitAuditStatus",
            },
            {
                label: "核验状态",
                prop: "approvalStatus",
            }
        ], // 招标容缺补件
        DC_BID_SECTION: [
            {
                label: "招标项目编号",
                prop: "projectCode",
                width: "100",
            },
            {
                label: "招标项目名称",
                prop: "projectName",
                width: "200",
            },
            {
                label: "标段编号",
                prop: "bidSectionCode",
                width: "150",
            },
            {
                label: "发包人",
                prop: "tendererName",
                width: "120",
            },
            {
                label: "监管部门",
                prop: "approveDeptName",
                width: "120",
            },
            {
                label: "所属区域",
                prop: "regionName",
                width: "100",
            },
            {
                label: "国民经济行业分类",
                prop: "industriesName",
                width: "130",
            },
            {
                label: "状态",
                prop: "approvalStatus",
                width: "150",
            }
        ], // 直接发包备案
        DC_ILLEG_FILING_SECTION: [
            {
                label: "招标项目编号",
                prop: "projectCode",
                width: "100",
            },
            {
                label: "招标项目名称",
                prop: "projectName",
                width: "200",
            },
            {
                label: "标段编号",
                prop: "bidSectionCode",
                width: "150",
            },
            {
                label: "发包人",
                prop: "tendererName",
                width: "120",
            },
            {
                label: "监管部门",
                prop: "approveDeptName",
                width: "120",
            },
            {
                label: "所属区域",
                prop: "regionName",
                width: "100",
            },
            {
                label: "国民经济行业分类",
                prop: "industriesName",
                width: "100",
            },
            {
                label: "状态",
                prop: "approvalStatus",
                width: "150",
            }
        ], // 未依法发包
        ES_EXPERT_PROJECT: [
            {
                label: "招标项目编号",
                prop: "tenderProjectCode",
                width: "100",
            },
            {
                label: "招标项目名称",
                prop: "tenderProjectName",
                width: "200",
            },
            {
                label: "招标人",
                prop: "tendererName",
                width: "120",
            },
            {
                label: "招标人",
                prop: "tenderAgentName",
                width: "120",
            },
            {
                label: "监管部门",
                prop: "approveDeptName",
                width: "120",
            },
            {
                label: "所属区域",
                prop: "regionName",
                width: "120",
            },
            {
                label: "状态",
                prop: "approvalStatus",
            }
        ], // 远程异地
    },
    afterProcessSupervisionData: {
        TENDER_RECORD: [
            // {
            //     label: "投资项目统一代码",
            //     prop: "investProjectCode",
            //     width: 150,
            // },
            // {
            //     label: "项目来源",
            //     prop: "projectSource",
            //     width: 150,
            // },
            {
                label: "项目名称",
                prop: "tenderProjectName",
                width: 250,
            },
            {
                label: "招标项目编号",
                prop: "tenderProjectCode",
                width: 170,
            },
            {
                label: "招标人",
                prop: "tendererName",
                width: 120,
            },
            {
                label: "代理机构",
                prop: "tenderAgentName",
                width: 120,
            },
            {
                label: "项目总投资（万元）",
                prop: "totalMoney",
                width: 80,
            },
            {
                label: "监管部门",
                prop: "approveDeptName",
            },
            {
                label: "所属区域",
                prop: "regionName",
                width: 100,
            },
            // {
            //     label: "办理状态",
            //     prop: "processingStatus",
            //     width: 80,
            // },
            {
                label: "电子监察",
                prop: "hasSupervision",
                redImg: redImg,
                yellowImg: yellowImg,
                greenImg: greenImg,
                width: 70,
            }
        ], // 事后监管 累计数量
    },
    electronicMonitoringData: {
        AS_INSTRUCTION_MATTERS: [
            {
                label: "标段唯一标识码",
                prop: "bidSectionUnifiedCode",
            },
            {
                label: "标段（包）编号",
                prop: "bidSectionCode",
                width: "120",
            },
            {
                label: "标段（包）名称",
                prop: "bidSectionName",
                width: "200",
            },
            {
                label: "监管交易环节",
                prop: "tradeStep",
            },
            {
                label: "监管事项",
                prop: "approvalProject",
                width: "200",
            },
            {
                label: "监管单位",
                prop: "approveDeptName",
            },
            {
                label: "监管指令下达时间",
                prop: "approvalTime",
                width: "150",
            },
            {
                label: "是否暂停招标活动",
                prop: "isPast",
            },
            {
                label: "回复时间",
                prop: "feedDate",
                width: "150",
            },
            {
                label: "处理状态",
                prop: "status",
            }
        ], // 电子监管 预警数量
        AS_AGENCY_LOCKOUT: [
            {
                label: "标段唯一标识码",
                prop: "bidSectionUnifiedCode",
            },
            {
                label: "标段（包）编号",
                prop: "bidSectionCode",
                width: "120",
            },
            {
                label: "标段（包）名称",
                prop: "bidSectionName",
                width: "200",
            },
            {
                label: "锁定类型",
                prop: "lockType",
            },
            {
                label: "单位名称",
                prop: "tenderAgentName",
            },
            {
                label: "单位统一社会信用代码",
                prop: "tenderAgentCode",
            },
            {
                label: "人员姓名",
                prop: "personName",
            },
            {
                label: "身份证号",
                prop: "idCard",
            },
            {
                label: "锁定状态",
                prop: "lockStatus",
            }
        ], // 人员解锁
        C_TENDER_SITCARD_SWIPING: [
            {
                label: "投资统一代码",
                prop: "investProjectCode",
            },
            {
                label: "招标项目名称",
                prop: "tenderProjectName",
                width: "200",
            },
            {
                label: "标段（包）名称",
                prop: "bidSectionName",
                width: "200",
            },
            {
                label: "标段（包）编号",
                prop: "bidSectionCode",
                width: "120",
            },
            {
                label: "所属区域",
                prop: "regionName",
            },
            {
                label: "代理机构名称",
                prop: "tenderAgentName",
            },
            {
                label: "开标时间",
                prop: "bidOpenTime",
            },
            {
                label: "认证状态",
                prop: "origionStatus",
            }
        ], // 人员解锁
        C_TENDER_AGENT_EVALUATE: [
            {
                label: "招标项目名称",
                prop: "tenderProjectName",
                width: "200",
            },
            {
                label: "标段（包）名称",
                prop: "bidSectionName",
                width: "200",
            },
            {
                label: "标段（包）编号",
                prop: "bidSectionCode",
                width: "120",
            },
            {
                label: "所属区域",
                prop: "regionName",
            },
            {
                label: "代理机构名称",
                prop: "tenderAgentName",
            }
        ], // 项目评价
        BAD_BEHAVIOR: [
            {
                label: "单位名称",
                prop: "legalName",
            },
            {
                label: "统一社会信用代码",
                prop: "legalCode",
            },
            {
                label: "单位类型",
                prop: "legalTypeName",
            },
            {
                label: "处罚日期",
                prop: "punishTime",
            },
            {
                label: "不良行为分类",
                prop: "badBehaviorName",
            },
            {
                label: "是否建设工程信息网注册",
                prop: "isRegisterName",
            },
            {
                label: "是否暂停招投标活动",
                prop: "isPastName",
            },
            {
                label: "是否建筑领域不良行为",
                prop: "isConstructionFieldName",
            }
        ], // 不良行为
        C_BAD_PERSONNEL: [
            {
                label: "公告标题",
                prop: "noticeName",
                width: "200",
            },
            {
                label: "人员姓名",
                prop: "personNames",
                width: "120",
            },
            {
                label: "是否对外发布",
                prop: "publishStatusName",
                width: "50",
            },
            {
                label: "发布状态",
                prop: "isPublishName",
                width: "50",
            },
            {
                label: "发布时间",
                prop: "publishTime",
                width: "100",
            },
            {
                label: "发布人",
                prop: "publishBy",
                width: "100",
            },
        ], // 人员不良行为
    }
}

export const htStatusRender = (text) => {
    const statusMap = {
        0: {
            label: "核验不通过",
            icon: STATUS_ICONS_MAP["status_error"],
            className: "status-red",
        },
        20: {
            label: "流程退回",
            icon: STATUS_ICONS_MAP["status_error"],
            className: "status-red",
        },
        66: {
            label: "标段延期",
            icon: STATUS_ICONS_MAP["status_no_check"],
            className: "status-orign",
        },
        8: {
            label: "核验通过",
            icon: STATUS_ICONS_MAP["status_success"],
            className: "status-green",
        },
    };
    if (statusMap[text]) {
        return statusMap[text];
    } else {
        return {
            label: "待核验",
            icon: STATUS_ICONS_MAP["status_no_check"],
            className: "status-orign",
        };
    }
};

export const ionCount = {
    redImg,
    yellowImg,
    greenImg
}
