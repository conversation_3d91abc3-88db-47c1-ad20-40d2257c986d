<template>
  <div id="projectInstruct" v-loading="isLoading">
    <el-form
      ref="instructRef"
      :model="instructForm"
      :rules="FORM_RULES"
      label-width="170px"
    >
      <div class="unify-title unify-title1">项目信息</div>
      <el-row  v-if="isShowInitiate">
        <el-col  :span="12">
          <el-form-item label="标段列表" >
            <el-select
              v-model="selectBidSectionGuid"
              :placeholder="请选择标段列表"
              filterable
              @change="selectBidGuidChange"
            >
              <el-option
                v-for="dict in bidList"
                :key="dict.bidSectionGuid"
                :label="dict.bidSectionName"
                :value="dict.bidSectionGuid"
              >
              <span style="float: left" :class="{isColor:dict.supervisionLevel == 2}">{{ dict.bidSectionName }}</span>
              <span v-if="dict.supervisionLevel == 2" style=" float: right; color: red; font-size: 13px;">
                电子监察
              </span>
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col
          v-for="(item, index) in FORM_CONTENT_BID"
          :key="index"
          :span="item.span"
        >
          <el-form-item :label="item.label" :prop="item.prop">
            <el-input
              v-model="instructForm[item.prop]"
              :disabled="item.disabled"
              :placeholder="item.placeholder"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <div class="unify-title unify-title1">行政监管事项</div>
      <el-row  v-if="isShowMonitor">
        <el-col  :span="12">
          <el-form-item label="电子监察" prop="monitorSectionGuid">
            <el-select
              v-model="instructForm.monitorSectionGuid"
              :placeholder="请选择电子监察"
              filterable
              multiple
              @change="selectMonitorChange"
              :disabled="isShowFul"
            >
              <el-option
                v-for="dict in monitorConList"
                :key="dict.rowGuid"
                :label="dict.supervisionContent"
                :value="dict.rowGuid"
              >
              <span style="float: left" >{{ dict.supervisionContent }}</span>
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col
          v-for="(item, index) in FORM_CONTENT_INFO"
          :key="index"
          :span="item.span"
        >
          <el-form-item :label="item.label" :prop="item.prop">
            <el-input
              v-if="item.type === 'INPUT'"
              v-model="instructForm[item.prop]"
              :disabled="item.disabled || isShowFul"
              :placeholder="item.placeholder"
            />
            <el-input
              v-if="item.type === 'TEXTAREA'"
              :rows="2"
              v-model="instructForm[item.prop]"
              type="textarea"
              :placeholder="item.placeholder"
              :disabled="item.disabled || isShowFul"
            />
            <el-select
              v-if="item.type === 'SELECT'"
              v-model="instructForm[item.prop]"
              :placeholder="item.placeholder"
              :disabled="item.disabled || isShowFul"
            >
              <el-option
                v-for="dict in item.optionList"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              ></el-option>
            </el-select>
            <el-date-picker
              style="width: 100%"
              v-if="item.type === 'DATE'"
              format="YYYY年MM月DD日 HH时mm分"
              value-format="YYYY-MM-DD HH:mm:ss"
              v-model="instructForm[item.prop]"
              type="datetime"
              :placeholder="item.placeholder"
              :disabled="item.disabled || isShowFul"
            />
            <el-upload
              v-if="item.type === 'UPLOAD' && !instructForm[item.prop].length"
              v-model:file-list="fileList"
              class="upload-demo"
              :action='uploadUrl'
              multiple
              :on-success="handleSuccess"
              :limit="1"
            >
              <el-button type="primary" v-if="isShowInitiate" >点击上传</el-button>
              <span v-else>暂无</span>
            </el-upload>
            <span
              v-show="item.type === 'UPLOAD' && instructForm[item.prop] != ''"
              v-for="(val, index) in fileList"
              :key="index"
            >
              <el-link :href="val.url" target="_blank">{{ val.attachmentFileName }}</el-link>
              <span v-if="isShowInitiate" class="my-span-delete" @click="handleDeleteImg"
                ><el-icon><CircleCloseFilled /></el-icon
              ></span>
            </span>
          </el-form-item>
        </el-col>
      </el-row>
      <div v-if="isShowFul && instructForm.feedDate">
        <div class="unify-title unify-title1">回复情况</div>
        <el-row>
          <el-col
            v-for="(item, index) in FORM_CONTENT_REPLY"
            :key="index"
            :span="item.span"
          >
            <el-form-item :label="item.label" :prop="item.prop">
              <el-input
                v-if="item.type === 'INPUT'"
                v-model="instructForm[item.prop]"
                :disabled="item.disabled"
                :placeholder="item.placeholder"
              />
              <el-input
                v-if="item.type === 'TEXTAREA'"
                :rows="2"
                v-model="instructForm[item.prop]"
                type="textarea"
                :disabled="item.disabled"
                :placeholder="item.placeholder"
              />
              <el-date-picker
                style="width: 100%"
                v-if="item.type === 'DATE'"
                format="YYYY年MM月DD日 HH时mm分"
                value-format="YYYY-MM-DD HH:mm:ss"
                v-model="instructForm[item.prop]"
                type="datetime"
                :disabled="item.disabled"
                :placeholder="item.placeholder"
              />
             <div  v-if="item.type === 'UPLOAD'">
              <div v-if="instructForm[item.prop].length">
                <span v-for="(val, index) in instructForm[item.prop]" :key="index">
                  <el-link :href="val.url" target="_blank">{{ val.attachmentFileName }}</el-link>
                </span>
              </div>
              <div v-else>暂无</div>
             </div>
            </el-form-item>
          </el-col>
        </el-row>
      </div>
      <el-dialog v-model="dialogTISHIProcessVisible" width="30%" class="dialogTISHIProcess" :show-close="false" :close-on-click-modal="false">
        <template #title>
          <div class="dialog-title">
            操作提示
          </div>
        </template>
        <br>
        <br>
        <div style="text-align: center;">
          <span style="color: #006400; font-weight: bold; margin-left: 32px;">监管指令已成功发出,是否需要进一步对代理机构进行评价操作？</span>
        </div>
        <br>
        <div class="btn-box">
          <el-button round type="primary" class="my-instruct-btn" @click="dialogTISHIVisible('1')">进行评价</el-button>
          <el-button round @click="dialogTISHIVisible('0')">暂不进行评价</el-button>
        </div>
        <br>
        <br>
      </el-dialog>
    </el-form>
    <div class="btn-box">

      <el-button
        type="primary"
        class="my-instruct-btn"
        round
        @click="handleProInstruct()"
        v-if="isShowInitiate"
      >
        发起指令</el-button
      >
      <el-button type="success" class="my-instruct-btn" round v-if="isShowComp" @click="handleCompleted()"
        >办 结</el-button
      >
      <el-button
        class="my-instruct-btn"
        round
        type="primary"
        plain
        @click="handleClose"
        >关 闭</el-button
      >
    </div>
  </div>
</template>

<script setup >
import {
  FORM_CONTENT_BID,
  FORM_CONTENT_INFO,
  FORM_CONTENT_REPLY,
  FORM_RULES,
} from "./formContent";
import { httpGet, httpPost } from "@wl-fe/http/dist";
import {ElMessage} from "element-plus";
const emit = defineEmits(["handleClose"]);
const dialogTISHIProcessVisible = ref(false);
const router = useRouter();
const props = defineProps({
  type: {
    type: String,
    default: "",
  },
  tenderProjectGuid: {
    type: String,
    default: "",
  },
  detailRowGuid: {
    type: String,
    default: "",
  },
});
const uploadUrl = `${import.meta.env.VITE_APP_RQUEST_API}sysUpload/uploadTenderFile`
const isLoading = ref(false);
const instructRef = ref();
const fileList = ref([]);
const data = reactive({
  instructForm: {
    sysAttachList: [],
    feedAttachList:[],
    monitorSectionGuid:""
  },
  bidInfo:{

  }
});
let selectBidSectionGuid = ref("");
let { instructForm ,bidInfo} = toRefs(data);
const isShowInitiate = computed(() => {
  return props.type == "1";
});
const isShowMonitor = computed(() => {
  console.log('instructForm',instructForm.value.supervisionLevel == 2,instructForm.value.illegalRecordGuids);
  return instructForm.value.supervisionLevel == 2 || instructForm.value.illegalRecordGuids;
});
const isShowFul = computed(() => {
  return props.type == "2";
});
const isShowComp = computed(() => {
  return props.type == "2" && instructForm.value.feedDate && instructForm.value.status != 4
});
const handleSuccess = (res, uploadFiles) => {
  console.log(res);
  fileList.value = [
    {
      id: res.data.id,
      attachmentFileName: res.data.attachmentFileName,
      url: res.data.url,
    },
  ];
  instructForm.value.sysAttachList = [res.data];
};
const handleDeleteImg = () => {
  fileList.value = [];
  instructForm.value.sysAttachList = [];
};
const handleProInstruct = async () => {
  if (!instructRef.value) return;
  await instructRef.value.validate(async (valid, fields) => {
    if (valid) {
      if(instructForm.value.supervisionLevel == 2){
        instructForm.value.illegalRecordGuids = instructForm.value.monitorSectionGuid.join(',')
      }
      console.log('instructForm',instructForm.value);
      isLoading.value = true;
      try {
        await httpPost('/inviteTender/asProjectPauseRestoration/save', instructForm.value);
        isLoading.value = false;
        ElMessage({
          message: "发起成功！",
          type: "success",
        });
        dialogTISHIProcessVisible.value = true;
        //handleClose('refresh')
      } catch (error) {
        isLoading.value = false;
        dialogTISHIProcessVisible.value = false;
      }

    }
  });
};
const handleClose = (refresh) => {
  emit("handleClose",refresh);
};
onMounted(() => {
  handleGetBidsectionList(props.tenderProjectGuid)
  console.log(6666666666666,props.tenderProjectGuid);
  if(props.type == "2"){
     handleGetDetail()
  }
 }
)
const handleGetDetail = async () => {
  isLoading.value = true;
  let data = await httpPost('/inviteTender/asProjectPauseRestoration/details',{rowGuid:props.detailRowGuid});
  isLoading.value = false
  console.log('data',data);
  instructForm.value = data;
  selectBidSectionGuid.value = instructForm.value.bidSectionGuid
  getSelectInfo()
  for (let key in instructForm.value) {
      // 如果属性的值为null，则删除该属性
      if (instructForm.value[key] === null) {
          delete instructForm.value[key];
      }
  }
  instructForm.value = Object.assign({}, bidInfo.value , instructForm.value);
  if(instructForm.value.sysAttachList.length > 0){
    fileList.value = instructForm.value.sysAttachList
  }
  if(instructForm.value.illegalRecordGuids){
    instructForm.value.monitorSectionGuid = instructForm.value.illegalRecordGuids.split(',')
    getSupervisionRecordList(instructForm.value.bidSectionGuid)
  }
}
const handleCompleted = async () => {
  isLoading.value = true;
  await httpPost('/inviteTender/asProjectPauseRestoration/completed',{rowGuid:props.detailRowGuid});
  isLoading.value = false;
  ElMessage({
    message: "办结成功！",
    type: "success",
  });
  handleClose('refresh')
}
const  bidList = ref([])
const monitorConList = ref([])
const bringLevel2ToFront = (arr) => {
  if (!Array.isArray(arr)) {
    console.error("Input must be an array.");
    return []; // 或者抛出错误
  }

  const level2Group = [];
  const otherGroup = [];

  for (const item of arr) {
    // 假设 item 是一个对象，且有 supervisionLevel 属性
    if (item && typeof item === 'object' && item.supervisionLevel == 2) {
      level2Group.push(item);
    } else {
      otherGroup.push(item);
    }
  }

  // 合并两个数组
  return [...level2Group, ...otherGroup];
}
const handleGetBidsectionList = async (val) =>{
  let info ={
    keyword: val ? val :''
  }
   let data = await httpPost('/inviteTender/bidSection/getBidsectionList', info);
   console.log('data',data);
   bidList.value = bringLevel2ToFront(data);
}
const selectBidGuidChange = async(val) => {
  console.log(val);
  selectBidSectionGuid.value = val;
  getSelectInfo()
  instructForm.value = bidInfo.value
  instructForm.value.sysAttachList = []
  console.log(instructForm.value,instructForm.value.supervisionLevel);
  if(instructForm.value.supervisionLevel == 2){
    getSupervisionRecordList(instructForm.value.bidSectionGuid)
  } else {
    monitorConList.value = []
  }
  instructForm.value.monitorSectionGuid = ''
};
const getSupervisionRecordList = async (id) => {
  let data = await httpPost('/api/projectManageHone/getSupervisionRecordList', {bidSectionGuid:id});
    console.log('data',data);
    monitorConList.value = data
}
const selectMonitorChange = async(val) => {
  
}
const getSelectInfo = () => {
  bidList.value.forEach((item) => {
    if (item.bidSectionGuid === selectBidSectionGuid.value) {
      bidInfo.value = item;
      bidInfo.value.sendType = 2 // 1招标人 2代理机构
    }
  })
}
const dialogTISHIVisible =  async (flag) => {

  if('1' === flag){//评价
    console.log(instructForm.value.bidSectionUnifiedCode)
    // window.open('http://************:5173/mg-admin/#/expertEvaluateStatisticalData/expertMainStore?my-app=%2F%23%2FbehaviorSummary&typeFrom=4&bidSectionUnifiedCode='+instructForm.value.bidSectionUnifiedCode,'_blank');
    // window.open('https://dlpj.lnzb.com:8888/#/behaviorSummary','_blank');
    await getJumpUrl()
  }
  dialogTISHIProcessVisible.value = false;
  emit("handleClose",'refresh');
};

const getJumpUrl = async () => {
  sessionStorage.setItem("evUrl", JSON.stringify('/singleLoginController/getAgentEvaluationBehaviorUrl?bidSectionUnifiedCode='+instructForm.value.bidSectionUnifiedCode));
  await router.push(`/expertEvaluateStatisticalData/expertMainStore`);
};

</script>

<style scoped lang="less">
#projectInstruct {
  width: 100%;
  height: auto;
  .btn-box {
    width: 100%;
    height: 50px;
    margin-top: 20px;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    .my-instruct-btn {
      width: 150px;
    }
  }
  .my-span-delete {
    cursor: pointer;
    cursor: pointer;
    display: inline-block;
    vertical-align: -webkit-baseline-middle;
    margin-left: 20px;
    &:hover {
      color: red;
    }
  }
  .dialog-title {
    text-align: center;
    color: white;
    background-color: #409eff;
    padding: 10px;
    width: 100%;
    box-sizing: border-box;
  }
  :deep(.el-dialog__header show-close) {
    display: none!important;
  }
}
.isColor{
  color: red;
}
</style>
<style>
 .dialogTISHIProcess {
   padding: 0!important;
 }
</style>

