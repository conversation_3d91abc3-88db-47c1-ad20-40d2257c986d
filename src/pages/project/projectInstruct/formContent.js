import { getOptionList } from './formOptionList'

export const FORM_CONTENT_BID = [
    {
        label: "项目编号",
        prop: "tenderProjectCode",
        type: "INPUT",
        placeholder: "请输入项目编号",
        span: 12,
        disabled: true
    },
    {
        label: "项目名称",
        prop: "tenderProjectName",
        type: "INPUT",
        placeholder: "请输入项目名称",
        span: 12,
        disabled: true
    },
    {
        label: "标段编号",
        prop: "bidSectionCode",
        type: "INPUT",
        placeholder: "请输入标段编号",
        span: 12,
        disabled: true
    },
    {
        label: "标段（包）名称",
        prop: "bidSectionName",
        type: "INPUT",
        placeholder: "请输入标段（包）名称",
        span: 12,
        disabled: true
    },
    {
        label: "标段唯一标识码",
        prop: "bidSectionUnifiedCode",
        type: "INPUT",
        placeholder: "请输入标段唯一标识码",
        span: 12,
        disabled: true
    },
    {
        label: "项目区域",
        prop: "regionName",
        type: "INPUT",
        placeholder: "请输入项目区域",
        span: 12,
        disabled: true
    },
    {
        label: "行业分类",
        prop: "qualifitionIndustry",
        type: "INPUT",
        placeholder: "请输入行业分类",
        span: 12,
        disabled: true
    },
    {
        label: "招标方式",
        prop: "tenderMode",
        type: "INPUT",
        placeholder: "请输入招标方式",
        span: 12,
        disabled: true
    },
    {
        label: "招标代理机构",
        prop: "tenderAgentName",
        type: "INPUT",
        placeholder: "请输入招标代理机构",
        span: 12,
        disabled: true
    }
]

export const FORM_CONTENT_INFO = [
    {
        label: "交易环节",
        prop: "tradeStep",
        type: "SELECT",
        placeholder: "请选择交易环节",
        span: 12,
        disabled: false,
        optionList: []
    },
    {
        label: "监管事项",
        prop: "approvalProject",
        type: "TEXTAREA",
        placeholder: "请输入监管事项",
        span: 24,
        disabled: false
    },
    {
        label: "法律、法规、政策依据",
        prop: "law",
        type: "TEXTAREA",
        placeholder: "请输入法律、法规、政策依据",
        span: 24,
        disabled: false
    },
    {
        label: "监管意见",
        prop: "content",
        type: "TEXTAREA",
        placeholder: "请输入监管意见",
        span: 24,
        disabled: false
    },
    {
        label: "是否暂停招标活动",
        prop: "isPast",
        type: "SELECT",
        placeholder: "请先选择是否暂停招标活动",
        span: 12,
        disabled: false,
        optionList: []
    },
    {
        label: "回复截止时间",
        prop: "handTime",
        type: "DATE",
        placeholder: "请选择回复截止时间",
        span: 12,
        disabled: false
    },
    {
        label: "监管单位",
        prop: "approveDeptName",
        type: "INPUT",
        placeholder: "请输入监管单位",
        span: 24,
        disabled: true
    },
    {
        label: "行政监管相关附件",
        prop: "sysAttachList",
        type: "UPLOAD",
        placeholder: "请上传",
        span: 24,
    }
]

// 然后在需要时更新optionList
async function updateOptionList() {
    const tradeStepOptions = await getOptionList('xzjgjyhj');
    const isPastOptions = await getOptionList('jgxtsf');

    // 假设你有一个方法来更新数组中的对象
    updateFormContentInfo(tradeStepOptions, 'tradeStep');
    updateFormContentInfo(isPastOptions, 'isPast');
}

function updateFormContentInfo(options, prop) {
    const index = FORM_CONTENT_INFO.findIndex(item => item.prop === prop);
    if (index !== -1) {
        FORM_CONTENT_INFO[index].optionList = options;
    }
}

updateOptionList()

export const FORM_CONTENT_REPLY = [
    {
        label: "回复人员",
        prop: "feedBacker",
        type: "INPUT",
        span: 12,
        disabled: true
    },
    {
        label: "回复时间",
        prop: "feedDate",
        type: "DATE",
        span: 12,
        disabled: true
    },
    {
        label: "回复内容",
        prop: "handFeedback",
        type: "TEXTAREA",
        span: 24,
        disabled: true
    },
    {
        label: "回复相关附件",
        prop: "feedAttachList",
        type: "UPLOAD",
        span: 24,
        disabled: true
    }
]
export const FORM_RULES = {
    tenderProjectCode: [{ required: true, message: "不能为空", trigger: "blur" }],
    tenderProjectName: [{ required: true, message: "不能为空", trigger: "blur" }],
    bidSectionCode: [{ required: true, message: "不能为空", trigger: "blur" }],
    bidSectionName: [{ required: true, message: "不能为空", trigger: "blur" }],
    bidSectionUnifiedCode: [{ required: true, message: "不能为空", trigger: "blur" }],
    regionName: [{ required: true, message: "不能为空", trigger: "blur" }],
    qualifitionIndustry: [{ required: true, message: "不能为空", trigger: "blur" }],
    tenderMode: [{ required: true, message: "不能为空", trigger: "blur" }],
    tenderAgentName: [{ required: true, message: "不能为空", trigger: "blur" }],
    tenderAgentContactPhone: [{ required: true, message: "不能为空", trigger: "blur" }],
    tenderAgentContact: [{ required: true, message: "不能为空", trigger: "blur" }],
    tradeStep: [{ required: true, message: "请选择交易环节", trigger: "blur" }],
    approvalProject: [{ required: true, message: "请输入监管事项", trigger: "blur" }],
    law: [{ required: true, message: "请输入法律、法规、政策依据", trigger: "blur" }],
    content: [{ required: true, message: "请输入监管意见", trigger: "blur" }],
    isPast: [{ required: true, message: "请先选择是否暂停招标活动", trigger: "blur" }],
    handTime: [{ required: true, message: "请选择回复截止时间", trigger: "blur" }],
    approveDeptName: [{ required: true, message: "请输入监管单位", trigger: "blur" }],
    monitorSectionGuid: [{ required: true, message: "请选择电子监察", trigger: "blur" }],
}
