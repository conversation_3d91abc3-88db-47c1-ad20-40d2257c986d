<template>
  <div id="perject-detail" v-loading="isLoading">
    <div class="project-header">
      <div class="header-box">
        <div class="header-text-s unify-title5">
          <span class="text-t">招标项目名称</span>
          <span>{{ name }}</span>
        </div>
        <div style="margin-left: 10%" class="header-text-s unify-title5">
          <span class="text-t">招标项目编号</span>
          <span>{{ code }}</span>
        </div>
      </div>
      <el-button @click="handleBack" class="back-btn" type="primary" plain round
        >返回</el-button
      >
    </div>
    <div class="project-content">
      <!-- <div class="tip-box">
        <div class="tip-header">操作指南</div>
        <div>
          <span class="top-color"
            ><el-icon><CaretRight /></el-icon>【双击】{{ countStr }}</span
          >
          可展示/收起流程节点
        </div>
        <div>
          <span class="top-color"
            ><el-icon><CaretRight /></el-icon>【点击】</span
          >可查看流程节点具体详情
        </div>
        <div>
          <span class="top-color"
            ><el-icon><CaretRight /></el-icon>【鼠标滚轮】</span
          >可控制流程缩放大小
        </div>
        <div>
          <span class="top-color"
            ><el-icon><CaretRight /></el-icon>【按住左键】</span
          >可拖动流程位置
        </div>
      </div> -->
      <div class="bidder-select">
        <span class="se-label">标段名称：</span>
        <el-select
          v-model="bidSectionGuid"
          filterable
          style="width: 65%"
          placeholder="选择标段名称"
          @change="handleBidName"
        >
          <el-option
            v-for="item in bidList"
            :key="item.bidSectionGuid"
            :label="item.bidSectionName"
            :value="item.bidSectionGuid"
          />
        </el-select>
      </div>
      <div class="tip-box-new" :class="{tipBoxNo:isClick}">
        <div class="tip-content">
          <span class="my-img"></span>
          <span class="tip-title">双击：</span>
          <span class="tip-text">可展示/收起流程节点</span>
        </div>
        <div class="tip-content">
          <span class="my-img my-img1"></span>
          <span class="tip-title">单击：</span>
          <span class="tip-text">可查看流程节点具体详情</span>
        </div>
        <div class="tip-content">
          <span class="my-img my-img2"></span>
          <span class="tip-title">鼠标滚轮：</span>
          <span class="tip-text">可控制流程缩放大小</span>
        </div>
        <div class="tip-content">
          <span class="my-img my-img3"></span>
          <span class="tip-title">按住左键：</span>
          <span class="tip-text">可拖动流程位置</span>
        </div>
        <span class="my-su" @click="handleNone">我知道了</span>
      </div>
      <perss ref="press" :dataArray="dataArray"></perss>
    </div>
  </div>
</template>
<script setup>
import { useRouter, useRoute } from "vue-router";
import { httpGet, httpPost } from "@wl-fe/http/dist";
import perss from "./perss.vue";

const bidSectionGuid = ref("");
const isClick = ref(false);
const bidList = ref([]);

// 提取环境变量
const baseImageUrl = import.meta.env.VITE_MG_ADMIN_JS || '';


let dataArray = [
  {
    id: 111,
    name: "项目登记",
    label: {
      formatter: ["{first|项目登记}"].join("\n"),
      rich: {
        first: {
          // backgroundColor: '#3AC082',
          color: "#fff",
          align: "center",
          width: 116,
          height: 30,
          borderRadius: 5,
          backgroundColor: {
            image: baseImageUrl + "echartsBg.png"
          },
        },
      },
    },
    children: [
      {
        id: 222,
        name: "招标项目",
        value: "你好",
        values: "你好",
        label: {
          formatter: ["{first|招标项目}"].join("\n"),
          rich: {
            first: {
              // backgroundColor: '#3AC082',
              color: "#fff",
              align: "center",
              width: 116,
              height: 30,
              borderRadius: 5,
              backgroundColor: {
                image: baseImageUrl + "echartsBg.png"
              },
            },
          },
        },
        children: [
          {
            id: 333,
            name: "招标委托",
            value: "你好",
            values: "你好",
            label: {
              formatter: ["{first|招标委托}"].join("\n"),
              rich: {
                first: {
                  // backgroundColor: '#3AC082',
                  color: "#fff",
                  align: "center",
                  width: 116,
                  height: 30,
                  borderRadius: 5,
                  backgroundColor: {
                    image: baseImageUrl + "echartsBg.png"
                  },
                },
              },
            },
            children: [
              {
                id: 444,
                name: "招标备案",
                value: "你好",
                values: "你好",
                children: [
                  {
                    id: 5551,
                    name: "招标公告",
                    value: "你好",
                    values: "你好",
                    children: [
                      {
                        id: 555,
                        name: "招标文件",
                        children: [
                          {
                            id: 555,
                            name: "澄清文件",
                            children: [
                              {
                                id: 555,
                                name: "开标信息",
                                children: [
                                  {
                                    id: 555,
                                    name: "评标信息",
                                    children: [
                                      {
                                        id: 555,
                                        name: "中标候选人公示",
                                        children: [
                                          {
                                            id: 555,
                                            name: "清表、定标",
                                            children: [
                                              {
                                                id: 555,
                                                name: "中标结果公示",
                                                children: [
                                                  {
                                                    id: 555,
                                                    name: "中标通知书",
                                                    children: [
                                                      {
                                                        id: 555,
                                                        name: "书面报告",
                                                        children: [
                                                          {
                                                            id: 555,
                                                            name: "合同公示",
                                                            children: [
                                                              {
                                                                id: 555,
                                                                name: "特殊事项",
                                                              },
                                                            ],
                                                          },
                                                        ],
                                                      },
                                                    ],
                                                  },
                                                ],
                                              },
                                            ],
                                          },
                                        ],
                                      },
                                    ],
                                  },
                                ],
                              },
                            ],
                          },
                        ],
                      },
                      {
                        id: 555,
                        name: "招标文件1",
                      },
                      {
                        id: 555,
                        name: "招标文件1",
                      },
                      {
                        id: 555,
                        name: "招标文件1",
                      },
                      {
                        id: 555,
                        name: "招标文件1",
                      },
                      {
                        id: 555,
                        name: "招标文件1",
                      },
                      {
                        id: 555,
                        name: "招标文件1",
                      },
                      {
                        id: 555,
                        name: "招标文件1",
                      },
                      {
                        id: 555,
                        name: "招标文件1",
                      },
                      {
                        id: 555,
                        name: "招标文件1",
                      },
                    ],
                  },
                  {
                    id: 5552,
                    name: "招标公告",
                    children: [
                      {
                        id: 555,
                        name: "招标文件1",
                      },
                    ],
                  },
                  {
                    id: 5553,
                    name: "招标公告2",
                    children: [
                      {
                        id: 555,
                        name: "招标文件1",
                      },
                    ],
                  },
                  {
                    id: 5554,
                    name: "招标公告3",
                    children: [
                      {
                        id: 555,
                        name: "招标文件1",
                      },
                    ],
                  },
                ],
              },
            ],
          },
        ],
      },
    ],
  },
];
const handleNone = () =>{
  isClick.value = true;
}
const handleBidName = (val) => {
  console.log(val);
  bidSectionGuid.value = val;
  handleGetProjectFlowchart();
};
const router = useRouter();
const route = useRoute();
let { query } = toRefs(route);
const { nodeCode, parentNodeCode, projectGuid, tenderProjectGuid, name, code } =
  query.value;
let isLoading = ref(false);
onMounted(() => {
  handleGetProjectFlowchart();
  handleGetBidSectionList();
});
const press = ref();
const handleGetProjectFlowchart = async () => {
  isLoading.value = true;
  let info = { tenderProjectGuid: tenderProjectGuid };
  if (bidSectionGuid.value) {
    info.bidSectionGuid = bidSectionGuid.value;
  }
  const data = await httpPost("/api/projectManageHone/projectFlowchart", info);
  isLoading.value = false;
  console.log(data);
  handleChangeData(data)
  press.value.handleInit(data);
};
const handleChangeData = (data) => {
  data.forEach((ele) => {
    if (
      ele.name == "项目登记" ||
      ele.name == "招标项目" ||
      ele.name == "项目委托" ||
      ele.name == "招标备案"
    ) {
      let img = {
        '项目登记':baseImageUrl +"xmdj.png",
        '招标项目':baseImageUrl +"zbxm.png",
        '项目委托':baseImageUrl +"xmwt.png",
        '招标备案':baseImageUrl +"zbba.png",
      }
      ele.label = {
        formatter:  () => {
            let nameStr = ele.name
            if (nameStr.length >= 5) {
              nameStr = nameStr.substring(0, 4) + ".."; //如果超出6个字符，显示5个+..
            }
            if(ele.count > 1){
                // return ele.name + "(" + ele.count + ")";
                return `{first|${nameStr}}{number|(${ele.count})}`
            }
            return  `{first|}`
          },
        rich: {
          first: {
            // backgroundColor: '#3AC082',
            color: "#000",
            align: "right",
             padding:[6,20,10,0],
            width: 58,
            height: 48,
            lineHeight: 25,
            fontSize: 16, //文字大小
            backgroundColor: {
              image: img[ele.name] //  require(url("/projectManagement/h-tip.png")) //  import.meta.env.VITE_MG_ADMIN_JS +"echartsBg2.png",
            },
          },
          number:{
              fontSize:16,
              color:'#FF9933',
              padding:[3,0,0,5],
              align:'center',
            }
        },
      };
    }
    // else if( ele.name == "特殊情况"){
    //   ele.label = {
    //     formatter: [`{first|${ele.name}}`].join("\n"),
    //     rich: {
    //       first: {
    //         // backgroundColor: '#3AC082',
    //         color: "#000",
    //         align: "right",
    //          padding:[6,20,10,0],
    //         width: 100,
    //         height: 30,
    //         lineHeight: 25,
    //         fontSize: 16, //文字大小
    //         backgroundColor: {
    //           image: import.meta.env.VITE_MG_ADMIN_JS +"echartsBg3.png",
    //         },
    //       },
    //     },
    //   };
    // }
    else if( ele.supervisionLevel === 1){
      ele.label = {
        formatter: [`{first|${ele.name}}`].join("\n"),
        rich: {
          first: {
            // backgroundColor: '#3AC082',
            color: "#000",
            align: "right",
             padding:[6,20,10,0],
            width: 100,
            height: 30,
            lineHeight: 25,
            fontSize: 16, //文字大小
            backgroundColor: {
              image: baseImageUrl + "echartsBg4.png"
            },
          },
        },
      };
    } else if(  ele.supervisionLevel === 2){
      ele.label = {
        formatter:  () => {
            let nameStr = ele.name
            if (nameStr.length >= 5) {
              nameStr = nameStr.substring(0, 4) + ".."; //如果超出6个字符，显示5个+..
            }
            if(ele.count > 1){

                return [
                  `{sec|${nameStr}}`,
                  `{number|共(${ele.count})条}`,
                ].join("\n");
            }
            return `{first|${nameStr}}`
          },
        rich: {
          first: {
            // backgroundColor: '#3AC082',
            color: "#000",
            align: "right",
             padding:[6,20,10,10],
            width: 100,
            height: 30,
            lineHeight: 25,
            fontSize: 16, //文字大小
            backgroundColor: {
              image: baseImageUrl + "echartsBg5.png"
            },
          },
        },
      };
    } else if(  ele.supervisionLevel === 9){
        ele.label = {
            formatter:  () => {
                let nameStr = ele.name
                if (nameStr.length >= 5) {
                    nameStr = nameStr.substring(0, 4) + ".."; //如果超出6个字符，显示5个+..
                }
                if(ele.count > 1){

                    return [
                        `{sec|${nameStr}}`,
                        `{number|共(${ele.count})条}`,
                    ].join("\n");
                }
                return `{first|${nameStr}}`
            },
            rich: {
                first: {
                    // backgroundColor: '#3AC082',
                    color: "#000",
                    align: "right",
                    padding:[6,20,10,10],
                    width: 100,
                    height: 30,
                    lineHeight: 25,
                    fontSize: 16, //文字大小
                    backgroundColor: {
                        image: baseImageUrl + "echartsBg6.png"
                    },
                },
            },
        };
    } else {
      ele.label = {
        formatter:  () => {
            let nameStr = ele.name
            if (nameStr.length >= 5) {
              nameStr = nameStr.substring(0, 4) + ".."; //如果超出6个字符，显示5个+..
            }
            if(ele.count > 1){

                return [
                  `{sec|${nameStr}}`,
                  `{number|共(${ele.count})条}`,
                ].join("\n");
            }
            return `{first|${nameStr}}`
          },
        rich: {
          first: {
            // backgroundColor: '#3AC082',
            color: "#000",
            align: "right",
            padding:[6,20,10,0],
            width: 110,
            height: 30,
            lineHeight: 25,
            fontSize: 16, //文字大小
            backgroundColor: {
              image: baseImageUrl + "echartsBg2.png"
            },
          },
          sec: {
            // backgroundColor: '#3AC082',
            color: "#000",
            align: "right",
            padding:[-30,30,-10,0],
            width: 120,
            height: 100,
            lineHeight: 25,
            fontSize: 16, //文字大小
            backgroundColor: {
              image: baseImageUrl + "echartsBg2.png"
            },
          },
          number:{
              width: 120,
              fontSize:16,
              color:'#FF9933',
              padding:[-10,40,20,0],
              align:'right',
            }
        },
      };
    }
    if(ele.children){
      handleChangeData(ele.children)
    }
  });
};
const handleGetBidSectionList = async () => {
  const data = await httpPost("api/projectManageHone/getBidsectionList", {
    tenderProjectGuid: tenderProjectGuid,
  });
  bidList.value = data;
  bidList.value.unshift({
    bidSectionGuid: "",
    bidSectionName: "全部",
  });
  console.log(data);
};
const handleBack = () => {
  router.push({
    path: "/projectStatisticaData/projectList",
    query: {
      nodeCode: nodeCode,
      parentNodeCode: parentNodeCode,
      isShowLeft: false,
    },
  });
};
</script>
<style scoped lang="less">
#perject-detail {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  .project-header {
    width: 100%;
    height: 6%;
    background-color: #fff;
    border-radius: 5px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    font-weight: bold;
    font-size: 16px;
    .header-box {
      display: flex;
      flex-direction: row;
      align-items: center;
      width: 80%;
      .header-lan {
        height: 30px;
        width: 8px;
        background: #438bfa;
        border-radius: 5px;
        margin-left: 21px;
      }
      .header-text-s {
        margin-left: 12px;
        font-size: 20px;
        .text-t {
          margin-right: 10px;
        }
      }
    }
    .back-btn {
      width: 90px;
      margin-right: 20px;
    }
  }
  .project-content {
    width: 100%;
    height: 93%;
    border-radius: 5px;
    // display: flex;
    // flex-direction: row;
    // justify-content: space-between;
    // align-items: center;
    background: #fff;
    position: relative;
    background: url("/projectManagement/press-bg.jpg") no-repeat;
    background-size: 100% 100%;
    // overflow: scroll;
    .tip-box {
      .tip-header {
        text-align: center;
        font-size: 18px;
      }
      width: 280px;
      height: auto;
      padding: 10px;
      // border: 1px solid #ccc;
      position: absolute;
      top: 10px;
      left: 10px;
      background: #3b85f8;
      line-height: 30px;
      border-radius: 5px 30px 5px 30px;
      color: #fff;
      z-index: 5;
      .top-color {
        color: #ffcc00;
      }
    }
    .tip-box-new{
      // width: 280px;
      padding: 10px;
      position: absolute;
      bottom: 10px;
      // left: 13%;
      border-radius: 30px;
      color: #000;
      z-index: 5;
      height: 60px;
      display: flex;
      width: 100%;
      background: #efefef;
      transition: all 1s;
      align-items: center;
      justify-content: space-around;
      .tip-content{
        height: 40px;
        line-height: 40px;
        // width: 100px;
        border-radius: 30px;
        background: #fff;
        display: inline-block;
        margin-right:30px;
        span{
          display: inline-block;
        }
        .tip-title{
          line-height: 40px;
          vertical-align: top;
          font-size: 18px;
          font-weight: 600;
          margin-left: 10px;
          color: #82828e;
        }
        .tip-text{
          line-height: 40px;
          vertical-align: top;
          margin-right: 10px;
          color:#a8abb2;
          font-size: 14px;
        }
        .my-img{
          border-radius: 30px;
          width: 40px;
          height: 40px;
          background: url("/projectManagement/svg/sj.svg") no-repeat;
          background-size: 200%;
          background-position: center;
          background-position-x: -15%;
          background-position-y: 204%;
          box-shadow: 0 2px 4px rgba(0, 0, 0, .12), 0 0 6px rgba(0, 0, 0, .04)
        }
        .my-img1{
          background: url("/projectManagement/svg/dj.svg") no-repeat;
          background-size: 200%;
          background-position: center;
          background-position-x: -15%;
          background-position-y: 204%;
        }
        .my-img2{
          background: url("/projectManagement/svg/gl.svg") no-repeat;
          background-size: 72%;
          background-position: center;
        }
        .my-img3{
          background: url("/projectManagement/svg/zj.svg") no-repeat;
          background-size: 81%;
          background-position: center;
        }
      }
      .my-su{
        display: inline-block;
        height: 40px;
        line-height: 40px;
        vertical-align: top;
        cursor: pointer;
        color: #3366ff;
      }
    }
    .tipBoxNo{
      bottom: -30%;
    }
    .bidder-select {
      position: absolute;
      top: 10px;
      right: 36%;
      z-index: 5;
      display: flex;
      align-items: center;
      width: 30%;
      height: 60px;
      background: #efefef;
      justify-content: center;
      border-radius: 80px;
      color: #000;
      .se-label {
        width: 18%;
      }
      :deep( .el-select__wrapper.is-filterable) {
          border-radius: 20px;
      }
    }
  }
}

.header-title {
  height: 32px;
  font-weight: 600;
  line-height: 32px;
  margin-left: 5px;
  padding-left: 5px;
  position: relative;
  color: #000;
  z-index: 0;
}
</style>
