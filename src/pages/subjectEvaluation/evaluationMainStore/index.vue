<!-- 远程监管界面 -->
<template>
  <div class="mic-box" v-loading="isLoading" element-loading-text="">
    <micro-app
      iframe
      name="my-app"
      :url="url"
      class="mic-box-app"
      :data="data"
      @unmount="handleUnMounted"
      @mounted="handleonMounted"
    />
  </div>
</template>
  
  <script setup>
import { httpGet } from "@wl-fe/http";
import { ElMessage } from "element-plus";
let isLoading = ref(true);
let url = ref("");
let data = ref({});
function getParamsAsObject(url) {
  const paramsStr = url.split("?")[1];
  if (!paramsStr) {
    return {};
  }
  return paramsStr.split("&").reduce((acc, param) => {
    const [key, value] = param.split("=");
    acc[key] = decodeURIComponent(value);
    return acc;
  }, {});
}
const getJumpUrl = async (apiUrl) => {
  const result = await httpGet(apiUrl, null, {
    transferResult: (result) => result,
  });
  if (!result) return;
  url.value = result; //moduleMap[`${pathStr.replace(/^\//, "")}`].localUrl;
  data.value = { params: getParamsAsObject(result) };
  console.log("result", result, url.value, data.value);
};
const handleUnMounted = () => {
  document.title = "辽宁省房屋建筑和市政工程招标投标智能监管系统";
};
const handleonMounted = () => {
  isLoading.value = false;
};
onMounted(() => {
   const url =  JSON.parse(sessionStorage.getItem("evUrl"))
   getJumpUrl(url);
});
</script>
<style lang="less" scoped>
.mic-box {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  .mic-box-app {
    width: 100%;
    height: 100%;
    overflow: hidden;
  }
}
</style>