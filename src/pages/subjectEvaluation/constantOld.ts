import { ColumnItemType } from "@/type"
import { MODULE_TYPE_SUBJECT_EVALUATION,MODULE_TYPE_AGENT_EVALUATION } from "@/constant/module"
import { summaryWrapperRender } from "@/models/searchList/constant"
const customStyle = {
    labelStyle: {
        marginTop: '6%',
        fontSize:"24px"
    },
    countStyle: {
        marginBottom: "6%"
    }
}




export const renderSummaryColumns = (): ColumnItemType[] => {
    return [
        {
            title: "主体评价",
            singlePointUrl:"/singleLoginController/getEvaluationUrl",
            api:"/subsyStemCallController/getExpertEvaluateList",
            index: 4,
            dataIndex: "4",
            columnsRender:renderTableColumns4,

        },
        {
            title: "代理评价",
            singlePointUrl:"/singleLoginController/getAgentEvaluationUrl",
            api:"/subsyStemCallController/getAgentEvaluateList",
            index: 3,
            dataIndex: "3",
            columnsRender:renderTableColumns3,
        }
    ].map((item) => {
        return {
            ...item,
            styles:customStyle,
            render: (text: any, record: any, index: number, column: ColumnItemType) => summaryWrapperRender(text, record, index, column, `${MODULE_TYPE_SUBJECT_EVALUATION}/summary/${index + 1}.png`)
        } as ColumnItemType
    })
}


export const renderTableColumns3 = (): ColumnItemType[] => {
    return [

        {
            title: '统一社会信用代码',
            dataIndex: 'orgNum',
        },
        {
            title: '单位名称',
            dataIndex: 'danweiName',
        },
        {
            title: '2024年度得分',
            dataIndex: 'lastScore',
        },
        {
            title: "动态得分",
            dataIndex: "actScore"
        }
    ]
}
export const renderTableColumns4 = (): ColumnItemType[] => {
    return [

        {
            title: '标段名称',
            dataIndex: 'bidSectionName',
        },
        {
            title: '标段唯一标识码',
            dataIndex: 'bidSectionUnifiedCode',
        },
        {
            title: '招标人',
            dataIndex: 'tendererName',
        },
        {
            title: "监督部门",
            dataIndex: "approveDeptName"
        }
        ,
        {
            title: "代理机构",
            dataIndex: "tenderAgentName"
        }
        ,
        {
            title: "核验状态",
            dataIndex: "hyStatus",
            width:160
        }
    ]
}
