<!-- 远程监管界面 -->
<template>
  <PageWrapper style="padding: 0px">
    <div class="bid">
      <div
        v-for="item in bidModuleList"
        :key="item.title"
        class="item"
        :style="{ background: `url(${item.bg})`, backgroundSize: '100% 100%' }"
        @click="() => getJumpUrl(item)"
      ></div>
    </div>
  </PageWrapper>
</template>

<script setup>
import { bidModuleList } from "./constant.ts";
import { httpGet } from "@wl-fe/http";
import { ElMessage } from "element-plus";
const router = useRouter();
const getJumpUrl = async (item) => {
  if (item.title === "招标人评价" || item.title === "投标人评价") {
    ElMessage.error("暂未开放！");
    return;
  }
  sessionStorage.setItem("evUrl", JSON.stringify(item.url));
  router.push(`/expertEvaluateStatisticalData/expertMainStore`);
};
</script>

<style lang="less">
.bid {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: url("/bid/bg.png") no-repeat;
  background-size: 100% 100%;

  .item {
    margin: 0px 62px;
    width: 368px;
    height: 407px;
    position: relative;
    display: flex;
    justify-content: center;
    cursor: pointer;
    background-size: 100% 100%;
    transition: all 0.3s;
    &:hover {
      transform: scale(1.05);
    }
    .text {
      font-size: 26px;
      color: #fff;
      font-weight: bold;
      display: flex;
      flex-direction: column;
      position: absolute;
      bottom: 13%;
      margin: 0px auto;
      letter-spacing: 4px;
      line-height: 47px;
      .title {
        display: flex;
        justify-content: space-between;
      }
    }
  }
}
</style>