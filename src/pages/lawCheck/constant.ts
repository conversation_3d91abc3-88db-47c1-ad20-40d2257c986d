import { ColumnItemType } from "@/type"
import { STATUS_ICONS_MAP } from "@/constant/icon"
import { getSummaryIconCommonIndex } from "@/utils"
import { statusRender, summaryWrapperRender } from "@/models/searchList/constant"
import { StatusType } from "@/models/searchList/type";

const customStyles = {
    labelStyle: {
        marginTop: "6%",
        textAlign: "center",
        paddingLeft: "0px",
        fontSize: "18px"
    },
    countStyle: {
        marginBottom: "12%",
        textAlign: "center",
        paddingLeft: "0px"
    },
};
export const renderSummaryColumns = (): ColumnItemType[] => {
    return [
        {
            title: "处罚人员数量",
            dataIndex: "cfrysl"
        },
        {
            title: "行政处罚标段",
            dataIndex: "xzcfbd"
        },
        {
            title: "发现问题标段",
            dataIndex: "fxwtbd"
        },
        {
            title: "处罚单位数量",
            dataIndex: "cfdwsl"
        },
        {
            title: "信用中国曝光",
            dataIndex: "xyzgbg"
        }
        ,
        {
            title: "网站获取标段",
            dataIndex: "wzhqbd"
        }
        ,
        {
            title: "自行添加标段",
            dataIndex: "zxtjbd"
        }
        ,
        {
            title: "已完成自查标段",
            dataIndex: "ywczcbd"
        }
    ].map((item) => {
        return {
            ...item,
            styles: customStyles,
            render: (text: any, record: any, index: number, column: ColumnItemType) => summaryWrapperRender(text, record, index, column, `/${import.meta.env.VITE_PROXY_SECMENU}/components/summary/${getSummaryIconCommonIndex(index)}.png`)


        } as ColumnItemType
    })
}

const zgqkStatusRender = (text: string) => {
    const statusMap: StatusType = {
        1: {
            label: "未整改",
            icon: STATUS_ICONS_MAP['status_error']
        },
        2: {
            label: "整改中",
            icon: STATUS_ICONS_MAP['status_ing']

        },
        3: {
            label: "已整改",
            icon: STATUS_ICONS_MAP['status_success']
        }
    }
    return statusRender(statusMap, text)
}


export const renderTableColumns = (): ColumnItemType[] => {
    return [
        {
            title: '标段唯一标识码',
            dataIndex: 'uniqueId',
        },
        {
            title: '标段名称',
            dataIndex: 'bidSectionName',
        },


        {
            title: '存在问题',
            dataIndex: 'status',
            width: 100
        },
        {
            title: '整改情况',
            dataIndex: 'zgqk',
            width: 160,
            render: zgqkStatusRender
        },
        {
            title: '招标人',
            dataIndex: 'tendererName',
        },
        {
            title: '监管部门',
            dataIndex: 'supervisoryAuthorityName',
        },
    ]
}