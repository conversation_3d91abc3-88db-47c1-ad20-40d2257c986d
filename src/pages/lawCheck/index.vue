<!-- 执法检查界面 -->
<script setup>
import SearchList from "@/models/searchList/index.vue";
import { renderSummaryColumns, renderTableColumns } from "./constant";
</script>
<template>
  <PageWrapper>
    <SearchList
      :summary="{
        key: 'statsData',
        columns: renderSummaryColumns(),
      }"
      :table="{
        singlePointUrl: '/singleLoginController/getDirectReportLoginUrl',
        api: '/subsyStemCallController/getDirectReportList',
        label: '检查列表',
        columns: renderTableColumns(),
      }"
    />
  </PageWrapper>
</template>
