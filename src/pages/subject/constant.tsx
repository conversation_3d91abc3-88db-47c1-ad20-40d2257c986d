import { ColumnItemType } from "@/type"




export const renderSummaryColumns = (): ColumnItemType[] => {
    return [
        {
            title: "待核验列表",
            // singlePointUrl:"/subsyStemCallController/getAuditCompanyList",
            api:"/subsyStemCallController/getAuditCompanyList",
            dataIndex: "4",
            columnsRender:renderTableColumns4,

        },
        {
            title: "企业列表",
            // singlePointUrl:"/singleLoginController/getAgentEvaluationUrl",
            api:"/subsyStemCallController/getCompanyList",
            dataIndex: "3",
            columnsRender:renderTableColumns3,
        }   
    ]
}


export const renderTableColumns3 = (): ColumnItemType[] => {
    return [

        {
            title: '单位名称',
            dataIndex: 'companyName',
        },
        {
            title: '统一社会信用代码',
            dataIndex: 'legalCode',
        }
    ]
}
export const renderTableColumns4 = (): ColumnItemType[] => {
    return [

        {
            title: '企业类型',
            dataIndex: 'classify',
        },
        {
            title: '单位名称',
            dataIndex: 'companyName',
        },
        {
            title: '统一社会信用代码',
            dataIndex: 'legalCode',
        },
        {
            title: "监管部门",
            dataIndex: "dept_name"
        }
        ,
        {
            title: "核验类型",
            dataIndex: "type"
        }
        
    ]
}
