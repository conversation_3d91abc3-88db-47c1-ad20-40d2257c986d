<!-- 主体信息页面 -->
<script setup>
import SearchList from "@/models/searchList/index.vue";
import { renderSummaryColumns } from "./constant";
import { labelExtraRender } from "@/models/searchList/constant";
const columns = renderSummaryColumns();
const activeKey = ref(columns[0].dataIndex);
const columnItem = computed(() =>
  columns.find((item) => item.dataIndex === activeKey.value)
);
</script>
<template>
  <PageWrapper>
    <SearchList
      :key="activeKey"
      searchPlaceholder='请输入单位名称或统一社会信用代码'
      :table="{
        labelExtraRender: () =>
          labelExtraRender(columns, activeKey, (value) => {
            activeKey = value;
          }),

        api: columnItem.api,
        singlePointUrl: '/singleLoginController/getCompanybasicUrl',
        label: '主体列表',
        columns: columnItem.columnsRender(),
      }"
    />
  </PageWrapper>
</template>

