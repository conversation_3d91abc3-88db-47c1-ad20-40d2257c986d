<!-- 投标集成页面 -->
<script setup>
import SearchList from "@/models/searchList/index.vue";
import { renderSummaryColumns, renderTableColumns } from "./constant.ts"
import './index.less'

</script>
<template>
  <PageWrapper>
    <SearchList
      :summary="{
        key: 'statsData',
        columns: renderSummaryColumns(),
      }"
      :table="{
        singlePointUrl: '/singleLoginController/getblindboxCasLoginUrl',
        api: '/subsyStemCallController/getBlindboxList',
        label: '标段列表',
        columns: renderTableColumns(),
      }"
    />
  </PageWrapper>
</template>

<style scoped lang="less"></style>
