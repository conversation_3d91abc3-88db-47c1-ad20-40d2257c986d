import { ColumnItemType } from "@/type"
import { MODULE_TYPE_TENDER } from "@/constant/module"
import { STATUS_ICONS_MAP } from "@/constant/icon"
import { statusRender, summaryWrapperRender } from "@/models/searchList/constant"
import { StatusType } from "@/models/searchList/type"


const customStyle = {
    labelStyle: {
        marginTop: '6%'
    },
    countStyle: {
        marginBottom: "6%"
    }
}


export const renderSummaryColumns = (): ColumnItemType[] => {
    return [
        {
            title: "全部",
            dataIndex: "allNum",
            styles: customStyle
        },
        {
            title: "已结束",
            dataIndex: "ywcNum",
            styles: customStyle

        },
        {
            title: "进行中",
            dataIndex: "zzjxNum",
            styles: customStyle
        }
    ]
        .map((item) => {
            return {
                ...item,
                render: (text: any, record: any, index: number, column: ColumnItemType) => summaryWrapperRender(text, record, index, column, `${MODULE_TYPE_TENDER}/summary/${index + 1}.png`)
            }
        }) as ColumnItemType[]

}

const bidStatusRender = (text: string) => {
    const statusMap: StatusType = {
        "进行中": {
            label: "进行中",
            icon: STATUS_ICONS_MAP['status_ing']
        },
        "已完成": {
            label: "已完成",
            icon: STATUS_ICONS_MAP['status_success']

        }
    }
    return statusRender(statusMap, text)

}

export const renderTableColumns = (): ColumnItemType[] => {
    return [
        {
            title: '标段唯一标识码',
            dataIndex: 'bidSingleCode',
            width: 300
        },
        {
            title: '标段名称',
            dataIndex: 'bidSectionName',
            width: 500
        },
        {
            title: '招标人',
            dataIndex: 'tendereeName',
        },
        {
            title: '项目地区',
            dataIndex: 'cityName',
            width: 150
        },
        {
            title: '项目情况',
            width: 100,
            dataIndex: 'bidStatus',
            render: bidStatusRender
        }
    ]
}