
export const listUrl = "/inviteTender/tenderPlan/list"
export const singlePointUrl = '/singleLoginController/getDecisionMakingUrl'


export const renderSummaryColumns = () => {
    return [
        {
            title: "自主决策",
            dataIndex: "performance",
            columns: [
                {
                    title: '招标人名称',
                    dataIndex: 'tendererName',
                },
                {
                    title: '项目名称',
                    dataIndex: 'projectName',
                    width: 300
                },
                {
                    title: '建设地点',
                    dataIndex: 'address',
                    width: 200
                },
                {
                    title: '建设规模',
                    dataIndex: 'constructionScale',
                    width: 300
                },
                {
                    title: '项目总投资（万元）',
                    width: 100,
                    dataIndex: 'totalMoney',
                },
                {
                    title: '资金来源',
                    dataIndex: 'fundingSource',
                    width: 120
                },
                {
                    title: '招标计划发布日期',
                    dataIndex: 'submitTime',
                    width: 110
                },
                {
                    title: '是否对外发布',
                    dataIndex: 'isPublished',
                    width: 70,
                    isSelf: true
                },
                {
                    title: '附件',
                    dataIndex: 'sysAttachList',
                    width: 210,
                    isSelf: true
                },
            ]

        }
    ]
}

