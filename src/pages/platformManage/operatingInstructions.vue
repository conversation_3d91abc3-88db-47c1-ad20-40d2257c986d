<template>
  <div class="opera">
    <div class="list">
      <div class="list-box">
        <div class="list-title"><img :src="zyImg" class="header-img" alt="" /> 业务导航</div>
        <div class="list-content">
          <el-segmented
            v-model="value"
            :options="options"
            :direction="direction"
            :size="size"
            @change="selectMenu = options.find((item) => item.value === value)"
          >
            <template #default="{ item }">
              <div class="flex flex-col items-center">
                <!-- <el-icon size="20">
                <component :is="item.icon" />
              </el-icon> -->
                <!-- <img :src="item.imgSrc" class="menu-icon" alt="" /> -->
                <span class="my-span">{{ item.title }}</span>
              </div>
            </template>
          </el-segmented>
        </div>
      </div>
    </div>
    <div class="content">
      <div class='content-header'>监管服务---操作指引</div>
      <div class="card">
        <span>流程图</span>
        <div class="mycon" style="opacity: 0">
          <div @click="handleOpen(4)" class="flowchart">
            {{ selectMenu.title }}流程图
          </div>
        </div>
      </div>
      <div class="card">
        <span>操作指引</span>
        <div class="mycon" style="opacity: 0">
          <div @click="handleOpen(3)" class="flowchart">
            {{ selectMenu.title }}操作指引
          </div>
        </div>
      </div>
      <div class="card">
        <span>操作指南</span>
        <div class="mycon" style="opacity: 0">
          <div @click="handleOpen(2)" class="flowchart">
            {{ selectMenu.title }}操作指南
          </div>
        </div>
      </div>
      <div class="card">
        <span>视频</span>
        <div class="mycon" style="opacity: 0">
          <div @click="handleOpen(1)" class="flowchart">
            {{ selectMenu.title }}操作视频
          </div>
        </div>
      </div>
    </div>
  </div>
  <allDialogView
    ref="allDialogViewEl"
    :dialogTitle="dialogTitle"
    :dialogWidth="'70%'"
  >
    <template #content>
      <div style="width: 100%; height: 700px; text-align: center">
        <VideoPlay
          v-show="true"
          :videoUrl="videoUrl"
          :videoCover="videoCover"
          :width="1200"
          :height="'98%'"
          :autoplay="true"
          :controls="true"
          :loop="false"
          :muted="false"
          preload="auto"
          :showPlay="true"
          :playWidth="96"
          zoom="cotain"
        />
      </div>
      <span class="dialog-footer">
        <el-button
          type="primary"
          round
          style="width: 100px"
          @click="handleClose()"
          >关闭</el-button
        >
      </span>
    </template>
  </allDialogView>
</template>

<script setup>
import { getMenus } from "@/layout/constant";
import useGloBalStore from "@/store/useGlobalStore";
const { sysUserModulesCopy } = useGloBalStore();
const zyImg =  `/${import.meta.env.VITE_PROXY_SECMENU}/bid/zy.png`;
let menusItems = getMenus(sysUserModulesCopy);
const direction = ref("vertical");
const size = ref("large");
const dialogTitle = ref("");
const allDialogViewEl = ref();
const videoUrl = ref("");
const videoCover = ref("");
const handleClose = () => {
  allDialogViewEl.value.showDialog = !allDialogViewEl.value.showDialog;
};
const list = menusItems.filter((item) => {
  item.value = item.title;
  return item.title != "首页" && item.title != "监管服务";
});
console.error("menusItemsmenusItems", list, menusItems);
const options = list;
const value = ref(list[0].value);
const selectMenu = ref(list[0]);

const handleOpen = (type) => {
  switch (type) {
    case 1:
      allDialogViewEl.value.showDialog = !allDialogViewEl.value.showDialog;
      videoUrl.value = selectMenu.value.videoUrl;
      dialogTitle.value = `${selectMenu.value.title}操作视频`;
      break;
    case 2:
      window.open(selectMenu.value.operatingManualUrl, "_blank");
      break;
    case 3:
      window.open(selectMenu.value.flowInsUrl, "_blank");
      break;
    case 4:
      window.open(selectMenu.value.flowChartUrl, "_blank");
      break;
  }
};
</script>

<style lang="scss" scoped>
.opera {
  width: 100%;
  height: 100%;
  background-color: #fff;
  display: flex;
  background: url("/bid/bg.png") no-repeat;
  background-size: 100% 100%;
  .list {
    width: 20%;
    height: 100%;
    margin-right: -1%;
    display: flex;
    justify-content: center;
    padding: 2% 0;
    .list-box {
      background-color: #fff;
      border-radius: 10px;
      overflow: hidden;
    }
    .list-title {
      background: #3366FF;
      text-align: center;
      line-height: 55px;
      font-size: 20px;
      color: #fff;
      font-weight: 600;
      .header-img{
        width: 25px;
        height: 25px;
        vertical-align: sub;
        margin-right: 6px;
      }
    }
    .list-content {
      padding: 10px 18px;
      :deep(.el-segmented__group) {
        background-color: #fff;
      }
      :deep(.el-segmented) {
        padding: 0px !important;
      }
      :deep(.el-segmented__item) {
        &:hover{
          background-color: #c6e2ff!important;
        }
      }
    }

  }
  .content {
    width: 78%;
    height: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;

    /* From Uiverse.io by eslam-hany */
    .content-header{
      position: absolute;
      top: 7%;
      left: 50%;
      transform: translate(-50%, 0%);
      z-index: 1000;
      font-size: 30px;
      color: blue;
      font-weight: 600;

    }
    .card {
      position: relative;
      width: 21%;
      height: 62%;
      background: #a0cfff;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 25px;
      font-weight: bold;
      border-radius: 15px;
      cursor: pointer;

      .mycon {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 10;
        opacity: 0;
        transition: all 2s;
        .flowchart {
          width: 100%;
          height: 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          color: #409eff;
        }
      }
      &:hover {
        .mycon {
          opacity: 1 !important;
        }
      }
    }

    .card::before,
    .card::after {
      position: absolute;
      content: "";
      width: 20%;
      height: 20%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 25px;
      font-weight: bold;
      background-color: #d9ecff;
      transition: all 0.5s;
    }

    .card::before {
      top: 0;
      right: 0;
      border-radius: 0 15px 0 100%;
    }

    .card::after {
      bottom: 0;
      left: 0;
      border-radius: 0 100% 0 15px;
    }

    .card:hover::before,
    .card:hover:after {
      width: 100%;
      height: 100%;
      border-radius: 15px;
      transition: all 0.5s;
    }

    // .card:hover:after {
    //   content: "HELLO";
    // }
  }
}
.menu-icon {
  width: 35px;
}
:deep(.is-selected) {
  .menu-icon {
    width: 40px;
    left: -90px;
    filter: drop-shadow(#fff 80px 0);
  }
  .my-span {
    font-size: 18px;
    font-weight: bold;
    color: #fff;
  }
}
:deep(.el-segmented--large) {
  width: 210px;
  border-radius: 10px;
  overflow: hidden;
}
.menu-icon {
  position: relative;
  margin-right: 5px;
  padding: 0 3px;
  left: 0;
}
.dialog-footer {
  display: flex;
  justify-content: center;
}
</style>