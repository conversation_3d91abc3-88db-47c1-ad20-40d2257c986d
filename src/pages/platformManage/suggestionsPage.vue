<template>
    <el-row :gutter="20">
        <!--用户数据-->
        <el-col :span="24" :xs="24">
            <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="108px">
                <el-form-item label="" prop="keyword">
                    <el-input v-model="queryParams.keyword" placeholder="请输入关键字" clearable
                              style="width: 240px"
                              @keyup.enter="handleQuery"/>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                    <el-button type="warning" icon="Setting" @click="handleExport">导出</el-button>
                </el-form-item>
            </el-form>
            <el-table v-loading="loading" :data="suggestionsList" @selection-change="handleSelectionChange"
                      :row-class-name="tableRowClassName"
                      class="my-table">
                <el-table-column
                    label="序号"
                    type="index"
                    width="60px"
                    :index="indexMethod"
                    align="center"
                />
                <el-table-column label="单位名称" align="center" key="unitName" prop="unitName" width="250px"
                                 :show-overflow-tooltip="true"/>
                <el-table-column label="联系人" align="center" key="contacts" prop="contacts" width="200px"
                                 :show-overflow-tooltip="true"/>
                <el-table-column label="联系电话" align="center" key="contactsPhone" prop="contactsPhone" width="150px"
                                 :show-overflow-tooltip="true"/>
                <el-table-column label="反馈内容" align="center" key="content" prop="content" width="300px"
                                 >
                    <template #default="{ row }">
                        <el-popover
                            placement="top"
                            :width="800"
                            trigger="hover"
                            popper-class="custom-popover"
                        >
                            <template #reference>
                                <div class="cell-content">{{ row.content }}</div>
                            </template>
                            <div class="popover-content">{{ row.content }}</div>
                        </el-popover>
                    </template>
                </el-table-column>
                <el-table-column label="意见描述" align="center" key="replyContent" prop="replyContent"
                                 >
                    <template #default="{ row }">
                        <el-popover
                            placement="top"
                            :width="800"
                            trigger="hover"
                            popper-class="custom-popover"
                        >
                            <template #reference>
                                <div class="cell-content">{{ row.replyContent }}</div>
                            </template>
                            <div class="popover-content">{{ row.replyContent }}</div>
                        </el-popover>
                    </template>
                </el-table-column>
                <el-table-column label="提交时间" align="center" key="dataTimestamp" prop="dataTimestamp"  width="150px"
                                 :show-overflow-tooltip="true">
                    <template #default="{ row }">
                        {{ formatDate(row.dataTimestamp) }}
                    </template>
                </el-table-column>
                <el-table-column align="center" label="操作" width="220px">
                    <template #default="{ row }">
                        <el-button type="primary" class="my-detail-btn" plain
                                   size="small" round @click="handleViewDetail(row)">查看附件
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
            <div class="pagination-box">
                <el-pagination
                        class="pagination"
                        background
                        v-model:current-page="queryParams.pageNum"
                        v-model:page-size="queryParams.pageSize"
                        :total="total"
                        layout=" total, prev, pager, next"
                        @current-change="getList"
                />
            </div>
        </el-col>
    </el-row>
    <allDialogView ref="allDialogViewSuggestion" :dialogTitle='"意见建议详情"' :dialogWidth="'60%'" :show-close = true>
        <template #content>
            <div class="viewSuggestionBox" v-html="suggestionContent">
            </div>
            <span class="dialog-footer">
                <el-button
                    type="primary"
                    round style="width: 100px"
                    @click="handleCloseSuggestion()"
                >关闭</el-button>
            </span>
        </template>
    </allDialogView>
</template>

<script setup name="Suggestions">
import {httpGet, httpPost} from "@wl-fe/http/dist";
import {useRoute, useRouter} from "vue-router";

const router = useRouter();
const route = useRoute();
const {proxy} = getCurrentInstance();

const suggestionsList = ref([]);
const loading = ref(true);
const showSearch = ref(true);
const allDialogViewSuggestion = ref(false);
const suggestionContent = ref("");
const total = ref(0);

const formatDate = (timestamp) => {
    if (!timestamp) return '';
    const date = new Date(timestamp);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}年${month}月${day}日`;
};

const data = reactive({
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        keyword: undefined
    }
});

const {queryParams} = toRefs(data);

const indexMethod = (index) => {
    return index + 1;
};

const handleViewDetail = (row) => {
  window.open(row.fileUrl, "_blank")
};

const handleCloseSuggestion = () => {
    allDialogViewSuggestion.value.showDialog = false;
};

const tableRowClassName = ({rowIndex}) => {
    if (rowIndex % 2 !== 0) {
        return "warning-row";
    }
    return "";
};

/** 查询建议列表 */
async function getList() {
    loading.value = true;
    try {
        const result = await httpPost("/api/systemRecommendation/selectSysRecommendationList" + `?pageNum=${queryParams.value.pageNum}&pageSize=${queryParams.value.pageSize}`, queryParams.value);
        suggestionsList.value = result.rows;
        total.value = result.total;
    } catch (error) {
        console.error("获取建议列表失败", error);
    } finally {
        loading.value = false;
    }
}

/** 搜索按钮操作 */
function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
};

async function handleExport() {
    console.log('导出')
    const response = await httpPost(
        "/api/systemRecommendation/export",
        queryParams.value,
        { ignoreTransferResult: true ,responseType: 'blob'}
    );
    const blob = new Blob([response], {
        type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.setAttribute("download", "统计表.xlsx");
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
};


getList();
</script>

<style scoped lang="less">
.pagination-box {
    margin-top: 20px;
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
}

.my-table {
    :deep(.el-table__header-wrapper) {
        .el-table__cell {
            background: #f0f6ff;
            height: 55px;
            color: rgba(0, 0, 0, 0.88);
        }
    }

    :deep(.el-table__row) {
        height: 55px;
    }

    :deep(.warning-row) {
        background: #f9fbff;
    }
}

.viewSuggestionBox {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;

    img {
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
    }

    * {
        width: 100%;
        box-sizing: border-box;
    }
}

.dialog-footer {
    display: flex;
    justify-content: center;
    margin-top: 10px;
}

.cell-content {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
}

.popover-content {
    max-height: 300px;
    overflow-y: auto;
    word-break: break-all;
    line-height: 1.5;
    font-size: 14px;
}

:deep(.custom-popover) {
    padding: 12px !important;
    max-width: 400px !important;
    background-color: #fff !important;
    border: 1px solid #e4e7ed !important;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1) !important;
}

:deep(.custom-popover .el-popover__title) {
    margin: 0;
    font-size: 16px;
    line-height: 1;
    color: #303133;
}

:deep(.custom-popover .el-popover__content) {
    margin: 0;
    padding: 0;
}
</style> 