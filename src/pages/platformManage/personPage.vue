<template>
    <!-- <PageWrapper> -->
        <el-row :gutter="20">
            <!--部门数据-->
            <el-col :span="4" :xs="24">
                <div class="head-container">
                    <el-input v-model="deptName" placeholder="请输入部门名称" clearable prefix-icon="Search"
                              style="margin-bottom: 20px"/>
                </div>
                <div class="head-container" style="height: 850px; overflow-y: auto;">
                    <el-tree :data="deptOptions" :props="{ label: 'label', children: 'children' }"
                             :expand-on-click-node="false"
                             :filter-node-method="filterNode" ref="deptTreeRef" node-key="id" highlight-current
                             default-expand-all
                             @node-click="handleNodeClick"/>
                </div>
            </el-col>
            <!--用户数据-->
            <el-col :span="20" :xs="24">
                <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
                    <el-form-item label="用户名称" prop="nickName">
                        <el-input v-model="queryParams.nickName" placeholder="请输入用户名称" clearable
                                  style="width: 240px"
                                  @keyup.enter="handleQuery"/>
                    </el-form-item>
                    <el-form-item label="状态" prop="status">
                        <el-select v-model="queryParams.status" placeholder="用户状态" clearable style="width: 240px">
                            <el-option v-for="dict in sys_normal_disable" :key="dict.value" :label="dict.label"
                                       :value="dict.value"/>
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                    </el-form-item>
                </el-form>
                <el-table v-loading="loading" :data="userList" @selection-change="handleSelectionChange"
                          :row-class-name="tableRowClassName"
                          class="my-table">
                    <!--                    <el-table-column type="selection" width="50" align="center"/>-->
                    <el-table-column label="监管部门" align="center" key="deptName" prop="dept.leader"
                                     v-if="columns[3].visible"
                                     :show-overflow-tooltip="true"/>
                    <el-table-column label="用户名称" align="center" key="nickName" prop="nickName"
                                     v-if="columns[2].visible"
                                     :show-overflow-tooltip="true"/>
                    <el-table-column label="登录账号" align="center" key="userName" prop="userName"
                                     v-if="columns[1].visible"
                                     :show-overflow-tooltip="true"/>
                    <el-table-column label="手机号" align="center" key="phonenumber" prop="phonenumber"
                                     v-if="columns[1].visible"
                                     :show-overflow-tooltip="true"/>
                    <el-table-column label="CA锁类型" align="center" key="certType" prop="certType"
                                     v-if="columns[4].visible"
                                     width="120"/>
                    <el-table-column label="用户状态" align="center" key="status" v-if="columns[5].visible">
                        <template #default="scope">
                            <el-switch v-model="scope.row.status" active-value="0" inactive-value="1"
                                       @change="handleStatusChange(scope.row)"></el-switch>
                        </template>
                    </el-table-column>
                </el-table>
                <div class="pagination-box">
                    <el-pagination
                            class="pagination"
                            background
                            v-model:current-page="queryParams.pageNum"
                            v-model:page-size="queryParams.pageSize"
                            :total="total"
                            layout=" total, prev, pager, next"
                            @current-change="getList"
                    />
                </div>
                <!--                <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"-->
                <!--                            v-model:limit="queryParams.pageSize" @pagination="getList"/>-->
            </el-col>
        </el-row>
    <!-- </PageWrapper> -->
</template>

<script setup name="User">
import {httpGet, httpPost} from "@wl-fe/http/dist";
import {getOptionList} from "@/pages/project/projectInstruct/formOptionList.js";
import {useRoute, useRouter} from "vue-router";

// import { getToken } from "@/utils/auth";
// import { changeUserStatus, listUser, resetUserPwd, delUser, getUser, updateUser, addUser, deptTreeSelect } from "@/api/system/user";

const route = useRoute();
const {proxy} = getCurrentInstance();
// const { sys_normal_disable, sys_user_sex } = proxy.useDict("sys_normal_disable", "sys_user_sex");
const sys_normal_disable = ref("");
const userList = ref([]);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const deptName = ref("");
const deptOptions = ref(undefined);

// 列显隐信息
const columns = ref([
    {key: 0, label: `用户编号`, visible: true},
    {key: 1, label: `用户名称`, visible: true},
    {key: 2, label: `用户昵称`, visible: true},
    {key: 3, label: `部门`, visible: true},
    {key: 4, label: `手机号码`, visible: true},
    {key: 5, label: `状态`, visible: true},
    {key: 6, label: `创建时间`, visible: true}
]);

const data = reactive({
    form: {},
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        userName: undefined,
        phonenumber: undefined,
        status: undefined,
        deptId: undefined
    },
    rules: {
        userName: [{required: true, message: "用户名称不能为空", trigger: "blur"}, {
            min: 2,
            max: 20,
            message: "用户名称长度必须介于 2 和 20 之间",
            trigger: "blur"
        }],
        nickName: [{required: true, message: "用户昵称不能为空", trigger: "blur"}],
        password: [{required: true, message: "用户密码不能为空", trigger: "blur"}, {
            min: 5,
            max: 20,
            message: "用户密码长度必须介于 5 和 20 之间",
            trigger: "blur"
        }, {pattern: /^[^<>"'|\\]+$/, message: "不能包含非法字符：< > \" ' \\\ |", trigger: "blur"}],
        email: [{type: "email", message: "请输入正确的邮箱地址", trigger: ["blur", "change"]}],
        phonenumber: [{pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/, message: "请输入正确的手机号码", trigger: "blur"}]
    }
});

const {queryParams, form} = toRefs(data);
const spinning = ref(false)

/** 通过条件过滤节点  */
const filterNode = (value, data) => {
    if (!value) return true;
    return data.label.indexOf(value) !== -1;
};

/** 根据名称筛选部门树 */
watch(deptName, val => {
    proxy.$refs["deptTreeRef"]?.filter(val);
});

/** 递归设置最后一级节点的 expanded 状态为 false */
function collapseLastLevelNodes(nodes) {
    nodes.forEach(node => {
        if (node.children && node.children.length > 0) {
            collapseLastLevelNodes(node.children);
        } else {
            node.expanded = false;
        }
    });
}

/** 查询部门下拉树结构 */
async function getDeptTree() {
    spinning.value = true;
    try {
        const result = await httpGet("/system/user/deptTree", {})
        console.log(result, 'result');
        // collapseLastLevelNodes(result.value)
        deptOptions.value = result
        spinning.value = false
        first()
    } catch (error) {
        spinning.value = false
    }
};

/** 查询用户列表 */
async function getList() {
    sys_normal_disable.value = await getOptionList('sys_normal_disable');
    loading.value = true;
    console.log( 'queryParams.value',queryParams.value)
    const result = await httpPost("/system/user/selectNewUserlist" + `?pageNum=${queryParams.value.pageNum}&pageSize=${queryParams.value.pageSize}`, queryParams.value)
    console.log(result, 'result22');
    loading.value = false;
    userList.value = result.rows;
    total.value = result.total;
};

/** 节点单击事件 */
function handleNodeClick(data) {
    queryParams.value.deptId = data.id;
    handleQuery();
};

/** 搜索按钮操作 */
function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
};


/** 用户状态修改  */
function handleStatusChange(row) {
    let text = row.status === "0" ? "启用" : "停用";
    ElMessageBox.alert('确认要' + text + '' + row.nickName + '用户吗?', '', {
        confirmButtonText: '确认',
        callback: (action) => {
            if (action == 'confirm') {
                const result = httpPost("/system/user/changeStatus", row)
                console.log("查看状态", result)
                // return changeUserStatus(row.userId, row.status);
            }
        }
    })
    // proxy.$modal.confirm().then(function () {
    //     return changeUserStatus(row.userId, row.status);
    // }).then(() => {
    //     ElMessage.success(text + "成功");
    // }).catch(function () {
    //     row.status = row.status === "0" ? "1" : "0";
    // });
};

const tableRowClassName = ({rowIndex}) => {
    if (rowIndex % 2 !== 0) {
        return "warning-row";
    }
    return "";
};

/** 选择条数  */
function handleSelectionChange(selection) {
    ids.value = selection.map(item => item.userId);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
};

/** 重置操作表单 */
function reset() {
    form.value = {
        userId: undefined,
        deptId: undefined,
        userName: undefined,
        nickName: undefined,
        password: undefined,
        phonenumber: undefined,
        email: undefined,
        sex: undefined,
        status: "0",
        remark: undefined,
        postIds: [],
        roleIds: []
    };
    //   proxy.resetForm("userRef");
};


async function first() {
    let {query} = toRefs(route);
    console.log('query', route)
    const {
        regionName
    } = query.value;
    await nextTick();
    deptName.value = regionName
    // regionName.value = ''
};



getDeptTree();

getList();
</script>
<style scoped lang="less">
.pagination-box {
  margin-top: 20px;
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
}

.my-table {
  :deep(.el-table__header-wrapper) {
    .el-table__cell {
      background: #f0f6ff;
      height: 55px;
      color: rgba(0, 0, 0, 0.88);
    }
  }

  :deep(.el-table__row) {
    height: 55px;
  }

  :deep(.warning-row) {
    background: #f9fbff;
  }
}
/* 修改 el-tree 的字体样式 */
:deep(.el-tree-node__label) {
  font-size: 15px; /* 设置字体大小 */
  font-weight: 500; /* 设置字体粗细 */
}
</style>
