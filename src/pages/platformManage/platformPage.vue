<template>
        <el-row :gutter="20">
            <!--部门数据-->
            <!-- <el-col :span="4" :xs="24">
                <div class="head-container">
                    <el-input v-model="platformName" placeholder="请输入部门名称" clearable prefix-icon="Search"
                              style="margin-bottom: 20px"/>
                </div>
                <div class="head-container" style="height: 850px; overflow-y: auto;">
                    <el-tree :data="deptOptions" :props="{ label: 'label', children: 'children' }"
                             :expand-on-click-node="false"
                             :filter-node-method="filterNode" ref="deptTreeRef" node-key="id" highlight-current
                             default-expand-all
                             @node-click="handleNodeClick"/>
                </div>
            </el-col> -->
            <!--用户数据-->
            <el-col :span="24" :xs="24">
                <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="108px">
                    <el-form-item label="交易平台名称" prop="platformCode">
                        <el-input v-model="queryParams.keyword" placeholder="请输入交易平台名称" clearable
                                  style="width: 240px"
                                  @keyup.enter="handleQuery"/>
                    </el-form-item>
                    <!--                    <el-form-item label="平台状态" prop="status">-->
                    <!--                        <el-select v-model="queryParams.status" placeholder="用户状态" clearable style="width: 240px">-->
                    <!--                            <el-option v-for="dict in sys_normal_disable" :key="dict.value" :label="dict.label"-->
                    <!--                                       :value="dict.value"/>-->
                    <!--                        </el-select>-->
                    <!--                    </el-form-item>-->
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
<!--                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>-->
                    </el-form-item>
                </el-form>
                <el-table v-loading="loading" :data="platformList" @selection-change="handleSelectionChange"
                          :row-class-name="tableRowClassName"
                          class="my-table">
                    <!--                    <el-table-column type="selection" width="50" align="center"/>-->
                    <el-table-column label="交易平台名称" align="center" key="platformName" prop="platformName"
                                     v-if="columns[3].visible"
                                     :show-overflow-tooltip="true"/>
                    <el-table-column label="交易平台编号" align="center" key="platformCode" prop="platformCode"
                                     v-if="columns[2].visible"
                                     :show-overflow-tooltip="true"/>
                    <el-table-column label="运营单位" align="center" key="operatingUnit" prop="operatingUnit"
                                     v-if="columns[1].visible"
                                     :show-overflow-tooltip="true"/>
                    <el-table-column label="平台状态" align="center" key="status" v-if="columns[5].visible">
                        <template #default="scope">
                            <!-- <el-switch v-model="scope.row.status" active-value="0" inactive-value="1"
                                       @change="handleStatusChange(scope.row)"></el-switch> -->
                            <el-select v-model="scope.row.status" @change="handleStatusChange(scope.row)"
                                       placeholder="Select" style="width: 100px">
                                <el-option
                                        v-for="item in colors"
                                        :key="item.status"
                                        :label="item.label"
                                        :value="item.status"
                                >
                                    <div class="flex items-center">
                                        <el-tag :color="item.value" style="margin-right: 8px" size="small">
                                            <span style="color: #fff;">{{ item.label }}</span>
                                        </el-tag>
                                    </div>
                                </el-option>
                                <template #label="{ label }">
                                    <el-tag :color="colors.find(item => item.label === label)?.value"><span
                                            style="color: #fff;">{{ label }}</span></el-tag>
                                </template>
                            </el-select>
                        </template>
                    </el-table-column>
                </el-table>
                <div class="pagination-box">
                    <el-pagination
                            class="pagination"
                            background
                            v-model:current-page="queryParams.pageNum"
                            v-model:page-size="queryParams.pageSize"
                            :total="total"
                            layout=" total, prev, pager, next"
                            @current-change="getList"
                    />
                </div>
                <!--                <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"-->
                <!--                            v-model:limit="queryParams.pageSize" @pagination="getList"/>-->
            </el-col>
        </el-row>
</template>

<script setup name="User">
import {httpGet, httpPost} from "@wl-fe/http/dist";
import {getOptionList} from "@/pages/project/projectInstruct/formOptionList.js";
import {useRoute, useRouter} from "vue-router";

// import { getToken } from "@/utils/auth";
// import { changeUserStatus, listUser, resetUserPwd, delUser, getUser, updateUser, addUser, deptTreeSelect } from "@/api/system/user";

const router = useRouter();
const route = useRoute();
const {proxy} = getCurrentInstance();
// const { sys_normal_disable, sys_user_sex } = proxy.useDict("sys_normal_disable", "sys_user_sex");
const sys_normal_disable = ref("");
const platformList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const dateRange = ref([]);
const platformName = ref("");
const deptOptions = ref(undefined);
const initPassword = ref(undefined);
const postOptions = ref([]);
const roleOptions = ref([]);
/*** 用户导入参数 */
const upload = reactive({
    // 是否显示弹出层（用户导入）
    open: false,
    // 弹出层标题（用户导入）
    title: "",
    // 是否禁用上传
    isUploading: false,
    // 是否更新已经存在的用户数据
    updateSupport: 0,
    // 设置上传的请求头部
    //   headers: { Authorization: "Bearer " + getToken() },
    // 上传的地址
    url: import.meta.env.VITE_APP_BASE_API + "/system/user/importData"
});
// 列显隐信息
const columns = ref([
    {key: 0, label: `用户编号`, visible: true},
    {key: 1, label: `用户名称`, visible: true},
    {key: 2, label: `用户昵称`, visible: true},
    {key: 3, label: `部门`, visible: true},
    {key: 4, label: `手机号码`, visible: true},
    {key: 5, label: `状态`, visible: true},
    {key: 6, label: `创建时间`, visible: true}
]);

const data = reactive({
    form: {},
    queryParams: {
        pageNum: 1,
        pageSize: 14,
        operatingUnit: undefined,
        phonenumber: undefined,
        status: undefined,
        deptId: undefined
    },
    rules: {
        operatingUnit: [{required: true, message: "用户名称不能为空", trigger: "blur"}, {
            min: 2,
            max: 20,
            message: "用户名称长度必须介于 2 和 20 之间",
            trigger: "blur"
        }],
        platformCode: [{required: true, message: "用户昵称不能为空", trigger: "blur"}],
        password: [{required: true, message: "用户密码不能为空", trigger: "blur"}, {
            min: 5,
            max: 20,
            message: "用户密码长度必须介于 5 和 20 之间",
            trigger: "blur"
        }, {pattern: /^[^<>"'|\\]+$/, message: "不能包含非法字符：< > \" ' \\\ |", trigger: "blur"}],
        email: [{type: "email", message: "请输入正确的邮箱地址", trigger: ["blur", "change"]}],
        phonenumber: [{pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/, message: "请输入正确的手机号码", trigger: "blur"}]
    }
});

const {queryParams, form, rules} = toRefs(data);
const spinning = ref(false)
const value = ref('')
const colors = [
    {
        value: '#1EC79D',
        status: '0',
        label: '正常',
    },
    {
        value: '#FF6600',
        status: '1',
        label: '整改',
    },
    {
        value: '#E63415',
        status: '2',
        label: '下线',
    },
]
const changColor = (status) => {
    return colors.find(item => item.status === status)?.value
}
/** 通过条件过滤节点  */
const filterNode = (value, data) => {
    if (!value) return true;
    return data.label.indexOf(value) !== -1;
};

/** 根据名称筛选部门树 */
watch(platformName, val => {
    proxy.$refs["deptTreeRef"]?.filter(val);
});

/** 查询部门下拉树结构 */
async function getDeptTree() {
    spinning.value = true
    try {
        const result = await httpGet("/system/user/deptTree", {})
        console.log(result, 'result');
        deptOptions.value = result
        console.log(deptOptions.value, 'deptOptionsdeptOptions');
        spinning.value = false
        first()
    } catch (error) {
        spinning.value = false
    }
};

/** 查询用户列表 */
async function getList() {
    sys_normal_disable.value = await getOptionList('sys_normal_disable');
    loading.value = true;
    const result = await httpPost("/api/SyPlatformManagement/list", queryParams.value)
    loading.value = false;
    platformList.value = result.rows;
    total.value = result.total;
};

/** 节点单击事件 */
function handleNodeClick(data) {
    queryParams.value.deptId = data.id;
    handleQuery();
};

/** 搜索按钮操作 */
function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
};

/** 重置按钮操作 */
function resetQuery() {
    dateRange.value = [];
    proxy.resetForm("queryRef");
    queryParams.value.deptId = undefined;
    proxy.$refs.deptTreeRef.setCurrentKey(null);
    handleQuery();
};

/** 用户状态修改  */
async function handleStatusChange(row) {
    let text = row.status === "0" ? "恢复" : row.status === "1" ? "整改" : "下线";
    ElMessageBox.alert('确认要' + text + '' + row.platformName + '吗?', '', {
        confirmButtonText: '确认',
        callback: async (action) => {
            if (action == 'confirm') {
                try {
                    const result = await httpPost("/api/SyPlatformManagement/edit", row);
                    console.log("查看状态", result);
                    if (result.code) {
                        ElMessage.success("操作成功");
                    }
                } catch (error) {
                    console.error("操作失败", error);
                }
            }
        }
    });
}

const tableRowClassName = ({rowIndex}) => {
    if (rowIndex % 2 !== 0) {
        return "warning-row";
    }
    return "";
};

/** 重置密码按钮操作 */
function handleResetPwd(row) {
    proxy.$prompt('请输入"' + row.operatingUnit + '"的新密码', "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        closeOnClickModal: false,
        inputPattern: /^.{5,20}$/,
        inputErrorMessage: "用户密码长度必须介于 5 和 20 之间",
        inputValidator: (value) => {
            if (/<|>|"|'|\||\\/.test(value)) {
                return "不能包含非法字符：< > \" ' \\\ |"
            }
        },
    }).then(({value}) => {
        resetUserPwd(row.userId, value).then(response => {
            ElMessage.success("修改成功，新密码是：" + value);
        });
    }).catch(() => {
    });
};

/** 选择条数  */
function handleSelectionChange(selection) {
    ids.value = selection.map(item => item.userId);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
};

/** 重置操作表单 */
function reset() {
    form.value = {
        userId: undefined,
        deptId: undefined,
        operatingUnit: undefined,
        platformCode: undefined,
        password: undefined,
        phonenumber: undefined,
        email: undefined,
        sex: undefined,
        status: "0",
        remark: undefined,
        postIds: [],
        roleIds: []
    };
    //   proxy.resetForm("userRef");
};

/** 取消按钮 */
function cancel() {
    open.value = false;
    reset();
};

async function first() {
    let {query} = toRefs(route);
    console.log('query', route)
    const {
        regionName
    } = query.value;
    await nextTick();
    platformName.value = regionName
    // regionName.value = ''
};


getDeptTree();

getList();
</script>
<style scoped lang="less">
.pagination-box {
  margin-top: 20px;
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
}

.my-table {
  :deep(.el-table__header-wrapper) {
    .el-table__cell {
      background: #f0f6ff;
      height: 55px;
      color: rgba(0, 0, 0, 0.88);
    }
  }


  :deep(.el-table__row) {
    height: 55px;
  }

  :deep(.warning-row) {
    background: #f9fbff;
  }
}
</style>
