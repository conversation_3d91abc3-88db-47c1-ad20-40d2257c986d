
<template>
    <PageWrapper>
        <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
            <el-tab-pane label="操作指引" name="four">
                <operatingInstructions></operatingInstructions>
            </el-tab-pane>
            <el-tab-pane label="通知公告" name="first" style="height: 727px; overflow-y: auto; overflow-x: hidden;">
                <announcement></announcement>
            </el-tab-pane>
            <el-tab-pane label="交易平台管理" name="second" v-if="user.roles[0].roleId === 6">
                <platformPage></platformPage>
            </el-tab-pane>
            <el-tab-pane label="人员管理" name="third" style="height: 727px; overflow-y: auto; overflow-x: hidden;" v-if="user.roles[0].roleId === 6">
                <personPage></personPage>
            </el-tab-pane>
            <el-tab-pane label="意见建议" name="fifth" style="height: 727px; overflow-y: auto; overflow-x: hidden;" v-if="user.userType == 99">
                <suggestionsPage></suggestionsPage>
            </el-tab-pane>
        </el-tabs>
    </PageWrapper>
</template>

<script setup >import platformPage from './platformPage.vue'
import personPage from './personPage.vue'
import announcement from './announcementPage.vue'
import operatingInstructions from './operatingInstructions.vue'
import suggestionsPage from './suggestionsPage.vue'
const activeName = ref('four')
import useGloBalStore from "@/store/useGlobalStore"
const { user } = useGloBalStore();

</script>

<style lang="less" scoped>.demo-tabs {
    width: 100%;
    height: 100%;
    :deep(.el-tabs__content) {
        height: 100%;
        width: 100%;
    }
    #pane-four {
        width: 100%;
        height: 100%;
    }
}

.el-tab-pane {
    font-size: 18px;
}

/* 自定义 el-tab 标题样式 */
:deep(.el-tabs__item) {
    font-size: 16px; /* 修改字体大小 */
    font-weight: bold; /* 修改字体加粗 */
    color: #333; /* 修改字体颜色 */
}

/* 可选：修改激活状态下的标签样式 */
:deep(.el-tabs__item.is-active) {
    color: #409EFF; /* 修改激活状态下的字体颜色 */
}
</style>