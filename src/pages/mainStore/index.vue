<template>
  <div class="mic-box" v-loading="isLoading" element-loading-text="">
    <micro-app
      iframe
      name="my-app"
      :url="url"
      class="mic-box-app"
      :data="data"
      @unmount="handleUnMounted"
      @mounted="handleonMounted"
    />
  </div>
</template>

<script setup>
import { httpGet, httpPost } from "@wl-fe/http/dist";
import { getModuleMap } from "@/constant/module";
const route = useRoute();
let isLoading = ref(true);
function getPathBeforeQueryParam(url, queryParam) {
  const queryParamWithQuestionMark = `?${queryParam}`;
  const index = url.indexOf(queryParamWithQuestionMark);
  if (index !== -1) {
    // 如果找到了查询参数，返回它之前的部分
    return url.slice(0, index);
  } else {
    // 如果没有找到查询参数，返回原值
    return url;
  }
}
getUrl(getPathBeforeQueryParam(route.fullPath, "my-app"));
// 监听路由变化
watch(
  () => route.fullPath,
  (newFullPath, oldFullPath) => {
    if (
      getPathBeforeQueryParam(newFullPath, "my-app") !=
      getPathBeforeQueryParam(oldFullPath, "my-app")
    ) {
      isLoading.value = true;
      getUrl(getPathBeforeQueryParam(newFullPath, "my-app"));
    }
  },
  { immediate: false }
);

let url = ref(""); // "http://localhost:8083"; //  "https://ztjg.lnwlzb.com"; // https://ztjg.lnwlzb.com
let data = ref({}); //
//  {
//   params: {
//     devicenum: "3300000477115376", // 锁号 目前写死 可以修改成动态
//   },
// };
function getParamsAsObject(url) {
  const paramsStr = url.split("?")[1];
  if (!paramsStr) {
    return {};
  }
  return paramsStr.split("&").reduce((acc, param) => {
    const [key, value] = param.split("=");
    acc[key] = decodeURIComponent(value);
    return acc;
  }, {});
}
async function getUrl(pathStr) {
  const moduleMap = getModuleMap();
  // console.log("moduleMap[pathStr]", moduleMap[`${pathStr.replace(/^\//, "")}`]);
  const singlePointUrl = moduleMap[`${pathStr.replace(/^\//, "")}`].singleUrl;
  const result = await httpGet(
    singlePointUrl,
    {},
    {
      transferResult: (result) => {
        return result;
      },
    }
  );
  url.value = result;//moduleMap[`${pathStr.replace(/^\//, "")}`].localUrl;
  data.value = { params: getParamsAsObject(result) };
  console.log("result", result, url.value, data.value);
}

const handleUnMounted = () => {
  document.title = "辽宁省房屋建筑和市政工程招标投标智能监管系统";
};
const handleonMounted = () => {
  isLoading.value = false;
}
</script>

<style lang="less" scoped>
.mic-box {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  font-size: 16px;
  .mic-box-app {
    width: 100%;
    height: 100%;
    overflow: hidden;
    font-size: 16px;
  }
}
</style>
