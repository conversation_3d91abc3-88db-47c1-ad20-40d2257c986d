<!-- 信息公开界面 -->
<script setup>
import SearchList from "@/models/searchList/index.vue";
import { renderSummaryColumns, listUrl, singlePointUrl } from "./constant.ts";
import { labelExtraRender } from "@/models/searchList/constant";
const columns = renderSummaryColumns();
const activeKey = ref(columns[0].dataIndex);
const columnItem = computed(() =>
  columns.find((item) => item.dataIndex === activeKey.value)
);
const getSinglePointUrl = (data) => {
  return {
     id: data.rowId,
     epointId: data.epointId?data.epointId:'',
     code: activeKey.value,
  };
};

</script>
<template>
  <PageWrapper>
    <SearchList
      :key="activeKey"
      searchPlaceholder='请输入单位名称或统一社会信用代码'
      :table="{
        extraParams: {
          code: activeKey,
        },
        labelExtraRender: () =>
          labelExtraRender(columns, activeKey, (value) => {
            activeKey = value;
          }),
        setSinglePointParams: (data) => getSinglePointUrl(data),
        api: listUrl,
        singlePointUrl: singlePointUrl,
        label: '信息列表',
        columns: columnItem.columns,
      }"
    />
  </PageWrapper>
</template>
