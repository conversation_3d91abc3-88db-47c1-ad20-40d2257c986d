
export const listUrl = "/subsyStemCallController/getPublicizeList"
export const singlePointUrl = '/singleLoginController/getPublicizeUrl'


export const renderSummaryColumns = () => {
    return [
        {
            title: "企业业绩",
            dataIndex: "performance",
            columns: [
                {
                    title: '单位名称',
                    dataIndex: 'companyName',
                },
                {
                    title: '统一社会信用代码',
                    dataIndex: 'legalCode',
                    width: 300
                },
                {
                    title: '标段编号',
                    dataIndex: 'bidSectionCodes',
                    width: 300
                },
                {
                    title: '标段名称',
                    dataIndex: 'bidSectionNames',
                },

            ]

        },
        {
            dataIndex: "companyCertification",
            title: "资质等级证书",
            columns: [
                {
                    title: '单位名称',
                    dataIndex: 'companyName',
                },
                {
                    title: '统一社会信用代码',
                    dataIndex: 'legalCode',
                },
                {
                    title: '类别及等级',
                    dataIndex: 'classifyAndLevel',
                },
                {
                    title: '证书编号',
                    dataIndex: 'certificateNumber',
                },
                {
                    title: '发证机关',
                    dataIndex: 'issuingAuthority',
                }
            ]

        },
        {
            dataIndex: "qualificationLicense",
            title: "资格许可证书",
            columns: [
                {
                    title: '单位名称',
                    dataIndex: 'companyName',
                },
                {
                    title: '统一社会信用代码',
                    dataIndex: 'legalCode',
                },
                {
                    title: '类别及等级',
                    dataIndex: 'classifyAndLevel',
                },
                {
                    title: '证书编号',
                    dataIndex: 'certificateNumber',
                },
                {
                    title: '发证机关',
                    dataIndex: 'issuingAuthority',
                }
            ]

        },
        {
            dataIndex: "securityClearance",
            title: "安全生产许可证书",
            columns: [
                {
                    title: '单位名称',
                    dataIndex: 'companyName',
                },
                {
                    title: '统一社会信用代码',
                    dataIndex: 'legalCode',
                },
                {
                    title: '主要负责人',
                    dataIndex: 'aqxkZyfzr',
                },
                {
                    title: '发证机关',
                    dataIndex: 'issuingAuthority',
                },
                {
                    title: '证书编号',
                    dataIndex: 'certificateNumber',
                }
            ]

        },
        {
            dataIndex: "personJobTitle",
            title: "专业技术职称",
            columns: [
                {
                    title: '单位名称',
                    dataIndex: 'companyName',
                },
                {
                    title: '姓名',
                    dataIndex: 'username',
                },
                {
                    title: '职称专业',
                    dataIndex: 'professionName',
                },
                {
                    title: '职称级别',
                    dataIndex: 'certificateLevel',
                },
                {
                    title: '编号',
                    dataIndex: 'certificateNumber',
                },
                {
                    title: '评审单位',
                    dataIndex: 'licenseOffice',
                },
                {
                    title: '通过日期',
                    dataIndex: 'licenseTime',
                }
            ]

        },
        {
            dataIndex: "personCRCM",
            title: "注册执业人员",
            columns: [
                {
                    title: '单位名称',
                    dataIndex: 'companyName',
                },
                {
                    title: '姓名',
                    dataIndex: 'username',
                },
                {
                    title: '注册执业名称',
                    dataIndex: 'certificateType',
                },
                {
                    title: '注册专业',
                    dataIndex: 'profession',
                },
                {
                    title: '注册编号',
                    dataIndex: 'serialNumber',
                },
                {
                    title: '聘用企业',
                    dataIndex: 'employer',
                }
            ]
        },
        {
            dataIndex: "professionalQualification",
            title: "职业岗位资格",
            columns: [
                {
                    title: '单位名称',
                    dataIndex: 'companyName',
                },
                {
                    title: '姓名',
                    dataIndex: 'username',
                },
                {
                    title: '证件类型',
                    dataIndex: 'certificateType',
                },
                {
                    title: '岗位名称',
                    dataIndex: 'appendix',
                },
                {
                    title: '证书编号',
                    dataIndex: 'serialNumber',
                },
                {
                    title: '发证日期',
                    dataIndex: 'issuingDate',
                },
                {
                    title: '有效期至',
                    dataIndex: 'endDate',
                }
            ]

        },
        {
            dataIndex: "personSpecialWork",
            title: "特种作业资格人员",
            columns: [
                {
                    title: '单位名称',
                    dataIndex: 'companyName',
                },
                {
                    title: '姓名',
                    dataIndex: 'username',
                },
                {
                    title: '证件类型',
                    dataIndex: 'certificateType',
                },
                {
                    title: '操作类别',
                    dataIndex: 'operateType',
                },
                {
                    title: '证书编号',
                    dataIndex: 'serialNumber',
                },
                {
                    title: '有效期至',
                    dataIndex: 'endDate',
                }
            ]

        },
        {
            dataIndex: "personHonorAndAwards",
            title: "荣誉表彰(个人)",
            columns: [
                {
                    title: '单位名称',
                    dataIndex: 'companyName',
                },
                {
                    title: '姓名',
                    dataIndex: 'username',
                },
                {
                    title: '奖项名称',
                    dataIndex: 'awardName',
                },
                {
                    title: '颁奖单位',
                    dataIndex: 'awardingUnit',
                }
            ]

        },
        {
            dataIndex: "managementSystemCertification",
            title: "管理体系认证",
            columns: [
                {
                    title: '单位名称',
                    dataIndex: 'companyName',
                },
           
                {
                    title: '证书名称',
                    dataIndex: 'zsmc',
                },
                {
                    title: '证书编号/注册号',
                    dataIndex: 'zsbh',
                }
            ]

        },
        {
            dataIndex: "projectAward",
            title: "工程获奖",
            columns: [
                {
                    title: '单位名称',
                    dataIndex: 'companyName',
                },
                {
                    title: '获奖项目(标段)名称',
                    dataIndex: 'bidSectionNames',
                },
                {
                    title: '奖项类别',
                    dataIndex: 'typename',
                },
                {
                    title: '奖项名称',
                    dataIndex: 'jxmc',
                },
                {
                    title: '颁奖单位',
                    dataIndex: 'bjdw',
                }
            ]

        },
        {
            dataIndex: "corporateCredit",
            title: "企业信用",
            columns: [
                {
                    title: '单位名称',
                    dataIndex: 'companyName',
                },
                {
                    title: '统一社会信用代码',
                    dataIndex: 'legalCode',
                },
                {
                    title: '信用（资信）类型',
                    dataIndex: 'xylx',
                },
                {
                    title: '级别',
                    dataIndex: 'standard',
                }
            ]

        },
        {
            dataIndex: "companyHonour",
            title: "荣誉表彰",
            columns: [
                {
                    title: '单位名称',
                    dataIndex: 'companyName',
                },
                {
                    title: '统一社会信用代码',
                    dataIndex: 'legalCode',
                },
                {
                    title: '奖项名称',
                    dataIndex: 'jxmc',
                },
                {
                    title: '颁奖单位',
                    dataIndex: 'bjdw',
                }
            ]
        }
    ]
}

