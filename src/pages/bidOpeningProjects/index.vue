<template>
    <PageWrapper>
        <div class="project-main">
            <div class="project-city" v-if="isShowList">
                <City
                    :selectCityId="queryParams.regionCode"
                    @selectCity="handleSelectCity"
                    :city-list="cityLists"
                ></City>
            </div>
            <div class="project-content">
                <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" class="custom-form">
                    <el-form-item label="标段名称" prop="bidSectionName" class="custom-form-item">
                        <el-input v-model="queryParams.bidSectionName" placeholder="请输入标段名称" clearable
                                  class="custom-input"
                                  @keyup.enter="handleQuery"/>
                    </el-form-item>
                    <el-form-item label="开标地点" prop="openBidAddress" class="custom-form-item">
                        <el-input v-model="queryParams.openBidAddress" placeholder="请输入开标地点" clearable
                                  class="custom-input"
                                  @keyup.enter="handleQuery"/>
                    </el-form-item>
                    <el-form-item label="开标时间" class="custom-form-item">
                        <el-date-picker
                            v-model="queryParams.startTime"
                            type="datetime"
                            placeholder="选择开始时间"
                            value-format="YYYY-MM-DD HH:mm:ss"
                            class="custom-datepicker"
                            :picker-options="startTimePickerOptions"
                        />
                        <span style="margin: 0 8px">至</span>
                        <el-date-picker
                            v-model="queryParams.endTime"
                            type="datetime"
                            placeholder="选择结束时间"
                            value-format="YYYY-MM-DD HH:mm:ss"
                            class="custom-datepicker"
                            :picker-options="endTimePickerOptions"
                        />
                    </el-form-item>
                    <el-form-item class="custom-form-item">
                        <el-radio-group v-model="queryParams.isOpenBid" @change="handleQuery">
                            <el-radio :label="true" class="custom-radio">已开标</el-radio>
                            <el-radio :label="false" class="custom-radio">未开标</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item class="custom-form-item">
                        <el-button type="primary" icon="Search" @click="handleQuery" class="custom-button">搜索</el-button>
                    </el-form-item>
                    <el-form-item class="custom-form-item">
                        <el-button type="warning" icon="Setting" @click="handleExport" class="custom-button">导出</el-button>
                    </el-form-item>
                    <el-form-item class="custom-form-item" v-if="user.userType == 99">
                        <el-button type="success" icon="Calendar" @click="handleTomorrowBidOpen" class="custom-button">明日开标</el-button>
                    </el-form-item>
                </el-form>
                <el-table v-loading="loading" :data="platformList" @selection-change="handleSelectionChange"
                          :row-class-name="tableRowClassName"
                          class="my-table"
                          style="width: 100%">
                    <el-table-column
                        label="序号"
                        type="index"
                        width="60px"
                        :index="indexMethod"
                        align="center"
                        fixed="left"
                    />
                    <el-table-column label="标段名称" align="center" key="bidSectionName" width="300px" :show-overflow-tooltip="true" fixed="left">
                        <template #default="{ row }">
                            <div class="bid-name-cell">
                                <span v-if="row.VOID_FLAG === 1" class="void-tag">【已作废】</span>
                                <span class="bid-name-text">{{ row.bidSectionName }}</span>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column label="标段编号" align="center" key="bidSectionCode" prop="bidSectionCode" width="300px"
                                     :show-overflow-tooltip="true" fixed="left"/>
                    <el-table-column label="标段类别" align="center" key="bidSectionContent" prop="bidSectionContent"
                                     :show-overflow-tooltip="true"/>
                    <el-table-column label="开标时间" align="center" key="bidOpenTime" prop="bidOpenTime" width="250px"
                                     :show-overflow-tooltip="true"/>
                    <el-table-column label="代理名称" align="center" key="tenderAgentName" prop="tenderAgentName" width="150px"
                                     :show-overflow-tooltip="true"/>
                    <el-table-column label="联系人" align="center" key="tenderAgentContact" prop="tenderAgentContact" width="100px"
                                     :show-overflow-tooltip="true"/>
                    <el-table-column label="联系电话" align="center" key="tenderAgentContactPhone" prop="tenderAgentContactPhone" width="150px"
                                     :show-overflow-tooltip="true"/>
                    <el-table-column label="开标地点" align="center" key="openBidAddress" prop="openBidAddress" width="250px"
                                     :show-overflow-tooltip="true"/>
                    <el-table-column label="评标组织形式" align="center" key="organizationType" prop="organizationType" width="150px"
                                     :show-overflow-tooltip="true"/>
                    <el-table-column label="所属区域" align="center" key="regionName" prop="regionName"
                                     :show-overflow-tooltip="true"/>
                    <el-table-column label="专家地区/是否主场" align="center" key="site" prop="site" width="200px"
                                     :show-overflow-tooltip="true"/>
<!--                    <el-table-column label="操作" v-if="user.level == 2" align="center" width="150" class-name="small-padding fixed-width" fixed="right">-->
<!--                        <template #default="scope">-->
<!--                            <el-tooltip content="权限分配" placement="top" v-if="scope.row.userId !== 1">-->
<!--                                <el-button link type="primary" icon="CircleCheck" @click="handleAuthRole(scope.row)">权限分配</el-button>-->
<!--                            </el-tooltip>-->
<!--                        </template>-->
<!--                    </el-table-column>-->
                </el-table>
                <div class="pagination-box">
                    <el-pagination
                        class="pagination"
                        background
                        v-model:current-page="queryParams.pageNum"
                        v-model:page-size="queryParams.pageSize"
                        :total="total"
                        layout=" total, prev, pager, next"
                        @current-change="getList"
                    />
                </div>
            </div>
        </div>
    </PageWrapper>
    <MenuPermissionDialog
      v-model="showMenuDialog"
      :row="currentRow"
    />
</template>

<script setup name="User">
import {httpGet, httpPost} from "@wl-fe/http/dist";
import {getOptionList} from "@/pages/project/projectInstruct/formOptionList.js";
import {useRoute, useRouter} from "vue-router";
import { ref, reactive, watch, onMounted } from 'vue';
import City from "@/pages/home/<USER>/riskWarningList/cityList/index.vue";
import MenuPermissionDialog from '@/pages/bidOpeningProjects/MenuPermissionDialog.vue';

// import { getToken } from "@/utils/auth";
// import { changeUserStatus, listUser, resetUserPwd, delUser, getUser, updateUser, addUser, deptTreeSelect } from "@/api/system/user";

const endTimePickerOptions = ref({
    disabledDate(time) {
        if (!queryParams.value.startTime) {
            return false;
        } else {
          return time.getTime() < queryParams.value.startTime;
        }
    }
});

const router = useRouter();
const route = useRoute();
const {proxy} = getCurrentInstance();
const sys_normal_disable = ref("");
const platformList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const platformName = ref("");
const cityLists = ref([]);
const isShowList = ref(false);
import useGloBalStore from "@/store/useGlobalStore"
const { user } = useGloBalStore();
/*** 用户导入参数 */
const upload = reactive({
    // 是否显示弹出层（用户导入）
    open: false,
    // 弹出层标题（用户导入）
    title: "",
    // 是否禁用上传
    isUploading: false,
    // 是否更新已经存在的用户数据
    updateSupport: 0,
    // 设置上传的请求头部
    //   headers: { Authorization: "Bearer " + getToken() },
    // 上传的地址
    url: import.meta.env.VITE_APP_BASE_API + "/system/user/importData"
});
const indexMethod = (index) => {
    return index + 1;
};
const data = reactive({
    form: {},
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        operatingUnit: undefined,
        phonenumber: undefined,
        status: undefined,
        deptId: undefined,
        bidSectionName: undefined,
        openBidAddress: undefined,
        startTime: undefined,
        endTime: undefined,
        isOpenBid: false, // 默认值设为false
        regionCode: undefined
    },
    rules: {
        operatingUnit: [{required: true, message: "用户名称不能为空", trigger: "blur"}, {
            min: 2,
            max: 20,
            message: "用户名称长度必须介于 2 和 20 之间",
            trigger: "blur"
        }],
        platformCode: [{required: true, message: "用户昵称不能为空", trigger: "blur"}],
        password: [{required: true, message: "用户密码不能为空", trigger: "blur"}, {
            min: 5,
            max: 20,
            message: "用户密码长度必须介于 5 和 20 之间",
            trigger: "blur"
        }, {pattern: /^[^<>"'|\\]+$/, message: "不能包含非法字符：< > \" ' \\\ |", trigger: "blur"}],
        email: [{type: "email", message: "请输入正确的邮箱地址", trigger: ["blur", "change"]}],
        phonenumber: [{pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/, message: "请输入正确的手机号码", trigger: "blur"}]
    }
});

const {queryParams, form, rules} = toRefs(data);
const value = ref('')
watch(() => queryParams.value.startTime, (newVal) => {
    if (newVal && queryParams.value.endTime && newVal > queryParams.value.endTime) {
        queryParams.value.endTime = newVal;
    }
});

watch(() => queryParams.value.endTime, (newVal) => {
    if (newVal && queryParams.value.startTime && newVal < queryParams.value.startTime) {
        queryParams.value.startTime = newVal;
    }
});
/** 查询用户列表 */
async function getList() {
    sys_normal_disable.value = await getOptionList('sys_normal_disable');
    loading.value = true;
    const result = await httpPost("/inviteTender/bidSection/getBidOpeningPreview" + `?pageNum=${queryParams.value.pageNum}&pageSize=${queryParams.value.pageSize}`, queryParams.value)
    loading.value = false;
    platformList.value = result.rows;
    total.value = result.total;
};

/** 搜索按钮操作 */
function handleQuery() {
    if (queryParams.value.startTime && queryParams.value.endTime) {
        if (queryParams.value.startTime > queryParams.value.endTime) {
            ElMessage.error('开始时间不能大于结束时间');
            return;
        }
    }
    queryParams.value.pageNum = 1;
    getList();
};

/** 查询明日开标 */
async function handleTomorrowBidOpen() {
    const result = await httpPost("/inviteTender/bidSection/getTomorrowBidOpen", queryParams.value)
    if (result.code === 200) {
        ElMessage.success(result.msg);
        getList();
    } else {
        ElMessage.error(result.msg);
    }
}

const tableRowClassName = ({rowIndex}) => {
    if (rowIndex % 2 !== 0) {
        return "warning-row";
    }
    return "";
};

/** 选择条数  */
function handleSelectionChange(selection) {
    ids.value = selection.map(item => item.userId);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
};

/** 重置操作表单 */
function reset() {
    form.value = {
        userId: undefined,
        deptId: undefined,
        operatingUnit: undefined,
        platformCode: undefined,
        password: undefined,
        phonenumber: undefined,
        email: undefined,
        sex: undefined,
        status: "0",
        remark: undefined,
        postIds: [],
        roleIds: []
    };
    //   proxy.resetForm("userRef");
};

/** 取消按钮 */
function cancel() {
    open.value = false;
    reset();
};

async function first() {
    let {query} = toRefs(route);
    console.log('query', route)
    const {
        regionName
    } = query.value;
    await nextTick();
    platformName.value = regionName
    regionName.value = ''
};

function handleSelectCity(item) {
    queryParams.value.regionCode = item.code;
    handleQuery();
}

onMounted(async () => {
    const list = await httpGet("/api/sysRegion/list", {});
    cityLists.value = list;
    isShowList.value = cityLists.value.length > 0;
    if (cityLists.value.length > 0) {
        queryParams.value.regionCode = user.regionCode;
    }
    getList();
});

/** 导出按钮操作 */
async function handleExport() {
    const response = await httpPost(
        "/inviteTender/bidSection/exportBidOpeningPreview",
        queryParams.value,
        {
            ignoreTransferResult: true ,
            responseType: 'blob' // 必须指定为blob类型
        }
    );
    const blob = new Blob([response], {
        type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.setAttribute("download", "开标一览表.xlsx");
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

};

const showMenuDialog = ref(false);
const currentRow = ref(null);

// 菜单权限假数据
const sys_menu_operate = ref([
  { label: '首页', value: 1 },
  { label: '用户管理', value: 2 },
  { label: '角色管理', value: 3 },
  { label: '菜单管理', value: 4 },
  { label: '系统设置', value: 5 }
]);

function handleAuthRole(row) {
  currentRow.value = row;
  showMenuDialog.value = true;
}
function handleMenuPermissionOk(val) {
  // 这里可以将val（选中的菜单权限ID数组）保存到后端或row
  // 例如：row.menuIds = val
  // 或发请求保存
}

</script>
<style scoped lang="less">
.project-main {
    display: flex;
    width: 100%;
    height: 100%;
}
.project-city {
    width: 180px;
    min-width: 120px;
    max-width: 220px;
    height: 100%;
    margin-right: 16px;
    overflow: auto;
    flex-shrink: 0;
}
.project-content {
    flex: 1;
    width: 0;
    display: flex;
    flex-direction: column;
    overflow: hidden; // 防止内容溢出
}
.custom-form {
    display: flex;
    flex-wrap: wrap;
    align-items: flex-start;
    justify-content: flex-start;
    gap: 0px 0; // 行间距20px，列间距0
    margin-bottom: 0; // 避免和表格之间有空白
}
.custom-form-item {
    margin-bottom: 16px; // 换行后有间距
}
.custom-input,
.custom-datepicker {
    width: 240px; // 统一输入框和日期选择器的宽度
    border-radius: 4px; // 圆角
    transition: border-color 0.3s; // 边框颜色过渡效果

    &:focus {
        border-color: #409EFF; // 聚焦时的边框颜色
        box-shadow: 0 0 5px rgba(64, 158, 255, 0.5); // 聚焦时的阴影效果
    }
}

.custom-button {
    height: 33px; // 统一按钮高度
    padding: 0 20px; // 按钮内边距
    border-radius: 4px; // 圆角
    font-size: 16px; // 字体大小
    transition: background-color 0.3s, color 0.3s; // 背景颜色和字体颜色过渡效果

    &:hover {
        background-color: #66b1ff; // 鼠标悬停时的背景颜色
        color: #fff; // 鼠标悬停时的字体颜色
    }
}

.pagination-box {
    margin-top: 20px;
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
}

.my-table {
    :deep(.el-table__header-wrapper) {
        .el-table__cell {
            background: #f0f6ff;
            height: 55px;
            color: rgba(0, 0, 0, 0.88);
        }
    }

    :deep(.el-table__row) {
        height: 55px;
    }

    :deep(.warning-row) {
        background: #f9fbff;
    }

    :deep(.el-table__body-wrapper) {
        overflow-x: auto !important;

        &::-webkit-scrollbar {
            width: 12px !important;
            height: 12px !important;
            background-color: #f5f7fa !important;
        }

        &::-webkit-scrollbar-thumb {
            background-color: #409EFF !important;
            border-radius: 6px !important;
            border: 2px solid #f5f7fa !important;
            min-width: 50px !important;

            &:hover {
                background-color: #66b1ff !important;
            }
        }

        &::-webkit-scrollbar-track {
            background-color: #f5f7fa !important;
            border-radius: 6px !important;
        }

        &::-webkit-scrollbar-corner {
            background-color: #f5f7fa !important;
        }
    }
}

/* 确保滚动条样式在Firefox中也能生效 */
.my-table :deep(.el-table__body-wrapper) {
    scrollbar-width: thin;
    scrollbar-color: #409EFF #f5f7fa;
}

.viewPdfbox {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;

    img {
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
    }

    * {
        width: 100%; /* 确保所有子元素的最大宽度为100% */
        box-sizing: border-box; /* 确保内边距和边框包含在宽度内 */
    }
}

.dialog-footer {
    display: flex;
    justify-content: center;
    margin-top: 10px;
}
.bid-name-cell {
    display: flex;
    align-items: center;
    gap: 4px;

    .void-tag {
        color: #ff4d4f;
        font-weight: 500;
        flex-shrink: 0;
    }

    .bid-name-text {
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .custom-radio {
        margin-right: 15px;

        :deep(.el-radio__label) {
            color: #606266;
            font-size: 14px;
        }

        &:hover :deep(.el-radio__inner) {
            border-color: #409EFF;
        }
    }
}

.project-content {
    width: 100%;
    display: flex;
    justify-content: normal;
}

.project-city {
    width: 8%;
    height: 100%;
    margin-right: 10px;
    overflow: auto;
    flex-shrink: 0;
}

.search-form {
    width: 92%;
}
</style>
