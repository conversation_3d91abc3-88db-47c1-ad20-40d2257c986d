<template>
    <el-dialog
            :title="title"
            v-model="visible"
            width="400px"
            append-to-body
            @close="handleClose"
    >
        <template v-if="!loading">
            <el-form :disabled="isDisabled">
                <el-form-item label="选择监管人员">
                    <el-select v-model="localrows" multiple placeholder="请选择" style="width: 100%;" :disabled="isDisabled">
                        <el-option
                                v-for="dict in (sysUserArr.value || [])"
                                :key="dict.userName"
                                :label="dict.nickName"
                                :value="String(dict.userName)"
                                :disabled="isDisabled"
                        ></el-option>
                    </el-select>
                </el-form-item>
            </el-form>
        </template>
        <template v-else>
            <el-skeleton :rows="1" animated style="width: 100%;"/>
        </template>
        <template #footer>
            <div class="dialog-footer">
                <el-button v-if="!isDisabled" type="primary" @click="handleOk">确 定</el-button>
                <el-button @click="handleClose">取 消</el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup>
import {onMounted, ref, watch, computed} from 'vue';
import {httpGet, httpPost} from "@wl-fe/http/dist/index.js";

const props = defineProps({
    modelValue: Boolean,
    row: {
        type: Object,
        default: () => ({})
    },
    title: {
        type: String,
        default: '权限分配'
    }
});
const emit = defineEmits(['update:modelValue', 'ok', 'cancel']);

const visible = ref(props.modelValue);
watch(() => props.modelValue, val => visible.value = val);
watch(visible, val => emit('update:modelValue', val));

// 假数据
// const sysUserArr = [
//   { label: '首页', value: 1 },
//   { label: '用户管理', value: 2 },
//   { label: '角色管理', value: 3 },
//   { label: '菜单管理', value: 4 },
//   { label: '系统设置', value: 5 }
// ];
const sysUserArr = [];


const localrows = ref([]);
const loading = ref(true);

const isDisabled = computed(() => {
    if (!props.row || !props.row.bidOpenTimeNew) return false;
    return new Date(props.row.bidOpenTimeNew) < new Date();
});

watch(visible, (val) => {
    if (val && props.row) {
        loading.value = true;
        selectUserByProject(props.row);
    }
});

async function selectUserByProject(row) {
    const data = await httpPost("/system/user/selectUserByProjectGuid",row);
    sysUserArr.value = data.userList;
    localrows.value = data.permissionList.map(t=>t.userName);
    loading.value = false;
};


function handleOk() {
    // if (!localrows.value.length) {
    //     ElMessage.warning("请选择监管人员");
    //     return;
    // }
    const payload = {
        bidSectionGuid: props.row.bidSectionGuid,
        dogNumArr: Array.from(localrows.value)
    };
    httpPost('/inviteTender/bidSection/saveUserByProjectGuid', payload).then(() => {
        visible.value = false;
        ElMessage.success("权限分配成功");
    });
}

function handleClose() {
    emit('cancel');
    visible.value = false;
}
</script> 