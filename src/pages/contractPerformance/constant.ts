import { ColumnItemType } from "@/type"
import { MODULE_TYPE_CONTRACT_PERFORMANCE } from "@/constant/module"
import { STATUS_ICONS_MAP } from "@/constant/icon"
import { statusRender, summaryWrapperRender } from "@/models/searchList/constant"
import { StatusType } from "@/models/searchList/type"


const customStyle = {
    labelStyle: {
        marginTop: '7.5%'
    },
    countStyle: {
        marginBottom: "8%"
    }
}

export const renderSummaryColumns = (): ColumnItemType[] => {
    return [
        {
            title: "全部",
            dataIndex: "allNum",

        },
        {
            title: "已签订",
            dataIndex: "signNum"
        },
        {
            title: "未签订",
            dataIndex: "noSignNum"
        },
        {
            title: "履约中",
            dataIndex: "perNum"
        },
        {
            title: "已完成",
            dataIndex: "overNum"
        }
    ].map((item) => {
        return {
            styles: customStyle,
            ...item,
            render: (text: any, record: any, index: number, column: ColumnItemType) => summaryWrapperRender(text, record, index, column, `${MODULE_TYPE_CONTRACT_PERFORMANCE}/summary/${index + 1}.png`)
        }
    }) as ColumnItemType[]
}



const htStatusRender = (text: number) => {
    const statusMap: StatusType = {
        0: {
            label: "未签订",
            icon: STATUS_ICONS_MAP['status_error']
        },
        1: {
            label: "已签订",
            icon: STATUS_ICONS_MAP['status_success']

        }
    }
    return statusRender(statusMap, text)
}


const performStatusRender = (text: string) => {
    const statusMap: StatusType = {
        0: {
            label: "履约中",
            icon: STATUS_ICONS_MAP['status_ing']
        },
        1: {
            label: "已竣工",
            icon: STATUS_ICONS_MAP['status_success']
        },
        2: {
            label: "已完成",
            icon: STATUS_ICONS_MAP['status_success']
        }
    }
    return statusRender(statusMap, text)
}

export const renderTableColumns = (): ColumnItemType[] => {
    return [
        {
            title: '标段唯一标识码',
            dataIndex: 'bdRowguid',
            width: 280
        },
        {
            title: '标段名称',
            dataIndex: 'bdName',
            width: 300
        },
        {
            title: '发包人',
            dataIndex: 'fbrName',
        },
        {
            title: '承包人',
            dataIndex: 'cbrName',
        },
        {
            title: '签约状态',
            dataIndex: 'htStatus',
            render: htStatusRender
        },
        {
            title: '签订时间',
            dataIndex: 'htqdTime',
        },
        {
            title: '履约状态',
            dataIndex: 'performStatus',
            render: performStatusRender
        }
    ]
}
