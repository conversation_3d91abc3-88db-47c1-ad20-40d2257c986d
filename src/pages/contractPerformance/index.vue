<!-- 合同履约界面 -->
<script setup>
import SearchList from "@/models/searchList/index.vue";
import { renderSummaryColumns, renderTableColumns } from "./constant";
</script>
<template>
  <PageWrapper>
    <SearchList
      :summary="{
        key: 'statsData',
        columns: renderSummaryColumns(),
      }"
      :table="{
        singlePointUrl: '/singleLoginController/getContractPerformanceUrl',
        api: '/subsyStemCallController/getContractPerformanceList',
        label: '合同列表',
        columns: renderTableColumns(),
      }"
    />
  </PageWrapper>
</template>

