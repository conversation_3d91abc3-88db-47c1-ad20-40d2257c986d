import { createRouter, createWeb<PERSON>ashHistory } from "vue-router";
import routes from "./constant";
import { getUrlPath } from "@/utils/url";
import { checkUserInfo } from "@/constant";
const router = createRouter({
    history: createWebHashHistory(`/${import.meta.env.VITE_PROXY_SECMENU}/`),
    routes,
})
export const BASE_PATH = '/'
export const HOME_PATH = 'home'
export const LOGIN_PATH = 'login'
export const TEST_PATH = '/test'
router.beforeEach(async (to, from, next) => {
    const path = getUrlPath()
    console.log('path',path,to, from);
    const isSuccess = await checkUserInfo()
    if(to.path == '/login' ){
        next()
    } else if (!isSuccess && !path.includes(LOGIN_PATH)) {
        // alert('请先登录')
        //  window.location.href=`/${import.meta.env.VITE_PROXY_SECMENU}/#/${LOGIN_PATH}`
        next(LOGIN_PATH)
    } else {
        next()

    }
})
export default router