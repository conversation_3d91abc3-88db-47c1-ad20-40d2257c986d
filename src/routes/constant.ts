import {
  M<PERSON><PERSON><PERSON>_TYPE_ARCHIVE_INFORMATION,
  MODULE_TYPE_BID,
  MODULE_TYPE_CONTRACT_PERFORMANCE,
  MODULE_TYPE_HOME,
  MODULE_TYPE_INFORMATION_DISCLOSURE,
  MODULE_TYPE_LAW_CHECK,
  MODULE_TYPE_OBJECTION_COMPLAINT,
  MODULE_TYPE_PROJECT,
  MODULE_TYPE_PROJECT_LIST,
  MODULE_TYPE_PROJECT_DETAIL,
  MODULE_TYPE_PROJECT_PRESS,
  MODULE_TYPE_PROJECT_REALNAME,
  MODULE_TYPE_SMART_REGULATION,
  MODULE_TYPE_SUBJECT,
  MODULE_TYPE_SUBJECT_EVALUATION,
  MODULE_TYPE_EXPORT_MAINSTORE,
  MODULE_TYPE_AGENT_EVALUATION,
  MODU<PERSON>_TYPE_TENDER,
  MOD<PERSON><PERSON>_TYPE_TRAINING_EXAMINATION,
  MODULE_TYPE_MAIN_STORE,
  MOD<PERSON><PERSON>_TYPE_PLATFORM_MANAGE,
  MODULE_TYPE_DECISION_MAKING,
  MODULE_TYPE_BID_OPENING_PROJECTS,
  MODULE_TYPE_CONSTRUCTION_MANAGE,
  MODULE_TYPE_CONSTRUCTION_DETAIL
} from "@/constant/module";
const routes = [
  {
    path: '/',
    component: () => import('@/layout/index.vue'),
    redirect: "/home",
    children: [
      {
        path: MODULE_TYPE_HOME,
        name:'home',
        component: () => import('@/pages/home/<USER>'),
        meta:{
          KeepAlive: true,
          name: 'home',
          title: '首页',
        }
      },
      {
        path: MODULE_TYPE_PROJECT,
        component: () => import('@/pages/project/index.vue'),
      },
      {
        path: MODULE_TYPE_PROJECT_LIST,
        component: () => import('@/pages/project/projectList/index.vue'),
      },
      {
        path: MODULE_TYPE_PROJECT_DETAIL,
        component: () => import('@/pages/project/projectDetail/index.vue'),
      },
      {
        path: MODULE_TYPE_PROJECT_REALNAME,
        component: () => import('@/pages/project/projectRealName/index.vue'),
      },
      {
        path: MODULE_TYPE_PROJECT_PRESS,
        name:'ProjectPress',
        component: () => import('@/pages/project/projectPress/index.vue'),
        meta: {
            name: 'ProjectPress',
            //	当前页面要缓存
            keepAlive: true,
            //	当前页面层级
            deepth: 3,
        }
      },
      {
        path: MODULE_TYPE_SUBJECT,
        // component: () => import('@/pages/subject/index.vue'),
        component: () => import('@/pages/mainStore/index.vue'),
      },
      {
        path: MODULE_TYPE_TENDER,
        // component: () => import('@/pages/tender/index.vue'),
          component: () => import('@/pages/mainStore/index.vue'),
      },
      {
        path: MODULE_TYPE_OBJECTION_COMPLAINT,
        component: () => import('@/pages/objectionComplaint/index.vue'),
      },
      {
        path: MODULE_TYPE_CONTRACT_PERFORMANCE,
        // component: () => import('@/pages/contractPerformance/index.vue'),
        component: () => import('@/pages/mainStore/index.vue'),
      },
      {
        path: MODULE_TYPE_LAW_CHECK,
        // component: () => import('@/pages/lawCheck/index.vue'),
        component: () => import('@/pages/mainStore/index.vue'),
      },
      {
        path: MODULE_TYPE_INFORMATION_DISCLOSURE,
        component: () => import('@/pages/informationDisclosure/index.vue'),
      },
      {
        path: MODULE_TYPE_SUBJECT_EVALUATION,
        component: () => import('@/pages/subjectEvaluation/index.vue'),
        // component: () => import('@/pages/mainStore/index.vue'),
      },
      {
        path: MODULE_TYPE_EXPORT_MAINSTORE,
        component: () => import('@/pages/subjectEvaluation/evaluationMainStore/index.vue'),
        // component: () => import('@/pages/mainStore/index.vue'),
      },
	   {
        path: MODULE_TYPE_AGENT_EVALUATION,
        // component: () => import('@/pages/subjectEvaluation/index.vue'),
        component: () => import('@/pages/mainStore/index.vue'),
      },	  
      {
        path: MODULE_TYPE_TRAINING_EXAMINATION,
        component: () => import('@/pages/trainingExamination/index.vue'),
      },
      {
        path: MODULE_TYPE_ARCHIVE_INFORMATION,
        component: () => import('@/pages/archiveInformation/index.vue'),
      },
      {
        path: MODULE_TYPE_SMART_REGULATION,
        component: () => import('@/pages/smartRegulation/index.vue'),
      },
      {
        path: MODULE_TYPE_BID,
        component: () => import('@/pages/bid/index.vue'),
      },
      {
        path: MODULE_TYPE_PLATFORM_MANAGE,
        component: () => import('@/pages/platformManage/index.vue'),
      },
      {
        path: MODULE_TYPE_DECISION_MAKING,
        component: () => import('@/pages/decisionMaking/index.vue'),
      },
      //微应用
      {
        path: MODULE_TYPE_MAIN_STORE,
        component: () => import('@/pages/mainStore/index.vue'),
      },
      {
        path: MODULE_TYPE_BID_OPENING_PROJECTS,
        component: () => import('@/pages/bidOpeningProjects/index.vue'),
      },
      {
        path: MODULE_TYPE_CONSTRUCTION_MANAGE,
        component: () => import('@/pages/constructionManage/index.vue'),
      },
      {
        path: MODULE_TYPE_CONSTRUCTION_DETAIL,
        component: () => import('@/pages/constructionManage/constructionDetail/index.vue'),
      },
    ]
  },
  {
    path: '/login',
    component: () => import('@/pages/login/index.vue')
  },
]

export default routes