import { ColumnItemType, SearchTableParamsType } from "@/type"

export type SearchListModelItemType = {
    extraParams: Record<string, any>
    label?: string
    labelExtraRender?: () => any
    columnClassWrapper?: string
    api?: string,
    key?: string,
    singlePointUrl?: string
    setSinglePointParams: (data: any) => any
    columns: ColumnItemType[]
}
export type ChangeConditionType = (params: SearchTableParamsType) => void


export type TabModuleItemType = {
    label: string,
    id: string;
    columns: ColumnItemType[]
}

export type TypeOptionsType = {
    options: TabModuleItemType[]
    changeCallback?: (key: string) => void
}

export type StatusType = Record<string, { label: string, color?: string, icon?: string }>