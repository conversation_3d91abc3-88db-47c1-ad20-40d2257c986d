<script setup>
import { httpGet } from "@wl-fe/http/dist";
import { openUrl } from "@/utils";
const attrs = useAttrs();
const props = defineProps({
  loading: Boolean,
  columns: Array,
  tableData: Object,
  condition: Object,
});
const emit = defineEmits(["changeCondition", "setSinglePointParams"]);
const getJumpUrl = async (record) => {
  const { singlePointUrl, setSinglePointParams } = attrs;
  if (singlePointUrl) {
    const result = await httpGet(
      singlePointUrl,
      setSinglePointParams ? setSinglePointParams(record) : {},
      {
        transferResult: (result) => {
          return result;
        },
      }
    );
    openUrl(result);
  }
};
const imgUrl =  `/${import.meta.env.VITE_PROXY_SECMENU}/components/row-oper.png`;
const getColumns = () => {
  const list = [...props.columns];
  const { singlePointUrl, onRowOperClick } = attrs;
  if (singlePointUrl || onRowOperClick) {
    list.push({
      title: "操作",
      dataIndex: "operation",
      width: 80,
      align: "center",
      render: (text, record) =>
        h(
          "span",
          {
            style: {
              cursor: "pointer",
              color: "#4D94FC",
              fontSize: "18px",
            },
            onClick: () =>
              onRowOperClick ? onRowOperClick(record) : getJumpUrl(record),
          },
          h("img", {
            src: imgUrl,
            class: "oper-icon",
          })
        ),
    });
  }
  return list;
};
</script>
<template>
  <div class="list" v-loading="loading">
    <WlTable :tableColumns="getColumns()" :tableData="tableData.rows" />
    <el-pagination
      class="pagination"
      background
      v-model:current-page="condition.pageNum"
      v-model:page-size="condition.pageSize"
      :total="tableData.total"
      :size="condition.pageSize"
      :page-sizes="[100, 200, 300, 400]"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="handleSizeChange"
      @current-change="
        (page) => {
          emit('changeCondition', {
            pageNum: page,
          });
        }
      "
    />
  </div>
</template>

<style scoped lang="less">
.list {
  flex: 1;
  height: 0px;
  display: flex;
  flex-direction: column;
  :deep .oper-icon {
    width: 24px;
    height: 24px;
  }
  .pagination {
    margin-top: 12px;
    display: flex;
    justify-content: right;
    align-items: center;
    :deep(.el-pagination__total) {
      color: rgba(0, 0, 0, 0.88);
    }
  }
}
</style>
