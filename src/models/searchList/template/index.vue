<script setup>
import { VALUE_UNKNOWN } from "@/constant";
import { fixedNumber } from "@/utils/number";
defineProps({
  showCount: {
    type: Boolean,
    default: true,
  },
  text: String,
  column: Object,
  bgPath: String,
});
const emit = defineEmits(["onClickCallback"]);
</script>
<template>
  <div class="wrapper" @click="() => emit('onClickCallback')">
    <template v-if="bgPath">
      <img :src="bgPath" :class="['bg', 'wrapper-bg']" />
    </template>
    <div class="content">
      <div
        class="label"
        :style="{
          ...(column?.styles?.labelStyle || {}),
        }"
      >
        {{ column?.title }}
      </div>
      <template v-if="showCount">
        <div
          class="count"
          :style="{
            ...(column?.styles?.countStyle || {}),
          }"
        >
          {{ text ? fixedNumber(text, 4) : VALUE_UNKNOWN }}
        </div>
      </template>
      <span v-if="column.tag" class="tag" 
      :style="{
            ...(column?.styles?.tagStyle || {}),
          }"
      >
        {{column.tag}}
      </span>
    </div>
  </div>
</template>
<style scoped lang="less">
.wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  .bg {
    width: 100%;
    height: auto;
  }
  .content {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0px;
    left: 0px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .label {
      margin-top: 9%;
      padding-left: 20px;
      font-size: 20px;
      color: #fff;
      transform-origin: left top;
      width: 100%;
      text-wrap: nowrap;
    }
    .count {
      color: #fff;
      padding-left: 20px;
      font-size: 24px;
      font-weight: bold;
      transform-origin: left top;
      margin-bottom: 8%;
    }
    .tag{
      position: absolute;
      bottom: 2px;
      right: 2px;
      font-size: 14px;
      color: #fff
    }
  }
}
</style>
