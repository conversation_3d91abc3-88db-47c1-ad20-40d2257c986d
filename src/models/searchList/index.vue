<script setup lang="ts">
import { PropType } from "vue";
import { DEFAULT_CONDITION } from "./constant";
import { httpPost } from "@wl-fe/http/dist";
import Tabs from "./tabs.vue";
import Summary from "./summary.vue";
import Table from "./table.vue";

const props = defineProps({
  searchPlaceholder: String,
  tab: Object as PropType<any>,
  summary: Object as PropType<any>,
  table: Object as PropType<any>,
});

const { tab, summary, table } = toRefs(props);
const {
  api: summarySearchApi,
  key: summaryDataKey,
  label: summaryLabel,
  columns: summaryColumns,
} = summary?.value || {};
const {
  api: tableSearchApi,
  label: tableLabel,
  columns: tableColumns,
  labelExtraRender,
  extraParams: tableExtraParams,
} = table?.value || {};
const { changeCallback: tabChangeCallback } = tab?.value || {};

const changeTab = (e: string) => {
  tabChangeCallback?.(e);
};
const summaryLoading = ref(false);
const summaryData = ref({});
const tableLoading = ref(false);
const tableData = ref({});
const condition = ref({
  ...DEFAULT_CONDITION,
  ...(tableExtraParams || {}),
});
const getSummaryData = async () => {
  summaryLoading.value = true;
  try {
    const result = await httpPost(summarySearchApi as string, condition.value);
    summaryData.value = result;
  } catch (err) {
    summaryData.value = {};
  } finally {
    summaryLoading.value = false;
  }
};

const getTableData = async () => {
  tableLoading.value = true;
  if (summaryDataKey) {
    summaryLoading.value = true;
  }
  try {
    const result = await httpPost(tableSearchApi + `?pageNum=${condition.value.pageNum}&pageSize=${condition.value.pageSize}`, condition.value, {
      errorHandler: (error) => {
        console.log("getTableData", error);
      },
    });
    if (summaryDataKey) {
      summaryData.value = result[summaryDataKey];
    }
    tableData.value = result;
  } catch (err) {
    tableData.value = {};
  } finally {
    tableLoading.value = false;
    if (summaryDataKey) {
      summaryLoading.value = false;
    }
  }
};

watch(
  condition,
  () => {
    if (summarySearchApi) {
      getSummaryData();
    }
    if (tableSearchApi) {
      getTableData();
    }
  },
  {
    immediate: true,
    deep: true,
  }
);

watch(
  () => table?.value.extraParams,
  (newVal) => {
    condition.value = {
      ...condition.value,
      ...newVal,
    };
  }
);
</script>
<template>
  <div :style="{ height: '100%', display: 'flex', flexDirection: 'column',fontSize:'16px'}">
    <SearchButton
      :is-show-ai-tip="true"
      autofocus
      :placeholder="searchPlaceholder || '请输入标识码或名称'"
      @search="
        (e) => {
          condition = {
            ...condition,
            keyword: e,
          };
        }
      "
    />
    <template v-if="tab">
      <Tabs
        v-bind="tab"
        :active-key="condition.activeKey"
        @onChange="changeTab"
      />
    </template>
    <template v-if="summary">
      <template v-if="summaryLabel">
        <LabelTitle :title="summaryLabel" :has-marin="true" />
      </template>
      <template v-if="summaryColumns">
        <Summary
          v-bind="summary"
          :loading="summaryLoading"
          :summary-data="summaryData"
          :condition="condition"
        />
      </template>
    </template>
    <template v-if="table">
      <template v-if="tableLabel">
        <div :style="{ display: 'flex', justifyContent: 'space-between' }">
          <LabelTitle :title="tableLabel" :has-marin="true" ><span v-if="'招标人自主决策' == tableLabel" style="font-size: 14px; font-weight: initial; color: var(--el-text-color-regular);">（招标计划列表）</span> </LabelTitle>
          <div v-if="labelExtraRender">
            <component :is="labelExtraRender()" />
          </div>
        </div>
      </template>
      <template v-if="tableColumns">
        <Table
          :loading="tableLoading"
          v-bind="table"
          :table-data="tableData"
          :condition="condition"
          @change-condition="(params:any)=>{
            condition={
              ...condition,
              ...params
            }
          }"
        />
      </template>
    </template>
  </div>
</template>
