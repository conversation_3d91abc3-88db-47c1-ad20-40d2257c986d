<script setup>
import SummaryItemWrapper from './template/index.vue'
defineProps({
  options: Object,
  activeKey: String,
});
const emit = defineEmits(["onChange"]);
const secMenu = import.meta.env.VITE_PROXY_SECMENU
</script>

<template>
  <div class="tab">
    <div
      v-for="(optionItem,index) in options"
      :key="optionItem.id"
      :class="['tab-item', 'tab-item' + index , activeKey === optionItem.id && 'active']"
      @click="() => emit('onChange', optionItem.id)"
    >
      <div
        v-for="(item, index) in optionItem.columns"
        :key="item.dataIndex"
        class="module"
      >
        <SummaryItemWrapper
          :column="item"
          :record="{}"
          :text="item.text"
          :bgPath="`/${secMenu}/${$route.path}/summary/${optionItem.id}-${index + 1}-${
            activeKey === optionItem.id ? 'active' : 'unactive'
          }.png`"
        />
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.tab {
  display: flex;
  margin-top: 16px;
  margin-bottom: 20px;
  .tab-item {
    flex: 1;
    display: flex;
    padding: 16px;
    display: flex;
    border: 2px solid #f0f0f0;
    border-bottom: 2px solid #bed6fe;
    border-radius: 8px 8px 0px 0px;
    cursor: pointer;
    transition: all 0.9s;
    &.active {
      border: 2px solid #bed6fe;
      border-bottom: 2px solid #f0f0f0;
      transition: all 0.9s;
      .module {
        transform: scale(1);
      }
    }
    .module {
      flex: 1;
      transform: scale(0.8);
      transition: all 0.9s;
      &:not(:last-child) {
        margin-right: 8px;
      }
    }
  }
  .tab-item0{
     background: #c3dff73d; 
  }
  .tab-item1{
     background: #9aeac94d;
  }
  .tab-item2{
     background: #dfccb063;  
  }
}
</style>
