import { DEFAULT_PAGE_NUM, DEFAULT_PAGE_SIZE, VALUE_UNKNOWN } from "@/constant";
import SummaryItemWrapper from './template/index.vue'
import { StatusType } from "./type";
import { ColumnItemType } from "@/type";
import { ElOption, ElSelect } from "element-plus";

export const DEFAULT_CONDITION = {
    keyword: '',
    pageNum: DEFAULT_PAGE_NUM,
    pageSize: DEFAULT_PAGE_SIZE
}


export const statusRender = (statusMap: StatusType, text: string | number) => {
    const { label, icon } = statusMap[text] || {}
    return h('div', {
        class: ["status-item"]
    }, label ? [
        h('img', { src: icon }),
        h('span', label)
    ] : VALUE_UNKNOWN)
}

export const summaryWrapperRender = (text: string, record: any, index: number, column: ColumnItemType, bgPath: string) => {
    return h(SummaryItemWrapper, {
        bgPath,
        text,
        record,
        index,
        column
    })
}

export const labelExtraRender = (columns: any[], activeKey: string, onChange: (key: string) => void) => {
    return h(
        ElSelect,
        {
            style: {
                width: '160px',
            },
            modelValue: activeKey,
            'onUpdate:modelValue': (value) => {
                onChange(value)
            },
        },
        columns.map((item) =>
            h(ElOption, {
                label: item.title,
                value: item.dataIndex,
            })
        )
    )
}