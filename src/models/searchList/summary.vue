<script setup>
defineProps({
  loading: <PERSON><PERSON><PERSON>,
  columns: Array,
  summaryData: Object,
});
</script>
<template>
  <div class="summary">
    <div :class="['columns']">
      <div
        v-for="(item, index) in columns"
        :key="item.dataIndex"
        class="column-item"
      >
        <Spin :spinning="loading">
          <template v-if="item.render">
            <component
              :is="
                item.render(
                  summaryData[item.dataIndex],
                  summaryData,
                  index,
                  item
                )
              "
              :data="summaryData[item.dataIndex]"
              :summary-data="summaryData"
              :index="index"
              :item="item"
            />
          </template>
          <template v-else>
            {{ item.dataIndex }}
          </template>
        </Spin>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.summary {
  margin-top: 16px;
  .columns {
    display: flex;
    align-items: center;
    .column-item {
      padding: 4px;
      transform-origin: top left;
      &:not(:first-child) {
        margin-left: 16px;
      }
    }
  }
}
</style>
