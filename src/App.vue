<template>
      <a-config-provider :locale="zhCN">
            <RouterView /> 
      </a-config-provider>
</template>

<script  setup>

import zhCN from 'ant-design-vue/es/locale/zh_CN';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
dayjs.locale('zh-cn');
import DevicePixelRatio from '@/utils/devicePixelRatio.js';
const route = useRoute()
const detectZoom = () => {
  var ratio = 0,
    screen = window.screen,
    ua = navigator.userAgent.toLowerCase();
  if (window.devicePixelRatio !== undefined) {
    ratio = window.devicePixelRatio;
  } else if (~ua.indexOf('msie')) {
    if (screen.deviceXDPI && screen.logicalXDPI) {
      ratio = screen.deviceXDPI / screen.logicalXDPI;
    }
  } else if (
    window.outerWidth !== undefined &&
    window.innerWidth !== undefined
  ) {
    ratio = window.outerWidth / window.innerWidth;
  }

  if (ratio) {
    ratio = Math.round(ratio * 100);
  }
  //ratio就是获取到的百分比
  console.log(ratio);
  bodyScale()
  // this.onresize_height = ratio;
  // return ratio;
};
const bodyScale = () => {
      var devicewidth = document.documentElement.clientWidth;//获取当前分辨率下的可是区域宽度
      var scale = devicewidth / 1920; // 分母——设计稿的尺寸
      document.body.style.zoom = scale;//放大缩小相应倍数
      sessionStorage.setItem("scale", scale);
}
watch(
  () => route.path,
  (newPath, oldPath) => {
    console.log('路由从', oldPath, '变化到', newPath)
    // 这里可以添加路由变化后的处理逻辑
    if( newPath == '/projectStatisticaData/projectPress' || newPath == '/smart_regulation'){
      document.body.style.zoom = 1;//放大缩小相应倍数  
    } else {
      document.body.style.zoom =   sessionStorage.getItem("scale");//放大缩小相应倍数 
    }
  }
)
onMounted(() => {
  new DevicePixelRatio().init();
  	bodyScale();
  window.onresize = () => {
    return (() => {
      detectZoom();
    })();
  };
});
</script>
<style lang="less">
@import './styles/title.less';
@import './styles/index.less';
/* 设置滚动条的样式 */
::-webkit-scrollbar {
  width: 6px;
  box-sizing: border-box;
}

/* 滚动条滑块 */
::-webkit-scrollbar-thumb {
  height: 5px;
  border-radius: 5px;
  background-color: #c2cede;
}

/*滚动槽*/
::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0);
  border-radius: 10px;
  background: #ffffff;
}

/* 滚动条滑块 鼠标悬浮 */
::-webkit-scrollbar-thumb:hover {
  background-color: #c2cede;
  // border: 1px solid rgba(0, 0, 0, 0.21);
  cursor: pointer;
}
</style>
