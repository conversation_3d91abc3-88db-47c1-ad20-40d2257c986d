<script lang="ts" setup>
import "./index.less";
import { PropType } from "vue";
defineProps({
  menus: Array as PropType<any>,
  activeIndex: String,
});
const emit =defineEmits(['onMenuClick'])
const select = (key: string) => {
  emit("onMenuClick",key)
};

</script>

<template>
  <el-menu
    class="el-menu-vertical-demo"
    @select="select"
  >
    <el-menu-item
      v-for="item in menus"
      :index="item.key"
      :key="item.key"
      :title="item.title"
    >
      <component :is="item.label" :class="item.key===activeIndex?'active':''"> </component>
    </el-menu-item>
  </el-menu>
</template>
