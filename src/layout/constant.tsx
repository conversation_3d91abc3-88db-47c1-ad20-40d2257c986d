import homeIcon from '@/assets/menus/menuSvg/home_nor.svg' // '/menus/home.png'
import projectIcon from '@/assets/menus/menuSvg/项目管理_nor.svg' //  '/menus/project.png'
import subjectIcon from '@/assets/menus/menuSvg/主体信息_nor.svg' // '/menus/subject.png'
import subjectEvaluationIcon from '@/assets/menus/menuSvg/主体评价_nor.svg' // '/menus/subjectEvaluation.png'
import proxyEvaluationIcon from '@/assets/menus/menuSvg/代理评价_nor.svg' // '/menus/subjectEvaluation.png'
import trainingExaminationIcon from '@/assets/menus/menuSvg/home_nor.svg' // '/menus/trainingExamination.png'
import tenderIcon from '@/assets/menus/menuSvg/投标集成_nor.svg' // '/menus/tender.png'
import informationDisclosureIcon from '@/assets/menus/menuSvg/信息公开_nor.svg' // '/menus/informationDisclosure.png'
import lawCheckIcon from '@/assets/menus/menuSvg/执法检查_nor.svg' // '/menus/lawCheck.png'
import contractPerformanceIcon from '@/assets/menus/menuSvg/合同履约_nor.svg' // '/menus/contractPerformance.png'
import objectionComplaintIcon from '@/assets/menus/menuSvg/异议投诉_nor.svg' // '/menus/objectionComplaint.png'
import archiveInformationIcon from '@/assets/menus/menuSvg/home_nor.svg' // '/menus/archiveInformation.png'
import smartRegulationIcon from '@/assets/menus/menuSvg/智慧监管_nor.svg' // '/menus/smartRegulation.png'
import bidIcon from '@/assets/menus/menuSvg/远程监管_nor .svg' // '/menus/bid.png'
import platForm from '@/assets/menus/menuSvg/平台管理_nor.svg' // '/menus/bid.png'
import decision from '@/assets/menus/menuSvg/自主决策_nor.svg' // '/menus/bid.png'
import bidOPIcon from '@/assets/menus/menuSvg/一览表.png'
import constructionManageIcon from '@/assets/menus/menuSvg/参建管理_nor.svg'
import './index.less'
import {    MODULE_TYPE_ARCHIVE_INFORMATION,
    MODULE_TYPE_BID,
    MODULE_TYPE_CONTRACT_PERFORMANCE,
    MODULE_TYPE_HOME,
    MODULE_TYPE_INFORMATION_DISCLOSURE,
    MODULE_TYPE_LAW_CHECK,
    MODULE_TYPE_OBJECTION_COMPLAINT,
    MODULE_TYPE_PROJECT,
    MODULE_TYPE_SMART_REGULATION,
    MODULE_TYPE_SUBJECT,
    MODULE_TYPE_SUBJECT_EVALUATION,
	MODULE_TYPE_AGENT_EVALUATION,
    MODULE_TYPE_TENDER,
    MODULE_TYPE_TRAINING_EXAMINATION,
    MODULE_TYPE_MAIN_STORE,
    MODULE_TYPE_PLATFORM_MANAGE,
    MODULE_TYPE_DECISION_MAKING,
    MODULE_TYPES_OBJ,
    MODULE_TYPE_BID_OPENING_PROJECTS,
    MODULE_TYPE_CONSTRUCTION_MANAGE
} from '@/constant/module';


export type MenuItem = {
    position: MenuPositionEnum,
    title: string,
    key: string,
    label: any,
    [key: string]: any

}
export enum MenuPositionEnum {
    top,
    middle,
    bottom
}

export const iconMaps: { [key: string]: any } = {
    [MODULE_TYPE_HOME]: homeIcon,
    [MODULE_TYPE_PROJECT]: projectIcon,
    [MODULE_TYPE_SUBJECT]: subjectIcon,
    [MODULE_TYPE_TENDER]: tenderIcon,
    [MODULE_TYPE_OBJECTION_COMPLAINT]: objectionComplaintIcon,
    [MODULE_TYPE_CONTRACT_PERFORMANCE]: contractPerformanceIcon,
    [MODULE_TYPE_LAW_CHECK]: lawCheckIcon,
    [MODULE_TYPE_INFORMATION_DISCLOSURE]: informationDisclosureIcon,
    [MODULE_TYPE_SUBJECT_EVALUATION]: subjectEvaluationIcon,
	[MODULE_TYPE_AGENT_EVALUATION]: proxyEvaluationIcon,	
    [MODULE_TYPE_TRAINING_EXAMINATION]: trainingExaminationIcon,
    [MODULE_TYPE_ARCHIVE_INFORMATION]: archiveInformationIcon,
    [MODULE_TYPE_SMART_REGULATION]: smartRegulationIcon,
    [MODULE_TYPE_BID]: bidIcon,
    [MODULE_TYPE_MAIN_STORE]: subjectIcon,
    [MODULE_TYPE_PLATFORM_MANAGE]: platForm,
    [MODULE_TYPE_DECISION_MAKING]: decision,
    [MODULE_TYPE_BID_OPENING_PROJECTS]: bidOPIcon,
    [MODULE_TYPE_CONSTRUCTION_MANAGE]:constructionManageIcon
}
export const getMenus = (sysUserModules) => {
    let sysUserModulesObj =  JSON.parse(sessionStorage.getItem('userMenu'))
    sysUserModulesObj.forEach(item=>{
        item.key = MODULE_TYPES_OBJ[item.key]
    })
    return sysUserModulesObj.map(item=>{
        return {
            ...item,
            imgSrc:iconMaps[item.key],
            label:h('div',{
            
                style:{
                    backgroundImage:`url(${iconMaps[item.key]})`
                }
            })
        }
    })
    // const moduleMap = getModuleMap()
    // if(user.roles[0].roleId === 99){
    //     return [
    //         {
    //             ...moduleMap[MODULE_TYPE_SMART_REGULATION],
    //             // position: MenuPositionEnum.bottom
    //         },
    //         {
    //             ...moduleMap[MODULE_TYPE_MAIN_STORE],
    //         }
    //     ].map(item=>{
    //         return {
    //             ...item,
    //             imgSrc:iconMaps[item.key],
    //             label:h('div',{
    //                 class:`icon ${activeKey === item.key ? "active" : ''}`,
    //                 style:{
    //                     backgroundImage:`url(${iconMaps[item.key]})`
    //                 }
    //             })
    //         }
    //     })
    // }else if( user.roles[0].roleId === 88){
    //     return [
    //         {
    //             ...moduleMap[MODULE_TYPE_PROJECT],
    //         },
    //         {
    //             ...moduleMap[MODULE_TYPE_OBJECTION_COMPLAINT],
    //         }
    //     ].map(item=>{
    //         return {
    //             ...item,
    //             imgSrc:iconMaps[item.key],
    //             label:h('div',{
    //                 class:`icon ${activeKey === item.key ? "active" : ''}`,
    //                 style:{
    //                     backgroundImage:`url(${iconMaps[item.key]})`
    //                 }
    //             })
    //         }
    //     })
    // }else if(user.roles[0].roleId === 6){
    //     return [
    //         {
    //             ...moduleMap[MODULE_TYPE_HOME],
    //             // position: MenuPositionEnum.top
    //         },
    //         {
    //             ...moduleMap[MODULE_TYPE_DECISION_MAKING],
    //         },
    //         {
    //             ...moduleMap[MODULE_TYPE_PROJECT],
    //         },
    //         {
    //             ...moduleMap[MODULE_TYPE_SUBJECT],
    //         },
    //         {
    //             ...moduleMap[MODULE_TYPE_TENDER],
    //         },
    //         {
    //             ...moduleMap[MODULE_TYPE_BID],
    //         },
    //         {
    //             ...moduleMap[MODULE_TYPE_OBJECTION_COMPLAINT],
    //         },
    //         {
    //             ...moduleMap[MODULE_TYPE_CONTRACT_PERFORMANCE],
    //         },
    //         {
    //             ...moduleMap[MODULE_TYPE_LAW_CHECK],
    //         },
    //         {
    //             ...moduleMap[MODULE_TYPE_INFORMATION_DISCLOSURE],
    //         },
    //         {
    //             ...moduleMap[MODULE_TYPE_SUBJECT_EVALUATION],
    //         },
    //         {
    //             ...moduleMap[MODULE_TYPE_AGENT_EVALUATION],
    //         },
    //         //
    //         // {
    //         //     ...moduleMap[MODULE_TYPE_TRAINING_EXAMINATION],
    //         // },
    //         // {
    //         //     ...moduleMap[MODULE_TYPE_ARCHIVE_INFORMATION],
    //         // },
    //         {
    //             ...moduleMap[MODULE_TYPE_SMART_REGULATION],
    //             // position: MenuPositionEnum.bottom
    //         },
    //         // {
    //         //     ...moduleMap[MODULE_TYPE_MAIN_STORE],
    //         // },
    //         {
    //             ...moduleMap[MODULE_TYPE_PLATFORM_MANAGE],
    //         }
    //     ].map(item=>{
    //         return {
    //             ...item,
    //             imgSrc:iconMaps[item.key],
    //             label:h('div',{
    //                 class:`icon ${activeKey === item.key ? "active" : ''}`,
    //                 style:{
    //                     backgroundImage:`url(${iconMaps[item.key]})`
    //                 }
    //             })
    //         }
    //     })
    // }else{
    //     return [
    //         {
    //             ...moduleMap[MODULE_TYPE_HOME],
    //             // position: MenuPositionEnum.top
    //         },
    //         {
    //             ...moduleMap[MODULE_TYPE_DECISION_MAKING],
    //         },
    //         {
    //             ...moduleMap[MODULE_TYPE_PROJECT],
    //         },
    //         {
    //             ...moduleMap[MODULE_TYPE_SUBJECT],
    //         },
    //         {
    //             ...moduleMap[MODULE_TYPE_TENDER],
    //         },
    //         {
    //             ...moduleMap[MODULE_TYPE_BID],
    //         },
    //         {
    //             ...moduleMap[MODULE_TYPE_OBJECTION_COMPLAINT],
    //         },
    //         {
    //             ...moduleMap[MODULE_TYPE_CONTRACT_PERFORMANCE],
    //         },
    //         {
    //             ...moduleMap[MODULE_TYPE_LAW_CHECK],
    //         },
    //         {
    //             ...moduleMap[MODULE_TYPE_INFORMATION_DISCLOSURE],
    //         },
    //         {
    //             ...moduleMap[MODULE_TYPE_SUBJECT_EVALUATION],
    //         },
    //         // {
    //         //     ...moduleMap[MODULE_TYPE_AGENT_EVALUATION],
    //         // },
    //         //
    //         // {
    //         //     ...moduleMap[MODULE_TYPE_TRAINING_EXAMINATION],
    //         // },
    //         // {
    //         //     ...moduleMap[MODULE_TYPE_ARCHIVE_INFORMATION],
    //         // },
    //         {
    //             ...moduleMap[MODULE_TYPE_SMART_REGULATION],
    //             // position: MenuPositionEnum.bottom
    //         },
    //         // {
    //         //     ...moduleMap[MODULE_TYPE_MAIN_STORE],
    //         // },
    //     ].map(item=>{
    //         return {
    //             ...item,
    //             imgSrc:iconMaps[item.key],
    //             label:h('div',{
    //                 class:`icon ${activeKey === item.key ? "active" : ''}`,
    //                 style:{
    //                     backgroundImage:`url(${iconMaps[item.key]})`
    //                 }
    //             })
    //         }
    //     })
    // }
}





export const getMenusClassify = (menus: MenuItem[]) => {
    const topMenus: MenuItem[] = []
    const middleMenus: MenuItem[] = []
    const bottomMenus: MenuItem[] = []
    menus.forEach(menuItem => {
        if (menuItem.position === MenuPositionEnum.top) {
            topMenus.push(menuItem)
        } else if (menuItem.position === MenuPositionEnum.bottom) {
            bottomMenus.push(menuItem)
        } else {
            middleMenus.push(menuItem)
        }
    });

    return [topMenus, middleMenus, bottomMenus]
}