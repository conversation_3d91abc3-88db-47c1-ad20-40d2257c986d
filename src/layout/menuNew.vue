<script lang="ts" setup>
import "./index.less";
import { PropType } from "vue";
defineProps({
  menus: Array as PropType<any>,
  activeIndex: String,
});
const emit = defineEmits(["onMenuClick"]);
const select = (key: string) => {
  emit("onMenuClick", key);
};
</script>

<template>
  <el-menu class="el-menu-vertical-demo" @select="select"  :default-active="activeIndex">
    <el-menu-item v-for="item in menus" :index="item.key" :key="item.key">
      <img :src="item.imgSrc" class="menu-icon" alt="" />
      <span :class="item.key === activeIndex ? 'active' : ''">{{
        item.title
      }}</span>
    </el-menu-item>
  </el-menu>
</template>

<style lang="less" scoped>
.el-menu-vertical-demo {
  .menu-icon {
    width: 35px;
  }
  :deep(li.el-menu-item){
    border-radius: 50px;
    height: 40px;
    font-size: 16px;
    margin: 10px 10px;
  }
  :deep(li.is-active){
    background: #3366ff;
    color: #fff;
  }
  .el-menu-item.is-active {
    .menu-icon {
      width: 40px;
      left: -80px;
      filter: drop-shadow(#fff 80px 0);
    }
  }
  .menu-icon {
    position: relative;
    margin-right: 5px;
    padding: 0 3px;
    left: 0;
  }
}
</style>