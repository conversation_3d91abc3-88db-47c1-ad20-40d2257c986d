<script setup>
import { useRouter } from "vue-router";
import { ElDropdown } from "element-plus";
import { getMenus, getMenusClassify } from "./constant";
import Menu from "./menu.vue";
import MenuNew from "./menuNew.vue";
import useGloBalStore from "@/store/useGlobalStore";
import { LOCAL_STORAGE_TOKEN_KEY } from "@/constant";
import { LOGIN_PATH } from "@/routes";
import { getUrlPath } from "@/utils/url";
import updateVersion from "@/pages/home/<USER>/updateVersion.vue";
import { fa } from "element-plus/es/locales.mjs";
const router = useRouter();
const { user,sysUserModules } = useGloBalStore();
const menusItems = getMenus(sysUserModules);
const [topMenus, middleMenus, bottomMenus] = getMenusClassify(menusItems);
const activeIndex = ref();
const isShow = ref(false);
const allDialogViewEl = ref();
onMounted(() => {
  const path = getUrlPath();
  if (path) {
    if (path.indexOf("/") != -1) {
      let pathArr = path.split("/");
      activeIndex.value = pathArr[0];
    } else {
      activeIndex.value = path;
    }
  }
});
const quitLogin = () => {
  ElMessageBox.alert('确认退出登陆？', '', {
    confirmButtonText: '确认',
    callback: (action) => {
      if(action == 'confirm'){
        localStorage.removeItem(LOCAL_STORAGE_TOKEN_KEY);
        useGloBalStore.getState().clear();
        router.push(`/login`);
      } 
    },
  })
};

const onMenuClick = (key) => {
  console.log('key',key);
  // if( key == 'constructionManage'){
  //   ElMessage({
  //     message: "升级中！",
  //     type: "error",
  //   });
  //   return
  // }    
  router.push(`/${key}`);
  activeIndex.value = key;
  // handleShowNabar()
};
const onMenuHrefClick = () => {
 window.open('http://218.60.147.115/')
};
const handleOpen = () => {
  // window.open('http://***************:8887/lnjsgc/监督管理部门--辽宁省房屋建筑和市政工程数字化全链条监管系统操作手册.pdf', '_blank')
  router.push(`/platformManage`);
}

const handleMore = () => {
   allDialogViewEl.value.showDialog = !allDialogViewEl.value.showDialog;
};

function handleShowNabar() {
  isShow.value = !isShow.value;
}
function handleMouseOver() {
  isShow.value = true;
}
function handleMouseLeave() {
  isShow.value = false;
}
</script>

<template>
  <div class="common-layout">
    <el-container>
      <el-header class="header">
        <div
          class="nav-box "
          @mouseover="handleMouseOver"
          @mouseleave="handleMouseLeave"
          @click="handleShowNabar"
        >
          <img :class="{heartbeat: !isShow}" src="../assets/layout/navbar1.png" alt="" />
        </div>
        <div class="con-box">
          <div className="left">
            <div
              @mouseover="handleMouseOver"
              @mouseleave="handleMouseLeave"
              class="navbar-box"
              :class="isShow ? 'slide-in-elliptic-left-fwd' : 'slide-out-elliptic-left-bck'"
            >
              <MenuNew
                :menus="middleMenus"
                :activeIndex="activeIndex"
                @onMenuClick="onMenuClick"
              ></MenuNew>
            </div>
<!--            <div className="demo-logo">-->
<!--              <img src="/layout/logo.png" alt="" />-->
<!--            </div>-->
            <img style="width:680px;" src="/layout/title.png" className="title" />
          </div>
          <div className="right">
            <span class="user-img" @click="onMenuHrefClick()"> 
              <div style="display: flex; align-items: center;justify-content: center;"> 
                <el-icon style="margin-right:10px;"><Monitor /></el-icon> 建网门户
              </div>
            </span>
            <span class="user"> 您好， {{ user.nickName }} </span>
            <span class="sc-img" @click="handleOpen()"></span>
               <el-tooltip effect="light" placement="bottom">
                <template #content>
                    <div class="font16 mb8">更新记录</div>
                </template>
                <span class="version-img"  @click="handleMore()"></span>
            </el-tooltip>
            <el-tooltip effect="light" placement="bottom">
                <template #content>
                    <div class="font16 mb8">技术服务热线：13644972236</div>
                    <div class="font16">办公时间：工作日周一至周五 08:30 - 11:30，13:00 - 17:00</div>
                </template>
                <span class="dh-img"></span>
            </el-tooltip>


            <!-- <el-popover
              placement="top-start"
              title="客服电话"
              :width="200"
              trigger="click"
            >
              <template #reference>
                <span class="dh-img"></span>
              </template>
              <span style="display: inline-block;font-weight: bold; font-size: 16px;">13644972236</span>
            </el-popover> -->
            <el-tooltip
              class="box-item"
              effect="light"
              content="退出"
              placement="bottom"
            >
              <span class="user-out" @click="quitLogin"></span>
            </el-tooltip>
          </div>
        </div>
      </el-header>
      <el-container class="main">
        <el-aside class="left">
          <Menu
            :menus="topMenus"
            :activeIndex="activeIndex"
            @onMenuClick="onMenuClick"
          />
          <Menu
            :menus="middleMenus"
            :activeIndex="activeIndex"
            @onMenuClick="onMenuClick"
          />
          <Menu
            :menus="bottomMenus"
            :activeIndex="activeIndex"
            @onMenuClick="onMenuClick"
          />
        </el-aside>
        <el-container class="right">
          
          <router-view v-slot="{ Component ,route}">
            <keep-alive include="home">
              <component :is="Component" />
            </keep-alive>
            <!-- <component :is="Component" v-if="!route.meta.keepAlive" /> -->
          </router-view>
        </el-container>
      </el-container>
    </el-container>
    <allDialogView
        ref="allDialogViewEl"
        :dialogTitle="'更新记录'"
        :dialogWidth="'60%'"
      >
        <template #content>
          <updateVersion
            v-if="allDialogViewEl.showDialog"
            @handleCloseDialog="handleMore"
          ></updateVersion>
        </template>
      </allDialogView>
  </div>
</template>

<style lang="less" scoped>
.navbar-box {
  width: 240px;
  height: calc(100% - 64px);
  background-color: #fff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  position: fixed;
  top: 64px;
  left: 0px;
  z-index: 9999999;
  border-radius: 0px 8px 8px 0px;
  opacity: 0.8;
  overflow: auto;
}
.common-layout {
  display: flex;
  height: 100%;
  :deep(.el-header) {
    padding: 0px;
  }
  .header {
    // background-color: #3366ff;
    display: flex;
    align-items: center;
    height: 64px;
    width: 100%;
    justify-content: space-between;
    .nav-box {
      width: 3%;
      height: 100%;
      cursor: pointer;
      background: #fff;
      // background-color: #3366ff;
      padding: 13px 8px;
      img {
        width: 100%;
        height: 100%;
      }
    }
    .con-box {
      background-color: #3366ff;
      display: flex;
      width: 97%;
      height: 100%;
      align-items: center;
      justify-content: space-between;
    }
    .left {
      display: flex;
      align-items: center;
      padding-left: 12px;
      .title {
        font-size: 24px;
        color: #fff;
      }

      .demo-logo {
        display: flex;
        align-items: center;
        border-radius: 50%;
        margin-right: 8px;

        > img {
          width: 100%;
          height: 100%;
        }
      }
      > span {
        color: #fff;
        font-size: 32px;
      }
    }
    .right {
      // padding-right: 20px;
      .user-img{
        display: inline-block;
        width:120px;
        line-height: 30px;
        text-align: center;
        background: #4ea3f9;
        margin-right: 12px;
        color: #fff;
        border-radius: 5px;
        cursor: pointer;
        font-size: 16px;
      }
      .user {
        cursor: pointer;
        color: #fff;
        font-size: 20px;
        border: none !important;
      }
      .user-out {
        display: inline-block;
        width: 24px;
        height: 24px;
        background: url(/layout/out.png) no-repeat;
        background-size: 100% 100%;
        margin: 0 12px;
        vertical-align: top;
        position: relative;
        margin-left: 22px;
        cursor: pointer;
        &::before {
          content: "";
          width: 2px;
          height: 25px;
          background: #fff;
          position: absolute;
          top: 0;
          left: -17px;
        }
      }
      .sc-img {
        display: inline-block;
        width: 24px;
        height: 24px;
        background: url("../assets/layout/sc.png") no-repeat;
        background-size: 100% 100%;
        margin: 0 12px;
        vertical-align: top;
        position: relative;
        margin-left: 35px;
        cursor: pointer;
        &::before {
          content: "";
          width: 2px;
          height: 25px;
          background: #fff;
          position: absolute;
          top: 0;
          left: -17px;
        }
      }
      .dh-img {
        display: inline-block;
        width: 24px;
        height: 24px;
        background: url("../assets/layout/dh.png") no-repeat;
        background-size: 100% 100%;
        margin: 0 12px;
        vertical-align: top;
        position: relative;
        cursor: pointer;
      }

       .version-img {
        display: inline-block;
        width: 24px;
        height: 24px;
        background: url("../assets/layout/vu.png") no-repeat;
        background-size: 100% 100%;
        margin: 0 12px;
        vertical-align: top;
        position: relative;
        cursor: pointer;
      }

      .el-dropdown {
        border: none !important;
        .user {
          cursor: pointer;
          color: #fff;
          font-size: 20px;
          border: none !important;
        }
      }
      .line {
        height: 25px;
        width: 1px;
        background: #cccccc;
        display: inline-block;
      }
    }
  }
  .main {
    flex: 1;
    margin: 16px 0px;
    .left {
      height: 100%;
      width: auto;
      margin: 0px 16px;
      padding: 8px;
      margin-bottom: 0px;
      border-radius: 8px;
      background-color: #fff;
      display: flex;
      flex-direction: column;
      align-items: center;
      overflow-x: hidden;
      overflow-y: auto;
    }
    .right {
    }
  }
}

.scale-in-tl {
  animation: scale-in-tl 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
}
.scale-out-tl {
  animation: scale-out-tl 0.2s cubic-bezier(0.55, 0.085, 0.68, 0.53) both;
}
.slide-out-elliptic-left-bck {
  animation: slide-out-elliptic-left-bck 0.1s ease-in both;
}
.slide-in-elliptic-left-fwd {
  animation: slide-in-elliptic-left-fwd 0.1s
    cubic-bezier(0.25, 0.3, 0.45, 0.94) both;
}
@keyframes slide-out-elliptic-left-bck {
  0% {
    transform: translateX(0) rotateY(0) scale(1);
    transform-origin: 1000px 50%;
    opacity: 1;
  }
  100% {
    transform: translateX(-1000px) rotateY(30deg) scale(0);
    transform-origin: -100% 50%;
    opacity: 1;
  }
}
@keyframes slide-in-elliptic-left-fwd {
  0% {
    transform: translateX(-600px) rotateY(30deg) scale(0);
    transform-origin: -100% 50%;
    opacity: 0;
  }
  100% {
    transform: translateX(0) rotateY(0) scale(1);
    transform-origin: 1000px 50%;
    opacity: 1;
  }
}
@keyframes scale-out-tl {
  0% {
    transform: scale(1);
    transform-origin: 0 0;
    opacity: 1;
  }
  100% {
    transform: scale(0);
    transform-origin: 0 0;
    opacity: 1;
  }
}
@keyframes scale-in-tl {
  0% {
    transform: scale(0);
    transform-origin: 0 0;
    opacity: 1;
  }
  100% {
    transform: scale(1);
    transform-origin: 0 0;
    opacity: 1;
  }
}
@keyframes heartbeat {
  from {
    transform: scale(1);
    transform-origin: center center;
    animation-timing-function: ease-out;
  }
  10% {
    transform: scale(0.91);
    animation-timing-function: ease-in;
  }
  17% {
    transform: scale(0.98);
    animation-timing-function: ease-out;
  }
  33% {
    transform: scale(0.77);
    animation-timing-function: ease-in;
  }
  45% {
    transform: scale(1);
    animation-timing-function: ease-out;
  }
}
.heartbeat {
  animation: heartbeat 1.5s ease-in-out infinite both;
}
</style>
<style>
.font16 {
    font-size: 16px;
}
.mb8 {
    margin-bottom: 8px;
}
</style>
