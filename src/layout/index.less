@header-height: 64px;
@menu-item-height: 68px;
.el-container .main {
  flex: 1;
  height: 0px;
  .el-aside {
    display: none !important;
    padding: 16px 8px !important;
    display: flex;
    flex-direction: column;
    .el-menu {
      border: none;
      &:nth-of-type(2) {
        flex: 1;
        overflow: auto;
        padding: 4px 0px;
        margin: 16px 0px;
      }

      &::-webkit-scrollbar {
        width: 0;
      }

      .el-menu-item {
        width: auto;
        margin: 0px;
        padding: 0px;
        margin-bottom: 20px;
        &:last-child{
          margin-bottom: 0px;
        }
        .icon {
          width: 68px;
          height: 68px;
          background-position: 0px 0px;
          background-size: 100%;
          background-repeat: no-repeat;

          &:hover {
            background-position: 0px -@menu-item-height !important;
          }

          &.active {
            background-position: 0px calc(@menu-item-height * -2);
          }
        }
      }
    }
  }
  .right{
    margin:0px 16px;
  }
}
