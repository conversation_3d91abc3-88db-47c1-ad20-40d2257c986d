import useGloBalStore from "@/store/useGlobalStore"
import { httpConfigSet, httpGet } from "@wl-fe/http"
export const LOCAL_STORAGE_TOKEN_KEY = 'jg-token'
import router from "@/routes"
export const VALUE_UNKNOWN = "--"


export const checkUserInfo = async () => {
    try {
        const token = localStorage.getItem(LOCAL_STORAGE_TOKEN_KEY)
        httpConfigSet('headers', { 'Authorization': token })
        const { user : globalUser, token: globalToken } = useGloBalStore.getState()
        if (globalToken && globalUser.nickName) {
            return true
        }
        const { setToken, setPermissions, setRoles, setUser,setSysUserModules } = useGloBalStore.getState()

        if (!token) {
            return false
        }

        const { permissions, roles, user ,sysUserModules} = await httpGet("/getInfo", null, {
            errorHandler(err) {
                console.log(err)
                localStorage.removeItem(LOCAL_STORAGE_TOKEN_KEY)
                router.push('/login')
                return false
            },
        })
        if (user) {
            setToken(token)
            setPermissions(permissions)
            setRoles(roles)
            setSysUserModules(sysUserModules)
            setUser(user)
            sessionStorage.setItem('userMenu', JSON.stringify(sysUserModules)) // 存储用户信息到sessionStorage中
            return true
        } else {
            localStorage.removeItem(LOCAL_STORAGE_TOKEN_KEY)
            return false
        }
    } catch (error) {
        return false
    }
}


export const DEFAULT_PAGE_NUM = 1
export const DEFAULT_PAGE_SIZE = 10



