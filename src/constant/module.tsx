import { MODULE_ITEM_TYPE } from "@/type"
export const MODULE_TYPES_OBJ = {
    MODULE_TYPE_HOME: 'home', // 首页
    MODULE_TYPE_PROJECT: 'projectStatisticaData', // 项目
    MODULE_TYPE_PROJECT_LIST: 'projectStatisticaData/projectList', // 项目管理列表
    MODULE_TYPE_PROJECT_DETAIL: 'projectStatisticaData/projectDetail', // 项目管理详情
    MODULE_TYPE_PROJECT_PRESS: 'projectStatisticaData/projectPress', // 项目管理流程详情
    MODULE_TYPE_PROJECT_REALNAME: 'projectStatisticaData/projectRealName', // 项目管理实名认证详情
    MODULE_TYPE_SUBJECT: 'companyBasicData', // 主体
    MODULE_TYPE_TENDER: 'blindboxStatisticalData', // 投标集成
    MODULE_TYPE_BID: 'bid_opening_evaluation',
    MODULE_TYPE_OBJECTION_COMPLAINT: 'tousuYIyiStatisticalData', // 异议投诉
    MODULE_TYPE_CONTRACT_PERFORMANCE: 'contractPerformanceStatisticalData', // 合同履约
    MODULE_TYPE_LAW_CHECK: 'directReportStatisticalData', // 执法检查
    MODULE_TYPE_INFORMATION_DISCLOSURE: 'information_disclosure', // 信息公开
    MODULE_TYPE_SUBJECT_EVALUATION: 'expertEvaluateStatisticalData', // 主体评价 subject_evaluation
    MODULE_TYPE_EXPORT_MAINSTORE: 'expertEvaluateStatisticalData/expertMainStore', // 主体评价内联微服务页面 subject_evaluation
    MODULE_TYPE_AGENT_EVALUATION: 'agentEvaluation', // 代理评价 agentEvaluation
    MODULE_TYPE_TRAINING_EXAMINATION: 'training_examination', // 培训考试
    MODULE_TYPE_ARCHIVE_INFORMATION: 'archive_information', // 档案信息
    MODULE_TYPE_SMART_REGULATION: 'smart_regulation', // 智能监管
    MODULE_TYPE_PLATFORM_MANAGE: 'platformManage', // 平台管理
    MODULE_TYPE_DECISION_MAKING: 'decisionMaking', // 自主决策
    MODULE_TYPE_BID_OPENING_PROJECTS: 'bidOpeningProjects', // 开标一览表
    MODULE_TYPE_CONSTRUCTION_MANAGE: 'constructionManage', // 参建管理
    MODULE_TYPE_CONSTRUCTION_DETAIL: 'constructionManage/constructionDetail', // 参建管理详情
 };
export const MODULE_TYPE_HOME = 'home' // 首页
export const MODULE_TYPE_PROJECT = 'projectStatisticaData'  // 项目
export const MODULE_TYPE_PROJECT_LIST = 'projectStatisticaData/projectList'  // 项目管理列表
export const MODULE_TYPE_PROJECT_DETAIL = 'projectStatisticaData/projectDetail'  // 项目管理详情
export const MODULE_TYPE_PROJECT_PRESS = 'projectStatisticaData/projectPress'  // 项目管理流程详情
export const MODULE_TYPE_PROJECT_REALNAME = 'projectStatisticaData/projectRealName'  // 项目管理实名认证详情
export const MODULE_TYPE_SUBJECT = 'companyBasicData'  // 主体
export const MODULE_TYPE_TENDER = 'blindboxStatisticalData'  // 投标集成
export const MODULE_TYPE_BID="bid_opening_evaluation"
export const MODULE_TYPE_OBJECTION_COMPLAINT = 'tousuYIyiStatisticalData'  // 异议投诉
export const MODULE_TYPE_CONTRACT_PERFORMANCE = 'contractPerformanceStatisticalData'  // 合同履约
export const MODULE_TYPE_LAW_CHECK = 'directReportStatisticalData'  // 执法检查
export const MODULE_TYPE_INFORMATION_DISCLOSURE = 'information_disclosure'  // 信息公开
export const MODULE_TYPE_SUBJECT_EVALUATION = 'expertEvaluateStatisticalData'  // 主体评价 subject_evaluation
export const MODULE_TYPE_EXPORT_MAINSTORE = 'expertEvaluateStatisticalData/expertMainStore'  // 主体评价内联微服务页面 subject_evaluation
export const MODULE_TYPE_AGENT_EVALUATION = 'agentEvaluation'  // 代理评价 agentEvaluation
export const MODULE_TYPE_TRAINING_EXAMINATION = 'training_examination'  // 培训考试
export const MODULE_TYPE_ARCHIVE_INFORMATION = 'archive_information'  // 档案信息
export const MODULE_TYPE_SMART_REGULATION = 'smart_regulation'  // 智能监管
export const MODULE_TYPE_PLATFORM_MANAGE = 'platformManage'  // 平台管理
export const MODULE_TYPE_DECISION_MAKING = 'decisionMaking'  // 自主决策

// 微应用
export const MODULE_TYPE_MAIN_STORE = 'main_store' // 主体诚信库
export const MODULE_TYPE_BID_OPENING_PROJECTS = 'bidOpeningProjects' // 开标一览表
export const MODULE_TYPE_CONSTRUCTION_MANAGE = 'constructionManage' // 参建管理
export const MODULE_TYPE_CONSTRUCTION_DETAIL =  'constructionManage/constructionDetail' // 参建管理详情

export const moduleList: MODULE_ITEM_TYPE[] = [
    {
        title: "首页",
        key: MODULE_TYPE_HOME,
    },
    {
        title: "项目管理",
        key: MODULE_TYPE_PROJECT,
        singleUrl:"/singleLoginController/getProjectExLoginUrl"
    },
    {
        title: "主体信息",
        key: MODULE_TYPE_SUBJECT,
        singleUrl:"/singleLoginController/getCompanybasicUrl",
        localUrl:"https://ztkjg.lnzb.cn", // http://localhost:8083
        zsUrl:"https://ztkjg.lnzb.cn"
    },
    {
        title: "远程监管",
        key: MODULE_TYPE_BID
    },
    {
        title: "投标集成",
        key: MODULE_TYPE_TENDER,
        singleUrl:"/singleLoginController/getblindboxCasLoginUrl",
        localUrl:"https://xbox.lnzb.com", // http://localhost:80
        zsUrl:"https://xbox.lnzb.com"
    },
    {
        title: "异议投诉",
        key: MODULE_TYPE_OBJECTION_COMPLAINT,
        singleUrl:"/subSysComplaint/skipUrl",
    },
    {
        title: "合同履约",
        key: MODULE_TYPE_CONTRACT_PERFORMANCE,
        singleUrl:"/singleLoginController/getContractPerformanceUrl",
        localUrl: "https://zj-lyht.lnzb.com:8888", // " http://localhost:8082", // http://localhost:8083
        zsUrl:"https://zj-lyht.lnzb.com:8888"
    },
    {
        title: "执法检查",
        key: MODULE_TYPE_LAW_CHECK,
        singleUrl:"/singleLoginController/getDirectReportLoginUrl",
        localUrl: "https://zbxt.lnzb.com", // " http://localhost:8082", // http://localhost:8083
        zsUrl:"https://zbxt.lnzb.com"
    },
    {
        title: "信息公开",
        key: MODULE_TYPE_INFORMATION_DISCLOSURE
    },
    {
        title: "专家评价",
        key: MODULE_TYPE_SUBJECT_EVALUATION,
        singleUrl:"/singleLoginController/getEvaluationUrl",
        localUrl: "https://www.lntb.gov.cn/zjkp/", // " http://localhost:8082", // http://localhost:8083
        zsUrl:"https://www.lntb.gov.cn/zjkp/"
    },
	{
        title: "代理评价",
        key: MODULE_TYPE_AGENT_EVALUATION,
        singleUrl:"/singleLoginController/getAgentEvaluationUrl",
        localUrl: "https://dlpj.lnzb.com:8888/", // " http://localhost:8082", // http://localhost:8083
        zsUrl:"https://dlpj.lnzb.com:8888/"
    },
    {
        title: "培训考试",
        key: MODULE_TYPE_TRAINING_EXAMINATION
    },
    {
        title: "档案信息",
        key: MODULE_TYPE_ARCHIVE_INFORMATION
    }
    ,
    {
        title: "智能监管",
        key: MODULE_TYPE_SMART_REGULATION
    },
    {
        title: "系统监督",
        key: MODULE_TYPE_PLATFORM_MANAGE
    },
    {
        title: "自主决策",
        key: MODULE_TYPE_DECISION_MAKING
    },
    // {
    //     title: "主体诚信",
    //     key: MODULE_TYPE_MAIN_STORE
    // }

]


export const getModuleMap = function () {
    const moduleMap: { [key: string]: any } = {}
    moduleList.forEach(item => {
        moduleMap[item.key] = item
    })
    return () => moduleMap
}()


