import { createApp } from 'vue'
import { createPinia } from 'pinia'
import router, { LOGIN_PATH } from '@/routes'
import 'element-plus/dist/index.css'
import ElementPlus from 'element-plus'
import zhCn from 'element-plus/es/locale/lang/zh-cn'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import './style.css'

import Antd from 'ant-design-vue';
import 'ant-design-vue/dist/reset.css';

// 初始化微应用
import microApp from '@micro-zoe/micro-app'
microApp.start()

import App from './App.vue'
import { httpInit } from '@wl-fe/http/dist'
import { ElMessage } from 'element-plus'
import { getAxiosBaseUrl } from './utils/env'
import caReader from '@jy/ca-reader';
import pdfViewer from '@jy/pdf-viewer';
import '@jy/ca-reader/dist/style.css';
import '@jy/pdf-viewer/dist/style.css';
console.log('pdfViewer',pdfViewer,caReader);
console.log('getAxiosBaseUrl(),', getAxiosBaseUrl());
console.log('httpInithttpInit66666',httpInit);

httpInit({
  baseURL: getAxiosBaseUrl(),
  // message: ElMessage,
  message: {
    error: (text) => {
      if( text == '获取用户信息异常' || text.includes('无法访问系统资源') ){
        router.push({ path: '/login' });
        ElMessage({
          message: '登录已过期，请重新登录',
          type: "error",
        });
        return
      }
      ElMessage({
        message: text,
        type: "error",
      });
    },
  },
  // redirect: {
  //   code: 401,
  //   path: LOGIN_PATH
  // }
})
const bootstrap = () => {
  const pinia = createPinia()
  const app = createApp(App)
  app.use(caReader, {baseApi: import.meta.env.VITE_APP_BASE_API});; // 自动引入pdf-viewer-ulti等一系列pdf相关操作的vue组件  
  app.use(pdfViewer, {baseApi: import.meta.env.VITE_APP_BASE_API}); // 自动引入pdf-viewer-ulti等一系列pdf相关操作的vue组件  
  app.use(Antd)
  app.use(pinia)
  app.use(ElementPlus, {
    locale: zhCn,
  })
  for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    app.component(key, component)
  }
  app.use(router)
  app.mount('#app')
}


bootstrap()
