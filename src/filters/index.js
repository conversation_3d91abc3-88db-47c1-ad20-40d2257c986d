export default function () {
    const getAuditStatusColor = computed(() => {
        return function (statusName) {
            switch (statusName) {
                case '【 初审通过 】':
                case '【 终审通过 】':
                    return "#0bbd87";
                case '【 核验不通过 】':
                    return "#e40d0d";
                case '【 流程退回 】':
                    return "#e40d0d";
                case '【 待核验 】':
                    return "#FF9933";
            }
        }
    })
    const getInsStatusColor = computed(() => {
        return function (statusName) {
            switch (statusName) {
                case '办结':
                    return "#0bbd87";
                case '未反馈':
                    return "#e40d0d";
                case '已反馈':
                    return "#FF9933";
                case '撤回':
                    return "rgba(29,29,183,0.93)";
            }
        }
    })
    const getAuditStatusColorByLock = computed(() => {
        return function (statusName) {
            console.log('statusName',statusName)
            switch (statusName) {
                case '锁定中':
                case '【 终审通过 】':
                    return "#0bbd87";
                case '【 核验不通过 】':
                    return "#e40d0d";
                case '已解锁':
                    return "#0278ff";
                case '变更待核验':
                    return "#FF9933";
                case '解锁待核验':
                    return "#ff7a33";
            }
        }
    })
    const getAuditStatusColorDetail = computed(() => {
        return function (statusName) {
            switch (statusName) {
                case '【 审核通过 】':
                case '【 终审通过 】':
                    return "#0bbd87";
                case '【 审核不通过 】':
                    return "#e40d0d";
                case '【 流程退回 】':
                    return "#e40d0d";
                case '【 待审核 】':
                    return "#FF9933";
            }
        }
    })
    return {
        getAuditStatusColor,
        getInsStatusColor,
        getAuditStatusColorByLock,
        getAuditStatusColorDetail
    }
}
