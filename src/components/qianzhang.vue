<template>
  <el-dialog v-model="centerDialogVisible" title="电子签章" width="900" center>
    <iframe id="mainIframe" ref="iframe" frameborder="0" :src="url"></iframe>
  </el-dialog>
</template>

<script setup >
import { httpGet, httpPost } from "@wl-fe/http/dist";
const emit = defineEmits(["getPdfInfo"]);
const centerDialogVisible = ref(false);
const url = ref("");
const handleGetPdfInfo = (pdfUrl) => {
  centerDialogVisible.value = true;
  url.value = pdfUrl;
  window.addEventListener("message", handleMessage);
};
const resetInfo = () => {
  window.removeEventListener("message", handleMessage);
};
const dataUrl = ref(null);
const handleMessage = async (event) => {
  if (typeof event.data == "string") {
    const data = await httpPost("/sysUpload/uploadBaseFile", {
      base64Image: event.data,
    });
    console.log(data);
    dataUrl.value = data;
    emit("getPdfInfo", dataUrl.value);
    resetInfo();
    url.value = "";
    centerDialogVisible.value = false;
  }
};
defineExpose({handleGetPdfInfo})
</script>

<style lang="less" scoped>
#mainIframe {
  width: 100%;
  height: 600px;
  display: block;
  margin: 0 auto 10px auto;
}
</style>
