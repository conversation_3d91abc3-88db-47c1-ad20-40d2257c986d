<script setup>
import { Edit, View as IconView } from '@element-plus/icons-vue'
// 定义 tooltipOptions 对象
const tooltipOptions = ref({
    effect: 'light', // 提示框主题为亮色
    placement: 'top', // 提示框显示在单元格上方
    showArrow: true, // 显示提示框的箭头
    hideAfter: 200, // 提示框隐藏的延迟时间为 200 毫秒
    popperOptions: {
        strategy: 'fixed' // 使用 fixed 定位策略
    }
});
defineProps({
  tableData: Array,
  tableColumns: Array,
});
</script>

<template>
  <el-table
    class="wl-table"
    :data="tableData"
    row-class-name="list-row"
  >
      <!-- 添加序号列 -->
      <el-table-column
              type="index"
              label="序号"
              align="center"
              width="70"
              :show-overflow-tooltip="true"
      />
    <el-table-column
      v-for="(item, index) in tableColumns"
      :key="item.dataIndex"
      :prop="item.dataIndex"
      :label="item.title"
      :width="item.width"
      :show-overflow-tooltip="true"
    >
      <template v-if="item.render" #default="scope">
        <component
          :is="item.render(scope.row[item.dataIndex], scope.row, index, item)"
        />
      </template>
        <template v-if=" item.title =='项目名称'" #default="scope">
            <span v-html="scope.row[item.dataIndex]" ></span>
        </template>
      <template v-if="item.isSelf && item.title =='是否对外发布'" #default="scope">
        <span :style="{color: scope.row[item.dataIndex] == '否'? 'red' : '#000' }">{{ scope.row[item.dataIndex] }}</span>
      </template>
      <template v-if="item.isSelf && item.title =='附件'" #default="scope">
        <div v-for="(val,indev) in scope.row[item.dataIndex]" :key="indev">
            <el-link :href="val.url" target="_blank"  type="primary" :underline="false">{{ val.attachmentFileName}}
                <el-icon class="el-icon--right"><icon-view /></el-icon></el-link>
            <div v-if="indev < scope.row[item.dataIndex].length - 1" style="border-bottom: 1px solid #ccc; margin: 5px 0px;"></div>
        </div>
      </template>
    </el-table-column>
  </el-table>
</template>

<style scoped lang="less">
.wl-table {
  :deep(.el-table__header-wrapper) {
    .el-table__cell {
      background: #f0f6ff;
      height: 55px;
      color: rgba(0, 0, 0, 0.88);
    }
  }
  :deep(.list-row) {
    .el-table__cell {
      padding: 14px 0px !important;
    }
  }
  .status-item {
    display: flex;
    align-items: center;
    > img {
      width: 16px;
      height: 16px;
      margin-right: 6px;
    }
  }
}
:deep(.el-popper) {
  max-width: 350px !important;
}
</style>
