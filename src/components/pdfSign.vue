<template>
  <div id="pdfSign">
    <div class="iframe-box">
      <iframe
        id="iframe"
        ref="iframe"
        width="100%"
        height="100%"
        frameborder="0"
        :src="iframeSrc"
      ></iframe>
    </div>
    <div class="btn-box">
      <el-button
        class="btn-footer"
        round
        type="primary"
        plain
        @click="handleClose"
        v-if="!pdfQzUrl"
        >关 闭</el-button
      >
      <el-button
        v-if="pdfQzUrl"
        class="btn-footer"
        round
        type="primary"
        plain
        @click="handleClose"
        >确认</el-button
      >
      <el-button
        :disabled="pdfQzUrl"
        :type="pdfQzUrl ? 'info' : 'primary'"
        class="btn-footer"
        round
        @click="handleSign()"
        >{{ pdfQzUrl ? "已签章" : "签 章" }}</el-button
      >
    </div>
  </div>
  <qianzhang ref="qzel" @getPdfInfo="getPdfInfo"></qianzhang>
  <newQz @signedSuccess="signedSuccess" ref="newQzEl"></newQz>
</template>

<script setup>
import { httpPost } from "@wl-fe/http/dist";
import { ref, computed } from 'vue';
const emit = defineEmits(["handleCloseQz"]);
const props = defineProps({
  pathUrl: {
    type: String,
    default: "",
  },
  pdfRowGuid: {
    type: String,
    default: "",
  },
});
let pdfUrl = ref(props.pathUrl);
const pdfQzUrl = ref(false);
const qzel = ref(null);

const iframeSrc = computed(() => {
    return import.meta.env.VITE_PDF_WEB_VIEW + pdfUrl.value;
});

function handleClose() {
  emit("handleCloseQz");
}
const newQzEl = ref(null);
function handleSign() {
  if(import.meta.env.VITE_PROXY_QZ == 'QZ'){
    let info = {
      id: new Date().getTime(), //fileRowGuid.value,
      fileUrl:pdfUrl.value
    }
    newQzEl.value.open(info);
  } else{
    let urlEncode = window.btoa(`${window.location.origin}/`);
    let encode = window.btoa(pdfUrl.value);
    let pdfPropUrl = `https://qzpublic.lnwlzb.com/signature/H5QZ.htm?fileURL=${encode}&back=${urlEncode}`;
    qzel.value.handleGetPdfInfo(pdfPropUrl);
  }
}
const signedSuccess = async (signInfo) =>{
    console.log("signInfo", signInfo,JSON.parse(signInfo.outResp));
    newQzEl.value.handleClose();
    try {
         let res = JSON.parse(signInfo.outResp)
         console.log('resresres',res);
         pdfUrl.value = res.msg;
          pdfQzUrl.value = res.msg;
          // console.log("pdfInfo", pdfInfo);
          let info = {
            rowGuid: props.pdfRowGuid,
            signatureUrl: pdfQzUrl.value,
          };
          await httpPost("api/sysAttach/updateSignatureFile", info);
          ElMessage({
            message: "签章成功！",
            type: "success",
          });
    } catch (e) {
         console.log('eerr',e);
         ElMessage({
            message: "签章失败！",
            type: "error",
          });
    }
  }
const getPdfInfo = async (pdfInfo) => {
  pdfUrl.value = pdfInfo.fj_url;
  pdfQzUrl.value = pdfInfo.fj_url;
  console.log("pdfInfo", pdfInfo);
  let info = {
    rowGuid: props.pdfRowGuid,
    signatureUrl: pdfQzUrl.value,
  };
  await httpPost("api/sysAttach/updateSignatureFile", info);
  ElMessage({
    message: "签章成功！",
    type: "success",
  });
};
</script>

<style lang="less" scoped>
#pdfSign {
  width: 100%;
  height: 100%;

  .iframe-box {
    width: 100%;
    height: 90%;
    border: 1px solid #000;
  }
  .btn-box {
    width: 100%;
    height: 50px;
    margin-top: 20px;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    .btn-footer {
      width: 120px;
    }
  }
}
</style>