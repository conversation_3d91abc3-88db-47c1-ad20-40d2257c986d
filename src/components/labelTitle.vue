<script setup>
defineProps({
  title: String,
  has<PERSON>arin: <PERSON><PERSON><PERSON>,
});
</script>

<template>
  <div :class="['label', hasMarin && 'has-margin']">{{ title }} <slot></slot> </div>
</template>

<style scoped lang="less">
.label {
  font-weight: bold;
  display: flex;
  align-items: center;
  &::before {
    content: "";
    display: block;
    width: 4px;
    height: 15px;
    margin-right: 6px;
    background-color: #3b84f9;
  }
  &.has-margin {
    margin: 16px 0px;
  }
}
</style>
