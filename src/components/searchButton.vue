<script setup>
import aiTipIcon from "/components/aitip.png";
const inputValue = ref("");
defineProps({
  autofocus: <PERSON><PERSON><PERSON>,
  isSupportImg: <PERSON>olean,
  isSupportVideo: <PERSON>olean,
  isShowAiTip: <PERSON>olean,
});
const emit = defineEmits(["search"]);
</script>

<template>
  <div class="search-button">
    <slot></slot>
    <el-input
      v-model="inputValue"
      class="input-with-select"
      v-bind="$attrs"
      style="width: 500px !important;"
      v-on:keydown.enter="emit('search', inputValue)"
    > 
    <template #append>
      <el-button type="primary"  @click="emit('search', inputValue)" :icon="Search" round>搜索</el-button>
      </template>
    </el-input>
    <span className="tip" v-if="isShowAiTip">
      <img :src="aiTipIcon" alt="" className="icon" />
      更多条件您可以前往智能监管进行搜索...</span
    >
    
  </div>
</template>

<style scoped lang="less">
.search-button {
  border-radius: 8px;
  display: flex;
  align-items: center;
  .search{
    cursor: pointer;
  }
  .el-input {
    height: 40px;
    :deep(.el-input__wrapper) {
      border-top-left-radius: 24px;
      border-bottom-left-radius: 24px;
      padding-left: 20px;
      // margin-right: 10px;
    }
    :deep(.el-input-group__append) {
      border-top-right-radius: 24px;
      border-bottom-right-radius: 24px;
      color: #fff;
      background: #1677ff;
      border:1px solid #1677ff;
      box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0);
    }
  }
  .tip {
    margin-left: 24px;
    display: flex;
    align-items: center;
    font-size: 16px;
    .icon {
      width: 18px;
      height: 18px;
      margin-right: 4px;
    }
  }
}
</style>
