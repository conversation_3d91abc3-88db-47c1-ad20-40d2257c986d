<template>
  <el-drawer :size="'100%'" v-model="drawer" :with-header="false">
    <div style="width: 100%; height: 90%" class="my-qz-draw">
      <pdf-viewer-ulti
        ref="pdfViewerRef"
        :autoReload="false"
        @signed="signed"
        @resigned="resigned"
        @unsigned="unsigned"
        :qrDriverName="type"
      ></pdf-viewer-ulti>
    </div>
    <div class="d-btn-sty">
        <el-button class="btn-footer" type="primary" @click="handleSuccess" round>
            确 认
        </el-button>
      <el-button
        @click="handleClose"
        class="btn-footer"
        type="danger"
        plain
        round
        >关 闭</el-button
      >
    </div>
  </el-drawer>
</template>

<script setup>
const emit = defineEmits(["signedSuccess"]);
const drawer = ref(false);
const pdfViewerRef = ref();
const qzInfo = ref();
const type = ref('EP')
let loginType = sessionStorage.getItem('loginType')
if(loginType == 'appNew'){
    type.value = 'UN'
}
const open = (info) => {
  drawer.value = true;
  console.log("open", info);
  setTimeout(() => {
    pdfViewerRef.value.load(info.id, {
      filePath: "",
      fileUrl: info.fileUrl,
      fileOutUrl: `${import.meta.env.VITE_APP_RQUEST_API}sysUpload/uploadFileNew`,
      // fileOutUrl: "https://**************:8888/mg-admin-back/sysUpload/uploadFileNew",
    });
  }, 200);
};
const handleSuccess = () => {
    // emit("signedSuccess", pdfViewerRef.value.getPdfInfo());
    emit("signedSuccess", qzInfo.value);
    handleClose();
};
const handleClose = () => {
  document.body.style.zoom =   sessionStorage.getItem("scale") || 1;
  drawer.value = false;
};
const signed = (inf) => {
  // console.log("signed", inf);
  // emit("signedSuccess", inf);
    console.log("signed", inf);
    qzInfo.value = inf;
    setTimeout(() => {
        pdfViewerRef.value.load(inf.fileId, {
            filePath: "",
            fileUrl: JSON.parse(inf.outResp).msg,
            fileOutUrl: `${import.meta.env.VITE_APP_RQUEST_API}sysUpload/uploadFileNew`,
            // fileOutUrl: "https://**************:8888/mg-admin-back/sysUpload/uploadFileNew",
        });
    }, 200);
};
const resigned = (inf) => {
  console.log("resigned", inf);
};
const unsigned = (inf) => {
  console.log("unsigned", inf);
};
defineExpose({ open ,handleClose});
</script>

<style lang="less" scoped>
.d-btn-sty {
  width: 100%;
  height: 10%;
  display: flex;
  justify-content: center;
  align-items: center;
  .btn-footer {
    width: 10%;
  }
}
.my-qz-draw{
  :deep(.page){
     box-sizing: content-box;
  }
}
</style>
