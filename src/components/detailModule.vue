<template>
  <div v-if="moduleInfo instanceof Array">
    <div class="descriptions-header-array unify-title unify-title1">
      {{ headerTitle.replace(/\*/g, "") }}
    </div>
    <el-table
      :row-class-name="tableRowClassName"
      class="my-table"
      :data="moduleInfo"
      style="width: 100%"
    >
      <el-table-column
        label="序号"
        type="index"
        :index="indexMethod"
        align="center"
      />
      <el-table-column
        v-for="(item, key, index) in getFirstFiveEntries(moduleInfo[0])"
        :key="index"
        :prop="item"
        :label="key"
        min-width="20%"
        align="center"
      >
        <template #default="scope">{{ scope.row[key] }}</template>
      </el-table-column>
      <el-table-column label="操作" min-width="10%" align="center">
        <template  #default="{ row }">
          <el-text class="my-el-text" type="primary" @click="handleViewDetail(row)">查看详情</el-text>
        </template>
      </el-table-column>
    </el-table>
  </div>
  <div v-else>
    <el-descriptions :column="2" :size="'Large'" border>
      <template #title v-if="headerTitle">
        <div class="descriptions-header unify-title unify-title1">
          {{ headerTitle.replace(/\*/g, "") }}
        </div>
      </template>
      <el-descriptions-item
        label-class-name="my-descriptions"
        v-for="(val, key, index) in moduleInfo"
        :span="val?.length > 20 ? 2 : 1"
        :key="index"
      >
        <template #label>
          <div class="cell-item">
            {{ key ? key.replace(/\*/g, "") : "" }}
          </div>
        </template>
        <img v-if=" typeof val === 'string' && val.includes('data:') " :src="val"/>
        <span v-else v-html="val"></span>
      </el-descriptions-item>
    </el-descriptions>
  </div>
  <el-drawer custom-class="my-drawer" v-model="isShowDrawer" v-if="isShowDrawer" size="50%" :show-close="false">
      <template #header>
        <h2></h2>
      </template>
      <detailModule :headerTitle="headerTitle" :moduleInfo="detailInfo"></detailModule>
    </el-drawer>
</template>

<script setup >
defineProps({
  moduleInfo: {
    type: Array,
    default: {},
  },
  headerTitle: {
    type: String,
    default: "",
  },
});
const isShowDrawer = ref(false);
const detailInfo = ref({});
const handleViewDetail = (row) => {
  isShowDrawer.value = true;
  detailInfo.value = row;
  console.log(row);
}
const getFirstFiveEntries = (obj) => {
  // 使用Object.keys()获取所有键，然后只取前五个
  const keys = Object.keys(obj).slice(0, 5);
  // 创建一个新对象，只包含前五个键及其对应的值
  const result = {};
  keys.forEach((key) => {
    result[key] = obj[key];
  });
  return result;
};
const tableRowClassName = ({ rowIndex }) => {
  if (rowIndex % 2 !== 0) {
    return "warning-row";
  }
  return "";
};
const indexMethod = (index) => {
  return index + 1;
};
</script>

<style scoped lang="less">
.descriptions-header {
  margin-top: 15px;
}
.descriptions-header-array{
  margin: 15px 0;
}
.descriptions-box {
  margin-bottom: 10px;
}
.my-table {
  :deep(.el-table__header-wrapper) {
    .el-table__cell {
      background: #f0f6ff;
      height: 55px;
      color: rgba(0, 0, 0, 0.88);
    }
  }
  :deep(.el-table__row) {
    height: 55px;
  }
  :deep(.warning-row) {
    background: #f9fbff;
  }
}
.my-el-text{
  cursor: pointer;
}
// .my-descriptions{
  :deep(.el-descriptions__content) {
    width: 25%;
  }
// }
</style>