<template>
  <el-dialog
    v-model="showDialog"
    custom-class="my-dialog"
    :width="dialogWidth"
    :close-on-click-modal="false"
    :show-close="false"
    top="7vh"
  >
    <template #header>
      <div class="dialog-header unify-title unify-title7">{{ dialogTitle }}</div>
    </template>
    <slot name="content">
        
    </slot>
  </el-dialog>
</template>

<script setup>
defineProps({
  dialogWidth: {
    type: String,
    default: "80%",
  },
  dialogTitle: {
    type: String,
    default: "",
  },
});
const showDialog = ref(false);
defineExpose({ showDialog });
</script>

<style lang="less" scoped>
.dialog-header {
  font-size: 18px;
  font-weight: bold;
  color: #000;
}
.my-dialog{
  margin-top: 7vh !important;
}
</style>