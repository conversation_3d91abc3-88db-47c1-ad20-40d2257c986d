import Jquery from '../jquery-1.11.1.min.js'
Jquery(function (factory) {
  if (typeof define === 'function' && define.amd) {
    // define(['jquery'], factory)
  } else if (typeof exports === 'object') {
    // factory(require('jquery'))
  } else {
    // factory(jQuery)
  }
}(function ($) {
  var pluses = /\+/g
  function encode(s) {
    return config.raw ? s : encodeURIComponent(s)
  }
  function decode(s) {
    return config.raw ? s : decodeURIComponent(s)
  }
  function stringifyCookieValue(value) {
    return encode(config.json ? JSON.stringify(value) : String(value))
  }
  function parseCookieValue(s) {
    if (s.indexOf('"') === 0) {
      s = s.slice(1, -1).replace(/\\"/g, '"').replace(/\\\\/g, '\\')
    }
    try {
      s = decodeURIComponent(s.replace(pluses, ' '))
      return config.json ? JSON.parse(s) : s
    } catch (e) { }
  }
  function read(s, converter) {
    var value = config.raw ? s : parseCookieValue(s)
    return $.isFunction(converter) ? converter(value) : value
  }
  var config = $.cookie = function (key, value, options) {
    if (value !== undefined && !$.isFunction(value)) {
      options = $.extend({}, config.defaults, options)
      if (typeof options.expires === 'number') {
        var days = options.expires; var t = options.expires = new Date()
        t.setTime(+t + days * 864e+5)
      }
      return (document.cookie = [
        encode(key), '=', stringifyCookieValue(value),
        options.expires ? '; expires=' + options.expires.toUTCString() : '', // use expires attribute, max-age is not supported by IE
        options.path ? '; path=' + options.path : '',
        options.domain ? '; domain=' + options.domain : '',
        options.secure ? '; secure' : ''
      ].join(''))
    }
    var result = key ? undefined : {}
    var cookies = document.cookie ? document.cookie.split('; ') : []
    for (var i = 0, l = cookies.length; i < l; i++) {
      var parts = cookies[i].split('=')
      var name = decode(parts.shift())
      var cookie = parts.join('=')
      if (key && key === name) {
        result = read(cookie, value)
        break
      }
      if (!key && (cookie = read(cookie)) !== undefined) {
        result[name] = cookie
      }
    }
    return result
  }

  config.defaults = {}
  $.removeCookie = function (key, options) {
    if ($.cookie(key) === undefined) {
      return false
    }
    $.cookie(key, '', $.extend({}, options, { expires: -1 }))
    return !$.cookie(key)
  }
}))
var iWebNet = {
  config: { rootUrl: 'http://127.0.0.1', catype: -1, def_port: 11345, port: 11345 },
  Init: function (catype, port) {
    if (!catype) { return }
    iWebNet.config.catype = catype
    // var csp = $.cookie(catype + '_port') // if (csp) {
    //     //   iWebNet.config.port = csp
    //     // }

    if (port) {
      iWebNet.config.def_port = port
    }
    iWebNet.connect(iWebNet.config).done(function (data) {
      if (data.CAType == iWebNet.config.catype) {
        // $.cookie(catype + '_port', port)
      } else {
        iWebNet.recon()
      }
    }).fail(function (data) {
      iWebNet.recon()
    })
  },
  recon: function () {
    var i = iWebNet.config.def_port
    for (; i < iWebNet.config.def_port + 10; i++) {
      iWebNet.config.port = i
      iWebNet.connect(iWebNet.config).done(function (data) {
        if (data.CAType == iWebNet.config.catype) {
          i = 999999
          $.cookie(catype + '_port', port)
        }
      })
    }
    if (i == 1000000) {
    } else {
      iWebNet.showMsg('程序启动失败，请重装驱动后重启电脑尝试。')
    }
  },
  connect: function (cfg) {
    $.support.cors = true
    return $.ajax({
      type: 'get',
      dataType: 'json',
      async: false,
      url: cfg.rootUrl + ':' + cfg.port,
      timeout: 150
    })
  },
  showMsg: function (s) {
    if (!window.epoint) {
      alert(s)
    } else {
      epoint.dialog.error(s)
    }
  },
  PostMsg: function (data) {
    return new Promise((resovle, reject) => {
      $.ajax({
        type: 'post',
        dataType: 'json',
        data: JSON.stringify(data),
        url: iWebNet.config.rootUrl + ':' + iWebNet.config.port,
      }).done(function (d) {
        resovle(d)
      }).fail(function (jqXHR, textStatus, errorThrown) {
        console.log('faile', errorThrown)
        reject(errorThrown)
      })

    })
  },
  Prepare: function (qrtype) {
    return {
      'body': {
        'qrinfo': {
          'qrcodetype': qrtype
        }
      }
    }
  }
}
var _Break = 'true'
function ThrowEx(Msg) { if (_Break) { alert(Msg); throw (new Error(-1, Msg)) } else { } }
function CaObj(NeedInitializtion, NeedBreak) { if (NeedBreak != undefined) { _Break = NeedBreak } if (NeedInitializtion) { if (!this.InitClientCert()) { } } }
CaObj.prototype.InitClientCert = async function () {
  var pData = iWebNet.Prepare('CAInit')
  var rtn = await iWebNet.PostMsg(pData)
  if (rtn.body.qrstatus == '1') return true
  else return false
}
CaObj.prototype.ClientSignCertSN = async function () {
  var rtnSN = await iWebNet.PostMsg(iWebNet.Prepare('ClientSignCertSN'))
  if (rtnSN != '') return rtnSN.body.qrinfo.certobject['sn']
  else return ''
}
CaObj.prototype.ClientSignCertCN = async function () {
  var rtnCN = await iWebNet.PostMsg(iWebNet.Prepare('ClientSignCertCN'))
  if (rtnCN != '') return rtnCN.body.qrinfo.certobject['cn']
  else return ''
}
CaObj.prototype.SignCert = async function () {
  var rtnSC = await iWebNet.PostMsg(iWebNet.Prepare('SignCert'))
  if (rtnSC != '') return rtnSC.body.qrinfo.certobject['signcert']
  else return ''
}
CaObj.prototype.EncCert = async function () {
  var rtnEC = await iWebNet.PostMsg(iWebNet.Prepare('EncCert'))
  if (rtnEC != '') return rtnEC.body.qrinfo.certobject['decryptcert']
  else return ''
}
CaObj.prototype.DeviceNum = async function () {
  var rtnDCN = await iWebNet.PostMsg(iWebNet.Prepare('DeviceNum'))
  if (rtnDCN != '') return rtnDCN.body.qrinfo.certobject['devicenum']
  else return ''
}
CaObj.prototype.CertSubjectKeyID = async function () {
  var rtnCKID = await iWebNet.PostMsg(iWebNet.Prepare('CertSubjectKeyID'))
  if (rtnCKID != '') return rtnCKID.body.qrinfo.certobject['skid']
  else return ''
}
CaObj.prototype.CertYouXiaoQi = async function () {
  var rtnYXQ = await iWebNet.PostMsg(iWebNet.Prepare('CertYouXiaoQi'))
  if (rtnYXQ != '') return rtnYXQ.body.qrinfo.certobject['yxq']
  else return ''
}
CaObj.prototype.SignData = async function (OrgData) {
  var SignData = {
    'body': {
      'qrinfo': {
        'signatureobjects': [{
          'org': OrgData
        }],
        'qrcodetype': 'SignData'
      }
    }
  }
  var rtnSigned = await iWebNet.PostMsg(SignData)
  if (rtnSigned != '') return rtnSigned.body.qrinfo.signatureobjects[0]['signed']
  else return ''
}
CaObj.prototype.VerifyData = async function (OrgData, SingCerInfo, SinInfo) {
  var VerifyData = {
    'body': {
      'qrinfo': {
        'signatureobjects': [{
          'org': OrgData,
          'signed': SinInfo,
          'cert': SingCerInfo
        }],
        'qrcodetype': 'VerifyData'
      }
    }
  }
  var rtnVRRes = await iWebNet.PostMsg(VerifyData)
  if (rtnVRRes != '') {
    if (rtnVRRes.body.qrinfo.signatureobjects[0]['desc'] == '1') return true
  }
  return false
}
CaObj.prototype.Encrypt = async function (Data) {
  var EncData = {
    'body': {
      'qrinfo': {
        'decryptobjects': [{
          'org': Data
        }],
        'ext': {
          'CertPath': ''
        },
        'qrcodetype': 'Encrypt'
      }
    }
  }
  var rtnEnc = await iWebNet.PostMsg(EncData)
  if (rtnEnc != '') return rtnEnc.body.qrinfo.decryptobjects[0]['encryed']
  else return ''
}
CaObj.prototype.Decrypt = async function (JiaMiData) {
  var DecData = {
    'body': {
      'qrinfo': {
        'decryptobjects': [{
          'encryed': JiaMiData
        }],
        'qrcodetype': 'Decrypt'
      }
    }
  }
  var rtnDec = await iWebNet.PostMsg(DecData)
  if (rtnDec != '') return rtnDec.body.qrinfo.decryptobjects[0]['org']
  else return ''
}
CaObj.prototype.Bfjg = async function () {
  var rtnBfjg = await iWebNet.PostMsg(iWebNet.Prepare('Bfjg'))
  if (rtnBfjg != '') return rtnBfjg.body.qrinfo.certobject['dn']
  else return ''
}
CaObj.prototype.GetImgData = async function (AreaType, Lic) {
  var CPData = {
    'body': {
      'qrinfo': {
        'ext': {
          'AreaType': AreaType,
          'Lic': Lic
        },
        'qrcodetype': 'GetEpImgData'
      }
    }
  }
  var rtnVRRes = await iWebNet.PostMsg(CPData)
  if (rtnVRRes != '') {
    return rtnVRRes.body.qrinfo.ext['ImgData']
  }
  return false
}
CaObj.prototype.MakeSeal = async function (AreaType, ImgData, Lic) {
  var CPData = {
    'body': {
      'qrinfo': {
        'ext': {
          'AreaType': AreaType,
          'ImgData': ImgData,
          'Lic': Lic
        },
        'qrcodetype': 'MakeSeal'
      }
    }
  }
  var rtnVRRes = await iWebNet.PostMsg(CPData)
  if (rtnVRRes != '') {
    return rtnVRRes.body.qrinfo.ext['res']
  }
  return false
}

CaObj.prototype.ModSeal = async function (AreaType, ImgData, Lic) {
  var CPData = {
    'body': {
      'qrinfo': {
        'ext': {
          'AreaType': AreaType,
          'ImgData': ImgData,
          'Lic': Lic
        },
        'qrcodetype': 'ModSeal'
      }
    }
  }
  var rtnVRRes = await iWebNet.PostMsg(CPData)
  if (rtnVRRes != '') {
    return rtnVRRes.body.qrinfo.ext['res']
  }
  return false
}

CaObj.prototype.MakeNewSeal = async function (UserInfo, SealInfo, Lic) {
  var CPData = {
    'body': {
      'qrinfo': {
        'ext': {
          'UserInfo': UserInfo,
          'SealInfo': SealInfo,
          'Lic': Lic
        },
        'qrcodetype': 'MakeNewSeal'
      }
    }
  }
  var rtnVRRes = await iWebNet.PostMsg(CPData)
  if (rtnVRRes != '') {
    return rtnVRRes.body.qrinfo.ext['res']
  }
  return false
}
CaObj.prototype.CheckPin = async function (pwd) {
  var rtnCHP = {
    'body': {
      'qrinfo': {
        'ext': {
          'pwd': pwd,
        },
        'qrcodetype': 'CheckPin'
      }
    }
  };
  var rtnCHP2 = await iWebNet.PostMsg(rtnCHP);
  if (rtnCHP2 != '') return rtnCHP2.body.qrinfo.ext['pwd'];
  else return ''
}

// 查看当前浏览器
function BrowserInfo() {
  var res = {
    name: '',
    version: ''
  }
  var reg
  var userAgent = self.navigator.userAgent
  if (reg = /edge\/([\d\.]+)/i.exec(userAgent)) {
    res.name = 'Edge'
    res.version = reg[1]
  } else if (reg = /edg\/([\d\.]+)/i.exec(userAgent)) {
    res.name = 'Edge(Chromium)'
    res.version = reg[1]
  } else if (/msie/i.test(userAgent)) {
    res.name = 'Internet Explorer'
    res.version = /msie ([\d\.]+)/i.exec(userAgent)[1]
  } else if (/Trident/i.test(userAgent)) {
    res.name = 'Internet Explorer'
    res.version = /rv:([\d\.]+)/i.exec(userAgent)[1]
  } else if (/chrome/i.test(userAgent)) {
    res.name = 'Chrome'
    res.version = /chrome\/([\d\.]+)/i.exec(userAgent)[1]
  } else if (/safari/i.test(userAgent)) {
    res.name = 'Safari'
    res.version = /version\/([\d\.]+)/i.exec(userAgent)[1]
  } else if (/firefox/i.test(userAgent)) {
    res.name = 'Firefox'
    res.version = /firefox\/([\d\.]+)/i.exec(userAgent)[1]
  }
  return res
}

// 校验是否安装插件  火狐的话没有的话   自动使用拓展插件下载
function LoadObj() {
  try {
    var browser = BrowserInfo()
    // console.log(browser);
    if (browser.name != 'Firefox' && browser.name != 'Chrome') {
      Message.error('请使用Chrome浏览器或者火狐浏览器')
      return
    }
  } catch (e) {
    alert(e)
    return
  }
}

export default {
  initCAService: function (NeedInitializtion) {
    if (NeedInitializtion) {
      LoadObj()
    }
  },

  initCaObj: function () {
    return new Promise(async (resovle, reject) => {
      iWebNet.Init(260)
      var ca = new CaObj(true)
      const [cc_SKey, cc_SN, cc_YXQ, cc_SCert, cc_ECert, deviceNum, orgname, bfjg] = await Promise.all(
        [ca.CertSubjectKeyID(),
        ca.ClientSignCertSN(),
        ca.CertYouXiaoQi(),
        ca.SignCert(),
        ca.EncCert(),
        ca.DeviceNum(),
        ca.ClientSignCertCN(),
        ca.Bfjg()
        ])
      if (!cc_SCert || !cc_SKey || !cc_SN || !cc_SCert || !cc_YXQ || !cc_ECert || !deviceNum) {
        const errorMsg="未检测到CA锁信息，请确认互联互通驱动是否为最新版本同时将CA锁插好!"
        reject(errorMsg)
      }
      let caobj = {};
      caobj.signCert = cc_SCert;
      caobj.encCert = cc_ECert;
      caobj.signCertSN = cc_SN;
      caobj.encCertSN = cc_SKey;
      caobj.deviceNum = deviceNum;
      caobj.yxq = cc_YXQ;
      caobj.orgname = orgname;
      caobj.bfjg=bfjg
      resovle(caobj)
    })
  },

  initCa: function () {
    iWebNet.Init(260)
    return new CaObj(true)
  },

  ClientSignCertSN: function () {
    try {
      iWebNet.Init(260)
      var ca = new CaObj(true)
      var cc_SN = ca.ClientSignCertSN()
      if (!cc_SN) {
        // Message.error('读取证书序列号信息出现异常，请检查锁是否插好!')
        Message.error('未检测到CA锁信息，请确认互联互通驱动是否为最新版本同时将CA锁插好!')
        return
      }
      return cc_SN
    } catch (ex) {
      // Message.error('读取证书序列号信息出现异常，请检查锁是否插好!')
      Message.error('未检测到CA锁信息，请确认互联互通驱动是否为最新版本同时将CA锁插好!')
    }
  },
  ClientSignCertCN: async function () {
    var locker = ''
    try {
      iWebNet.Init(260)
      var ca = new CaObj(true)
      var cc_SCert = ca.SignCert()
      if (!cc_SCert) {
        // Message.error('读取使用者信息出现异常，请检查锁是否插好!')
        Message.error('未检测到CA锁信息，请确认互联互通驱动是否为最新版本同时将CA锁插好!')
        return
      }
      return locker
    } catch (ex) {
      // Message.error('读取使用者信息出现异常，请检查锁是否插好!')
      Message.error('未检测到CA锁信息，请确认互联互通驱动是否为最新版本同时将CA锁插好!')
    }
  },
  CertYouXiaoQi: function () {
    try {
      iWebNet.Init(260)
      var ca = new CaObj(true)
      var cc_YXQ = ca.CertYouXiaoQi()
      if (!cc_YXQ) {
        // Message.error('读取有效期信息出现异常，请检查锁是否插好!')
        Message.error('未检测到CA锁信息，请确认互联互通驱动是否为最新版本同时将CA锁插好!')
        return
      }
      return cc_YXQ
    } catch (ex) {
      // Message.error('读取有效期信息出现异常，请检查锁是否插好!')
      Message.error('未检测到CA锁信息，请确认互联互通驱动是否为最新版本同时将CA锁插好!')
    }
  },
  CretQianMingZS: function () {
    try {
      iWebNet.Init(260)
      var ca = new CaObj(true)
      var cc_SCert = ca.SignCert()
      if (!cc_SCert) {
        // Message.error('读取签名证书内容出现异常，请检查锁是否插好!')
        Message.error('未检测到CA锁信息，请确认互联互通驱动是否为最新版本同时将CA锁插好!')
        return
      }
      return cc_SCert
    } catch (ex) {
      // Message.error('读取签名证书内容出现异常，请检查锁是否插好!')
      Message.error('未检测到CA锁信息，请确认互联互通驱动是否为最新版本同时将CA锁插好!')
    }
  },
  CretQianMingJMZS: function () {
    try {
      iWebNet.Init(260)
      var ca = new CaObj(true)
      var cc_ECert = ca.EncCert()
      if (!cc_ECert) {
        // Message.error('读取加密证书内容出现异常，请检查锁是否插好!')
        Message.error('未检测到CA锁信息，请确认互联互通驱动是否为最新版本同时将CA锁插好!')
        return
      }
      return cc_ECert
    } catch (ex) {
      // Message.error('读取加密证书内容出现异常，请检查锁是否插好!')
      Message.error('未检测到CA锁信息，请确认互联互通驱动是否为最新版本同时将CA锁插好!')
    }
  },
  CertSubjectKeyID: function () {
    try {
      iWebNet.Init(260)
      var ca = new CaObj(true)
      var cc_SKey = ca.CertSubjectKeyID()
      if (!cc_SKey) {
        // Message.error('读取使用者密钥标识符信息出现异常，请检查锁是否插好!')
        Message.error('未检测到CA锁信息，请确认互联互通驱动是否为最新版本同时将CA锁插好!')
        return
      }
      return cc_SKey
    } catch (ex) {
      // Message.error('读取使用者密钥标识符信息出现异常，请检查锁是否插好!')
      Message.error('未检测到CA锁信息，请确认互联互通驱动是否为最新版本同时将CA锁插好!')
    }
  },
  Bfjg: function () {
    try {
      iWebNet.Init(260)
      var ca = new CaObj(true)
      var cc_DN = ca.Bfjg()
      if (!cc_DN) {
        // Message.error('读取证书颁发者信息出现异常，请检查锁是否插好!')
        Message.error('未检测到CA锁信息，请确认互联互通驱动是否为最新版本同时将CA锁插好!')
        return
      }
      return cc_DN
    } catch (ex) {
      // Message.error('读取证书颁发者信息出现异常，请检查锁是否插好!')
      Message.error('未检测到CA锁信息，请确认互联互通驱动是否为最新版本同时将CA锁插好!')
    }
  },
  CheckPin: async function (pwd) {
    await iWebNet.Init(260);
    var ca = await new CaObj(true);
    var rtn = await ca.CheckPin(pwd);
    if (rtn == 1) {
      return true
    }
    else {
      Message.error('数字证书密码错误，请重试！');
      return false;
    }
  },
  QianZhangInfo: function (xh) {
    try {
      var str = '3LKzp8gknhTBrYi1xoUCHJs3rdbeMSjTabufvyVSkis5s/47y9PRwnoy923L09tYjUhxtrGxXLDV1lAGAP5t7r/LlRKM3xe39elUfaQOkJrpwBYB5ZK4aWKS7bgkuKGXIP7v+XxFp9rIWv8JothGtVr7J1JUUX4tBHj6pyYLGZhI7CTszRbhDgZla7kdKPXghzCfbK8ZdKHCr1UFrBsDTbtoZBg66aBiMob2GSxmJlouSzczzWFUiExSzwIhmKNgTmnBP3FVLv+BhYpekVz6OA==*3LKzp8gknhTBrYi1xoUCHJs3rdbeMSjTabufvyVSkis5s/47y9PRwnoy923L09tYjUhxtrGxXLDV1lAGAP5t7r/LlRKM3xe39elUfaQOkJrpwBYB5ZK4aWKS7bgkuKGXIP7v+XxFp9rIWv8JothGtVr7J1JUUX4tBHj6pyYLGZhI7CTszRbhDgZla7kdKPXg596Fs2sqpRxlBJyPiLGbx93AN7FFNrhY+zziDvbkeKjsdtHRq2mLA5nVN8/fhskkFAPean+DSQFHFsiSiX2vcQ==*|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'
      var ca = new CaObj(true)
      var cc = ca.GetImgData(3, str)
      if (!cc) {
        // Message.error('读取指定签章信息出现异常，请检查锁是否插好!')
        Message.error('未检测到CA锁信息，请确认互联互通驱动是否为最新版本同时将CA锁插好!')
        return
      }
      return cc
    } catch (ex) {
      // Message.error('读取指定签章信息出现异常，请检查锁是否插好!')
      Message.error('未检测到CA锁信息，请确认互联互通驱动是否为最新版本同时将CA锁插好!')
    }
  },
  DeviceNum: function () {
    try {
      iWebNet.Init(260)
      var ca = new CaObj(true)
      var cc_SN = ca.DeviceNum()
      if (!cc_SN) {
        // Message.error('读取设备唯一标识符信息出现异常，请检查锁是否插好!')
        Message.error('未检测到CA锁信息，请确认互联互通驱动是否为最新版本同时将CA锁插好!')
        return
      }
      return cc_SN
    } catch (ex) {
      // Message.error('读取设备唯一标识符信息出现异常，请检查锁是否插好!')
      Message.error('未检测到CA锁信息，请确认互联互通驱动是否为最新版本同时将CA锁插好!')
    }
  },
  InitClientCert: function () {
    var ca = new CaObj(true)
    var cc_SN_1 = ca.InitClientCert()
    return cc_SN_1
  },

  Decrypt: function (encData) {
    try {
      iWebNet.Init(260)
      var ca = new CaObj(true)
      var decryptData = ca.Decrypt(encData)
      // if (!decryptData) {
      //   Message.error('解密失败!')
      //   return
      // }
      return decryptData
    } catch (ex) {
      Message.error('解密失败!')
    }
  },

  Ecrypt: function (encData) {
    try {
      iWebNet.Init(260)
      var ca = new CaObj(true)
      var ecryptData = ca.Encrypt(encData)
      if (!ecryptData) {
        Message.error('加密失败!')
        return
      }
      return ecryptData
    } catch (ex) {
      Message.error('加密失败!')
    }
  },

  getEncCert: function () {
    try {
      iWebNet.Init(260)
      let ca = new CaObj(true)
      return ca.EncCert()
    } catch (ex) {
      // Message.error('读取证书异常，请检查锁是否插好!')
      Message.error('未检测到CA锁信息，请确认互联互通驱动是否为最新版本同时将CA锁插好!')
    }
  }
}
