import dayjs from 'dayjs'
import cerModule from './core.js'


export type CaInfoType = Partial<{
    orgname: string,
    deviceNum: string,
    signCertSN: string
    signCert: string
    bfjg: string,
    yxq: string,
    diffDay: number,
    dayTip: string
}>

class Ca {
    caInfo: CaInfoType
    constructor() {
        this.caInfo = {}
        this.init()
    }

    async init() {
        // @ts-ignore
        window.Message = ElMessage
        await cerModule.InitClientCert()
        await cerModule.initCAService(true)
    }

    validiDate(time: string) {
        let dayTip = ''
        if (time) {
            time = time?.split(' ')[0].replace(new RegExp('/', 'gm'), '-')
            // 有效期处理
            if (time?.split('-')?.length > 0) {
                const diffDay = dayjs(time).diff(dayjs(), 'day')
                // 如果证书有效期小于90天将弹出提示信息
                if (diffDay < 0) {
                    dayTip = time + ' 此证书已经到期，为保障业务正常开展，请及时办理延期手续!'
                } else if (diffDay < 90) {
                    dayTip = time + '  (距过期还有' + diffDay + '天) '
                } else {
                    dayTip = time + ' (距过期还有' + diffDay + '天) ' 
                }
            }
        }
        return dayTip

    }
    async read() {
        const result =await cerModule.initCaObj() as any
        this.caInfo = {
            ...result,
            diffDay: dayjs(result.yxq).diff(dayjs(), 'day'),
            dayTip: this.validiDate(result.yxq)
        }
        return this.caInfo
    }
    async checkPassword(password:string){
        return await cerModule.CheckPin(password)
    }
}

export default Ca