import { ElMessage } from "element-plus";
import html2canvas from "html2canvas";

// 本系统只支持谷歌浏览器
export const isAvailableBrow = () => {
    // 获取浏览器的用户代理字符串
    const userAgent = navigator.userAgent;

    // 正则表达式匹配Chrome和Firefox
    const isChrome = /Chrome/i.test(userAgent);
    const isFirefox = /Firefox/i.test(userAgent);
    const isAvailable = isChrome || isFirefox
    if (!isAvailable) {
        ElMessage.error("请使用Chrome浏览器或者火狐浏览器!")
    }
    return isAvailable
}

export const openUrl = (url: string) => {
    if (url) {
        window.open(url, "_blank");
    } else {
        ElMessage.error("暂无跳转链接!")
    }
}

export const generateUUID = () => {
    let timestamp = new Date().getTime();
    const uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        const r = (timestamp + Math.random() * 16) % 16 | 0;
        timestamp = Math.floor(timestamp / 16);
        return (c === 'x' ? r : (r & 0x3 | 0x8)).toString(16);
    });
    return uuid;
}

export const getRandomFromArr = (array: any) => {
    return array[Math.floor(Math.random() * array.length)];
}


export const scrollToBottom = (id: string) => {
    setTimeout(() => {
        const element = document.getElementById(id) as any;
        element.scrollTop = element.scrollHeight - element.clientHeight;
    }, 0);
}

export const screenshotHtml = async (ref: any, name?: string) => {
    return new Promise<void>((resolve) => {
        ref.style.overflow = "visible"
        html2canvas(ref).then(function (canvas) {
            ref.style.overflow = "auto"
            const link = document.createElement('a');
            link.href = canvas.toDataURL();
            link.setAttribute('download', `${name ? name : generateUUID()}.png`);
            link.style.display = 'none';
            document.body.appendChild(link);
            link.click();
            ElMessage.success("下载成功！")
            resolve()
        });
    })
}

export const getSummaryIconCommonIndex = (index: number) => {
    const t = 4
    return index % t + 1
}

