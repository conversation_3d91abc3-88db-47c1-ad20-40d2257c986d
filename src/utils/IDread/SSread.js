
if (!window.WebSocket) {
  alert("该版本浏览器不支持WebSocket");
}

var websocket;

export function StartWebSocket(callBack) {
  websocket = new WebSocket("ws://127.0.0.1:9000/");
  websocket.onopen = function (evt) {
      //onOpen(evt) 
      // websocket.send("{\"Method\":\"SdtReadCard\", \"CameraName\":\"1-uni\"}");
      // document.getElementById("retinfo").value = "建立WebSocket连接成功";
      // return true
      callBack('神思')
  };
  websocket.onclose = function (evt) {
      //onClose(evt) 
      callBack('失败2')
  };

  websocket.onerror = function (evt) {
      //onError(evt) 
      console.log(evt);
      callBack('失败2')
      // return false
      // document.getElementById("retinfo").value = "建立WebSocket连接失败";
  };

}

export function OpenDevice() {
  websocket.send("{\"Method\":\"OpenDevice\",\"PortType\":\"AUTO\",\"PortPara\":\"\",\"ExtendPara\":\"\"}");
}

export function SdtSamGetIdStr(callBack){
    websocket.send("{\"Method\":\"SdtSamGetIdStr\"}");
    websocket.onmessage = function (evt) {
      console.log(evt.data);
      callBack(JSON.parse(evt.data).SamIdStr)
  };
}
            


function SsseReadCard() {
  var SsseReadCardjson = { "Method": "SsseReadCard", "iType": "xxxx" }
  SsseReadCardjson.iType = 4;


  var params = JSON.stringify(SsseReadCardjson);
  console.log(params);
  //websocket.send("{\"Method\":\"IdReadCard\",\"CardType\":\"16\",\"InfoEncoding\":\"1\",\"TimeOutMs\":\"0\"}");
  websocket.send(params);
}
export function IdReadCard(callBack) {
  OpenDevice()

  var IdReadCardjson = { "Method": "IdReadCard", "CardType": "0x10", "InfoEncoding": "0x02", "TimeOutMs": "xxxx" }
  IdReadCardjson.CardType = 16;
  IdReadCardjson.InfoEncoding = 1;
  IdReadCardjson.TimeOutMs = 0;

  var params = JSON.stringify(IdReadCardjson);
  console.log(params);
  //websocket.send("{\"Method\":\"IdReadCard\",\"CardType\":\"16\",\"InfoEncoding\":\"1\",\"TimeOutMs\":\"0\"}");
  websocket.send(params);

  websocket.onmessage = function (evt) {
    //onMessage(evt) 
    // document.getElementById("retinfo").value = evt.data;
    console.log(evt.data);
    var jsonsr = JSON.parse(evt.data);
    var data = jsonsr.IdCardInfo.split(":")
    var result = {
      name:data[1],
      idNumber:data[9],
      effDate:data[11],
      expDate:data[12],
      imgSrc:data[14],
    }
    callBack(result)
    //websocket.close();
};
}

function CloseDevice() {
  websocket.send("{\"Method\":\"CloseDevice\"}");
}
