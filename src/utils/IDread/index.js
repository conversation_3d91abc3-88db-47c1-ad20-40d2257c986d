import { connect, readCert } from "./JLread.js";
import { StartWebSocket, IdReadCard , SdtSamGetIdStr} from "./SSread.js";
import { OcrRecognition, ReadIDCard } from "./HSread.js";
import {  servicesend1, servicesend2 } from "./HDread.js";
let userInfo = {};
let funCallBack = ''

let HsConnect = function () {
  OcrRecognition(HsStatus);
}  
var getInfo = function(val) {
  console.error('神思数据', val);
  userInfo = val;
  funCallBack(userInfo)
  // this.transformDate();
}
var  HsStatus = function (type) {
  console.error("华视", type);

  if (type.lastIndexOf("失败") !== -1) {
    funCallBack('失败')
    // HdConnect();
  } else {
    this.isHs = false;
    this.onOcrRecognition();
    console.error(1111);
  }
}
var HdConnect = function () {
  servicesend1(HdStatus);
}
var HdStatus = function  (type) {
  // this.type = type;
  console.error("华大", type);
  if (this.type.lastIndexOf("失败") == -1) {
    // this.result = '端口连接失败！'
    userInfo = {}
    this.HdReadID();
  } else {
    console.error("华大", type);
    
  }
}
// import { queryModelSerino } from "@/api/wlApi";
export default {

  data() {
    return {
      result: "",
      type: "",
      user: {},
      isJl: false,
      isSs: false,
      isHs: false,
      isHd: false,
      isC: false,
      // connectFalse:false
    };
  },
  mounted() {
    // this.toConnect();
  },
  computed: {
    // 判断是否连接上了
    hasConnect() {
      console.error(88889, this.isJl, this.isSs, this.isHs, this.isHd);
      // 全为false是初始状态，全为true是全失败状态
      if (this.isJl && this.isSs && this.isHs && this.isHd || !this.isJl && !this.isSs && !this.isHs && !this.isHd) {
        // if (this.isJl && this.isSs && this.isHs  || !this.isJl && !this.isSs && !this.isHs ) {
        console.error(false);
        return false;
      } else {
        console.error(true);
        return true;
      }
    },
  },
  watch: {
    type: {
      handler(val) {
        console.error("变化了", val);
        if (val != "" && val != null) {
          // 滚动判断什么读卡器未连接上
          switch (val) {
            case "失败1":
              this.isJl = true;
              break;
            case "失败2":
              this.isSs = true;
              break;
            case "失败3":
              this.isHs = true;
              break;
            case "失败4":
              this.isHd = true;
              break;
          }
          console.error(8989, this.isJl, this.isSs, this.isHs, this.isHd);
          // 当读卡器都没有连接成功时弹窗
          if (this.isJl && this.isSs && this.isHs && this.isHd) {
            userInfo = {}
            this.$alert("未插入读卡器", "提示", {
              confirmButtonText: "确定",
              showClose: false,
            });
            // this.connectFalse = true
          }
          // 按下按钮并且没有返回失败后才调用
          if (this.isC && val.lastIndexOf("失败") == -1) {
            this.IdRead();
          }

          // 华大会有重复读取状态
          if (this.isC && val.lastIndexOf("重复读取了") !== -1) {
            this.HdReadID()
          }
        }
      },
    },
  },
  methods: {
    // 读卡
    IdRead() {
      // 判断是什么类型的读卡器连接成功了
      console.error('this.type', this.type);
      switch (this.type) {
        case "精伦":
          this.toReadCert();
          break;
        case "神思":
          this.IdReadCard();
          break;
        case "华视":
          this.onOcrRecognition();
          break;
        case "华大":
          this.HdReadID();
          break;
      }
    },
    // 清空数据
    clearData() {
      this.isJl = false;
      this.isSs = false;
      this.isHs = false;
      this.isHd = false;
      // this.connectFalse = false
    },
    // 按下连接
    isConnect() {
      // isC  当按下读取后才开始读卡
      this.isC = true
      // 当是精伦（第一个）并没有调watch
      if (this.hasConnect || (this.hasConnect && this.type == '精伦')) {
        this.IdRead();
      } else {
        this.toConnect();
      }
    },
    // 连接
    toConnect(callBack) {
      funCallBack = callBack
      this.clearData();
      let result = connect();
      console.error("结果:", result);
      this.type = result.type;
      if (this.type.lastIndexOf("失败") !== -1) {
        console.error(666);
        
        this.onStartWebSocket();
      } else {
        this.toReadCert()
      }
    },
    getInfo(){
        return userInfo
    },
    // 精伦读卡 TODO:
    async toReadCert() {
      let data = readCert();
      console.error("数据:", data);
      // const res = await queryModelSerino({ itemNo: data.readerCode })
      // const { code,msg } = JSON.parse(res.data)
      // console.error('codecodecode',code);
      // if(code == 200){
        userInfo = data;
        funCallBack(userInfo)
        // this.transformDate();
      // } else {
      //   this.$message.error(msg)
      // }
    },
    // 神思连接
    onStartWebSocket() {
      StartWebSocket(this.statusFun);
      console.error("神思");
    },
    // 神思连接返回值
    statusFun(type) {
      console.error("神思", type);
      
      // this.type = type;
      if (type.lastIndexOf("失败") !== -1) {
        console.error(555);
        
        HsConnect();
      } else {
        // this.IdReadCard();
        IdReadCard(getInfo);
      }
    },
    async getSdtSamGetIdStr(SamIdStr){
      console.error('神思读卡器模块号', SamIdStr);
      // const res = await queryModelSerino({ itemNo: SamIdStr.split('-').slice(0,3).join('-') })
      // const { code,msg } = JSON.parse(res.data)
      // console.error('codecodecode',code);
      // if(code == 200){
        IdReadCard(getInfo);
      // } else {
        // this.$message.error(msg)
      // }
    },
    // 神思读卡
    IdReadCard() {
      SdtSamGetIdStr(this.getSdtSamGetIdStr)
      // IdReadCard(this.getInfo);
    },
    // 神思读卡数据 TODO:
    async getInfo(val) {
      console.error(555, val);
      userInfo = val;
      funCallBack(userInfo)
      // this.transformDate();
    },
    // 华视连接
    HsConnect() {
      OcrRecognition(this.HsStatus);
    },
    // 华视连接返回值
    HsStatus(type) {
      console.error("华视", type);
      this.type = type;
      console.error(55555, this.type);
      if (this.type.lastIndexOf("失败") !== -1) {
        this.isHs = true;
        // this.result = '端口连接失败！'
        this.HdConnect();
      } else {
        this.isHs = false;

        this.onOcrRecognition();
        console.error(1111);
      }
    },
    // 华视读卡
    onOcrRecognition() {
      ReadIDCard(this.HsInfo);
    },
    // 华视读卡数据 TODO:
    async HsInfo(val) {
      console.error("hhhh", val);
      // const res = await queryModelSerino({ itemNo: val.readerCode })
      // const { code,msg } = JSON.parse(res.data)
      // console.error('codecodecode',code);
      // if(code == 200){
        userInfo = val;
        funCallBack(userInfo)
        // this.transformDate();
      // } else {
      //   this.$message.error(msg)
      // }
    },
    // 华大连接
    HdConnect() {
      servicesend1(this.HdStatus);
    },
    // 华大连接返回值
    HdStatus(type) {
      this.type = type;
      console.error("华大", this.type);
      if (this.type.lastIndexOf("失败") == -1) {
        // this.result = '端口连接失败！'
        userInfo = {}
        this.HdReadID();
      }
    },
    // 华大读卡
    HdReadID() {
      servicesend2(this.HdInfo);
    },
    // 华大读卡数据 TODO:
    async HdInfo(val) {
      // const res = await queryModelSerino({ itemNo: val.readerCode })
      // const { code,msg } = JSON.parse(res.data)
      // console.error('codecodecode',code);
      // if(code == 200){
        userInfo = val;
        funCallBack(userInfo)
        // this.transformDate();
      // } else {
      //   this.$message.error(msg)
      // }
    },
    // 传值
    transformDate() {
      if (JSON.stringify(userInfo) === "{}") {
        // alert("读取失败");
        funCallBack('失败')
      } else {
      }
    },
  },
};