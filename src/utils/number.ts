
export const formatIntNumber = (text: string | undefined | null): number => {
  return text ? parseInt(text) : 0;
};

export function fixedNumber(num: number, idx: number) {
  try {
    if(!num){
      return num
    }
    // 使用toFixed保留两位小数
    let str = Number(num).toFixed(idx);
    // 使用正则表达式移除末尾的0和小数点
    str = str.replace(/0+$/, ''); // 移除尾部的0
    str = str.replace(/\.$/, '');
    return str;
  } catch (error) {
    console.log(error, 'error')
    return num
  }
}


export  function isEven(num:number) {
  // 如果num除以2的余数为0，则是偶数
  return num % 2 === 0;
}