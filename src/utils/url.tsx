export const getUrlParams = (url: string, key?: string) => {
    const paramsRegex = /[?&]+([^=&]+)=([^&]*)/gi;
    const params = {} as any;
    let match;
    while ((match = paramsRegex.exec(url))) {
        params[match[1]] = match[2];
    }
    if (key) {
        return params[key];
    } else {
        return params;
    }
};


export const getUrlPath=()=>{
    return window.location.hash.replace('#/',"").trim();
}

export const fullBase64Url=(url:string)=>{
    return `data:image/gif;base64,${url}`
}