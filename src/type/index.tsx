import { ReactNode } from "react";

export type MODULE_ITEM_TYPE = {
    key: string,
    title: string,
    icon?: string
    singleUrl?:string,
    localUrl?:string,
    zsUrl?:string
}


export function keysToEnum<T extends { [key: string]: unknown }>(arr: T): { [P in keyof T]: P } {
    return arr as { [P in keyof T]: P };
}


export type SearchTableParamsType={
    pageNum:number,
    pageSize:number,
    [key:string]:any
}


export type TableResultType<T> = Partial<
{
    total: number,
    rows: T[]
}>

export type labelExtraRenderColumnItemType={
    title:string,
    text?:string
    dataIndex:string,
    width?:number,
    minWidth?:number, // 拖拽缩放的最小宽度
    isEllipsis?:boolean // 是否显示省略号
    render?:(text:any,record:any,index:number,column?:ColumnItemType,)=>ReactNode,
    [key:string]:any
}
